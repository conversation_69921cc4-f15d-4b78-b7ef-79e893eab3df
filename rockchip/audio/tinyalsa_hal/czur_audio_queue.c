/**
 * Copyright (c) 2013-2020
 * All Rights Reserved by CZUR Technology Co., Ltd and its affiliates.
 * You may not use, copy, distribute, modify, transmit in any form this file
 * except in compliance with CZUR in writing by applicable law.
 */
#define LOG_TAG "audio_hw_czur_queue"

#include<stdlib.h>
#include "czur_audio_queue.h"
#include <cutils/log.h>

/**
 * real capacity of queue if capacity - 1
 */
czur_audio_queue_t* czur_queue_init(void* base, int capacity)
{
    if (NULL == base) {
        return NULL;
    }

    czur_audio_queue_t* queue = (czur_audio_queue_t*) base;
    pthread_mutex_init(&(queue->mutex), NULL);

    queue->capacity = capacity;
    queue->front = 0;
    queue->rear = 0;
    queue->size_of_step = 0;
    queue->capacity_ex = capacity;
    queue->isfull = 0;
    queue->more = true;

    return queue;
}

void czur_queue_destroy(czur_audio_queue_t* queue)
{
    if (NULL != queue) {
        pthread_mutex_destroy(&(queue->mutex));
    }
}

int czur_queue_real_capacity(czur_audio_queue_t* queue)
{
    return queue->capacity - 1;
}

int czur_queue_front(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int front = (queue->front);
    pthread_mutex_unlock(&(queue->mutex));

    return front;
}

int czur_queue_rear(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int rear = (queue->rear);
    pthread_mutex_unlock(&(queue->mutex));

    return rear;
}

void czur_queue_clear(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    queue->front = 0;
    queue->rear = 0;
    pthread_mutex_unlock(&(queue->mutex));
}

void czur_queue_clear_ex(czur_audio_queue_t* queue, int sizeofstep, int index)
{
    pthread_mutex_lock(&(queue->mutex));
    queue->front = 0;
    queue->rear = 0;
    queue->size_of_step = sizeofstep;
    queue->capacity_ex = queue->capacity / sizeofstep * sizeofstep;
    queue->queue_index = index;
    queue->isfull = 0;
    pthread_mutex_unlock(&(queue->mutex));
}

int czur_queue_len(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int len = (queue->rear - queue->front + queue->capacity_ex) % queue->capacity_ex;
    pthread_mutex_unlock(&(queue->mutex));

    return len;
}

int czur_queue_len_asyn(czur_audio_queue_t* queue)
{
    return (queue->rear - queue->front + queue->capacity_ex) % queue->capacity_ex;
}

int czur_queue_left(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int left = queue->capacity_ex - 1 - czur_queue_len_asyn(queue);
    pthread_mutex_unlock(&(queue->mutex));

    return left;
}

int czur_queue_left_asyn(czur_audio_queue_t* queue)
{
    return queue->capacity_ex - 1 - czur_queue_len_asyn(queue);
}

int czur_queue_empty(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int empty = queue->rear == queue->front;
    pthread_mutex_unlock(&(queue->mutex));

    return empty;
}

int czur_queue_full(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int full = ((queue->rear + 1 + queue->capacity_ex) % queue->capacity_ex == queue->front);
    pthread_mutex_unlock(&(queue->mutex));

    return full;
}

int czur_queue_write(czur_audio_queue_t* queue, char data[], int dataLen)
{
    if (queue == NULL || data == NULL || dataLen <= 0) {
        return false;
    }
    pthread_mutex_lock(&(queue->mutex));
    if (czur_queue_left_asyn(queue) < dataLen) {
        pthread_mutex_unlock(&(queue->mutex));
        ALOGD("queue < dataLen ,data too much,space not enough\n");
        return false;
    }
    //calculate begin addr of data filed.
    char* queueBase = (char*)(queue + 1);
    char* begin = &((queueBase)[queue->rear]);
    if (queue->rear + dataLen <= queue->capacity_ex - 1) {
        memcpy(begin, data, dataLen);
    } else {
        //write in two seperate.
        int dataLen1 = queue->capacity_ex - 1 - queue->rear + 1;
        int dataLen2 = dataLen - dataLen1;
        memcpy(begin, data, dataLen1);
        memcpy(queueBase, data + dataLen1, dataLen2);
    }

    queue->rear = (queue->rear + dataLen) % queue->capacity_ex;
    pthread_mutex_unlock(&(queue->mutex));
    return true;
}

char* getWriteBuff(czur_audio_queue_t* queue)
{
    char *buff;
    if (queue == NULL) {
        ALOGD("queue == NULL\n");
        return NULL;
    }
    pthread_mutex_lock(&(queue->mutex));

    // 修复：直接检查可用空间，不使用有问题的 czur_queue_left_asyn
    int queue_len = czur_queue_len_asyn(queue);
    int available_space = queue->capacity_ex - queue_len;  // 不减1！

    if (available_space < queue->size_of_step) {
        pthread_mutex_unlock(&(queue->mutex));
        ALOGD("index:%d queue < size_of_step, rear:%d, front:%d, queue_len:%d, available:%d, need:%d\n",
            queue->queue_index, queue->rear, queue->front, queue_len, available_space, queue->size_of_step);
        return NULL;
    }

    //calculate begin addr of data filed.
    char* queueBase = (char*)(queue + 1);
    char* begin = &((queueBase)[queue->rear]);
    buff = begin;
    pthread_mutex_unlock(&(queue->mutex));
    return buff;
}

void setWriteBuff_pos(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    queue->rear = (queue->rear + queue->size_of_step) % queue->capacity_ex;
    if (queue->rear == queue->front)
        queue->isfull = true;
    pthread_mutex_unlock(&(queue->mutex));
}

int czur_queue_read_ex(czur_audio_queue_t* queue, char **data)
{
    char* queueBase = NULL;
    char* begin = NULL;
    int queueLen = 0;
    if (queue == NULL) {
        return 0;
    }
    pthread_mutex_lock(&(queue->mutex));
    queueBase = (char*)(queue + 1);
    begin = &((queueBase)[queue->front]);
    if (queue->front == queue->rear){
        if (queue->isfull == false){
            ALOGV("index:%d queue_read fail, queueLen < size_of_step \n", queue->queue_index);
            pthread_mutex_unlock(&(queue->mutex));
            return -1;
        }else{
            queue->isfull = false;
        }
    }

    queueLen = queue->size_of_step;
    *data = begin;
    queue->front = (queue->front + queueLen) % queue->capacity_ex;
    pthread_mutex_unlock(&(queue->mutex));

    return queueLen;
}

int czur_queue_read(czur_audio_queue_t* queue, char **data,int dataLen)
{
    char* queueBase = NULL;
    char* begin = NULL;
    int queueLen = 0;
    char *temp_buff = NULL;
    if (queue == NULL || data == NULL) {
        return 0;
    }
    pthread_mutex_lock(&(queue->mutex));
    queueBase = (char*)(queue + 1);
    begin = &((queueBase)[queue->front]);
    queueLen = czur_queue_len_asyn(queue);
 //   ALOGD("#### front :%d  rear:%d capacity:%d \n", queue->front, queue->rear, queue->capacity);
 //   ALOGD("#### queueLen :%d  dataLen:%d \n", queueLen, dataLen);

    if(queueLen < dataLen){
        ALOGD("queue_read fail, queueLen < dataLen \n");
        pthread_mutex_unlock(&(queue->mutex));
        return -1;
    }
    queueLen = dataLen;
    temp_buff = *data;
 //   temp_buff = (char*)malloc(queueLen);
    if (NULL == data){
        ALOGD("queue_read malloc error queueLen%d\n", queueLen);
        return 0;
    }
    if (queue->front + queueLen <= queue->capacity_ex - 1) {
        memcpy(temp_buff, begin, queueLen);
    } else {
        int readLen1 = queue->capacity_ex - 1 - queue->front + 1;
        int readLen2 = queueLen - readLen1;
        memcpy(temp_buff, begin, readLen1);
        memcpy(&(temp_buff[readLen1]), queueBase, readLen2);
    }
 //   *data = temp_buff;
    queue->front = (queue->front + queueLen) % queue->capacity_ex;
    pthread_mutex_unlock(&(queue->mutex));

    return queueLen;
}

void czur_queue_set_more(czur_audio_queue_t* queue, int more)
{
    pthread_mutex_lock(&(queue->mutex));
    queue->more = more;
    pthread_mutex_unlock(&(queue->mutex));
}

int czur_queue_get_more(czur_audio_queue_t* queue)
{
    pthread_mutex_lock(&(queue->mutex));
    int end = queue->more;
    pthread_mutex_unlock(&(queue->mutex));

    return end;
}
