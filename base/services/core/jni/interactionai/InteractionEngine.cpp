#define LOG_TAG "AudioAi.InteractionEngine.cpp"

#include "InteractionEngine.h"
#include <stdio.h>
#include <utils/Log.h>
#include <stdlib.h>
#include <iostream>
#include <memory>
#include <json/json.h>
#include "rapidjson/document.h"
#include "rapidjson/writer.h"
#include "rapidjson/stringbuffer.h"
#include <ctime>
#include <dlfcn.h>
#include "rtc_base/ssl_adapter.h"
#include "PeerConnectionClient.h"
// #include "AudioDataCapture.h"
#include "PackMetaInterFace.h"
#include "MessageParser.h"
#include "MicAmplitude.h"
#include <cutils/properties.h>
#include <inttypes.h>
#include <thread>
#include <chrono>

namespace android::server::interactionai {

std::mutex datamtx;
std::condition_variable data_cond;
std::deque<std::vector<unsigned char>>data_queue;

// AudioDataCapture* g_audioDataCapture = nullptr;
Conductor* g_conductor = nullptr;
KwsInterFace_WakeUpStatus_Func gWakeUpCallback = nullptr;
KwsHandle gKwsHandle = nullptr;
int64_t gTimestamp = 0;

bool isVadWakeUp = false;
bool shouldPushed = false;
bool shouldAmp = false;
size_t PCM_CHUNK_SIZE = 16 * 1024;
bool isDataChannelOpened = false;

OnWakeUpCallback onWakeUpCallback;
OnFirstStageWakeUpCallback onFirstStageWakeUpCallback;
OnStreamStopedCallback onStreamStopedCallback;
OnAudioMetaCallback onAudioMetaCallback;
OnMicAmpCallback onMicAmpCallback;
OnInstructCallback onInstructCallback;
OnStartCallback onStartCallback;
OnChatMsgCallback onChatMsgCallback;
OnTtsCallback onTtsCallback;
OnErrorCallback onErrorCallback;


void InteractionEngine::onLocalDataChannelReady() {
    ALOGD("RegisterOnLocalDataChannelReady success");
    isDataChannelOpened = true;
    onStartCallback("OK");
}
void InteractionEngine::onLocalDataChannelClosed() {
    ALOGD("RegisterOnLocalDataChannelReady Closed");
    isDataChannelOpened = false;
}

void InteractionEngine::OnDataFromDataChannelReady(const uint8_t* data, size_t len){
    ALOGD("WebRtc MainServer success");
    MessageParser::MainServerMessage message = MessageParser::Parse(data, len);
    MessageParser::LogMessage(message);
    if (static_cast<int>(message.msg_type) == 2) {
        ALOGD("WebRtc MainServer message.msg_type = 2");
        if (message.meta.HasMember("result_type") && message.meta["result_type"].IsString()) {
            std::string result_type = message.meta["result_type"].GetString();
            ALOGD("WebRtc MainServer result_type : %s", result_type.c_str());
            if (result_type == "wakeup_succ") {
                shouldPushed = true;
                shouldAmp = true;
                if (onWakeUpCallback) {
                    onWakeUpCallback(MessageParser::GetMetaString(message));
                }
            } else if (result_type == "wakeup_failed") {
                shouldPushed = false;
                shouldAmp = false;
                if (gWakeUpCallback) {
                    gWakeUpCallback(gKwsHandle, 0, 1);
                }
            } else if (result_type == "client_voice_text") {
                onAudioMetaCallback(MessageParser::GetMetaString(message));
            } else if (result_type == "chat_text") {
                onChatMsgCallback(MessageParser::GetMetaString(message));
            } else if (result_type == "instruction") {
                onInstructCallback(MessageParser::GetMetaString(message));
            } else if (result_type == "sentence_ends") {
                shouldPushed = false;
                shouldAmp = false;
                if (gWakeUpCallback) {
                    gWakeUpCallback(gKwsHandle, 0, 1);
                }
            } else if (result_type == "interrupt_operation") {
                if (onStreamStopedCallback) {
                    onStreamStopedCallback(MessageParser::GetMetaString(message));
                }
            }
        }
    } else if (static_cast<int>(message.msg_type) == 0) {
        ALOGD("WebRtc MainServer message.msg_type = 0");
        if (message.meta.HasMember("result_type") && message.meta["result_type"].IsString()) {
            std::string result_type = message.meta["result_type"].GetString();
            ALOGD("WebRtc MainServer result_type : %s", result_type.c_str());
            if (result_type == "chat_voice") {
                if (onTtsCallback) {
                    onTtsCallback(message.raw, MessageParser::GetMetaString(message));
                }
            }
        }
    }
}

void InteractionEngine::OnAudioDataCapture(const char* data, int length){
    ALOGD("OnAudioDataCapture success: %s", data);
    g_conductor->OnAudioDataCapture(data, length);
}

InteractionEngine::InteractionEngine() {
    ALOGD("InteractionEngine()");
    mAudioBuffer = new AudioBuffer(getAudioChunkMsSize());
}

InteractionEngine::~InteractionEngine() {
    delete mAudioBuffer;
    ALOGD("~InteractionEngine()");
}

int InteractionEngine::getAudioChunkMsSize() {
    // 根据采样率和采样时间计算出缓冲区大小 1 channel 16bit
    return (int) (AUDIO_SAMPLE_RATE * 16 / 8 * AUDIO_CHUNK_MS / 1000);
}

int InteractionEngine::init(std::string secret, bool isPrd) {
    ALOGD("InteractionEngine init");
    mSecret = secret;
    mIsPrd = isPrd;
    if (kwsInterface_ptr == nullptr) {
        kwsInterface_ptr = std::make_shared<kws_interface_t>();
        // 打开共享库
        handle = dlopen("libczslkws.so", RTLD_NOW);
        if (!handle) {
            ALOGE("dlopen err: %s",dlerror());
            return EXIT_FAILURE;
        } else {
            ALOGE("InteractionEngine dlopen sucess");
        }
        dlerror();

        // 获取所有函数指针
        kwsInterface_ptr->createF = (KwsInterFace_Create_Func)dlsym(handle, "KwsInterFace_Create");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_Create err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->deleteF = (KwsInterFace_Delete_Func)dlsym(handle, "KwsInterFace_Delete");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_Delete err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->bindF = (KwsInterFace_BindCallback_Func)dlsym(handle, "KwsInterFace_BindCallback");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_BindCallback err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->initF = (KwsInterFace_Init_Func)dlsym(handle, "KwsInterFace_Init");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_Init err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->runkwsF = (KwsInterFace_RunOneStageWakeup_Func)dlsym(handle, "KwsInterFace_RunOneStageWakeup");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_RunOneStageWakeup err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->pushF = (KwsInterFace_PushBuffer_Func)dlsym(handle, "KwsInterFace_PushBuffer");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_PushBuffer err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->wakeUpF = (KwsInterFace_WakeUpStatus_Func)dlsym(handle, "KwsInterFace_WakeUpStatus");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_WakeUpStatus err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }
        gWakeUpCallback = kwsInterface_ptr->wakeUpF;

        kwsInterface_ptr->stopF = (KwsInterFace_SetStop_Func)dlsym(handle, "KwsInterFace_SetStop");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_SetStop err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kwsInterface_ptr->stageOneF = (KwsInterFace_OpenStageOne_Func)dlsym(handle, "KwsInterFace_OpenStageOne");
        if ((error = dlerror()) != NULL)  {
            ALOGE("InteractionEngine KwsInterFace_OpenStageOne err: %s",error);
            dlclose(handle);
            return EXIT_FAILURE;
        }

        kws_handle = kwsInterface_ptr->createF();
        gKwsHandle = kws_handle;
    }

    if (mKeepKwsRunThread == nullptr) {
        mKeepKwsRunThread = new KeepKwsRunThread(this);
    }
    mKeepKwsRunThread->run("KeepKwsRunThread", PRIORITY_NORMAL);

    if (mWakeUpTaskThread == nullptr) {
        mWakeUpTaskThread = new WakeUpTaskThread(this);
    }
    mWakeUpTaskThread->run("WakeUpTaskThread", PRIORITY_NORMAL);

    if (mPushStreamThread == nullptr) {
        mPushStreamThread = new PushStreamThread(this);
    }
    mPushStreamThread->run("PushStreamThread", PRIORITY_NORMAL);

    return 0;
}

void InteractionEngine::rtcStandby(std::string secret, bool isPrd) {
    ALOGD("InteractionEngine::rtcStandby");
    rtc::InitializeSSL();
    // 创建WebRTC线程
    mCurrent = rtc::Thread::Current();
    if (!mCurrent) {
        mThread = rtc::Thread::Create();
        mThread->Start();
        mCurrent = mThread.get();
    }
    
    char prop_value[PROPERTY_VALUE_MAX];
    if (property_get("persist.vendor.czur.ai.interaction.tts.status", prop_value, "1") > 0) {
        mTtsStatus = atoi(prop_value);
        ALOGD("InteractionEngine::rtcStandby loaded tts status from prop: %d", mTtsStatus);
    }

    mConductor = rtc::make_ref_counted<Conductor>(secret, isPrd, mTtsStatus);
    g_conductor = mConductor.get();
    // mConductor->SetSecret(secret);
    // 注册本地数据通道的回调函数， lambda表达式
    mConductor->RegisterOnLocalDataChannelReady(onLocalDataChannelReady);
    mConductor->RegisterOnLocalDataChannelClosed(onLocalDataChannelClosed);
    mConductor->RegisterOnDataFromDataChannelReady(OnDataFromDataChannelReady);

    mConductor->ConnectToPeer(1);
    mCurrent->ProcessMessages(100);
}

int InteractionEngine::unInit() {
    if (kwsInterface_ptr) {
        ALOGD("InteractionEngine ShouldDeInit");
        kwsInterface_ptr->stopF(kws_handle, 0);
        kwsInterface_ptr->deleteF(kws_handle);
        kwsInterface_ptr.reset();
        kwsInterface_ptr = nullptr;
        dlclose(handle);
        handle = nullptr;
        mIsInited = false;
    }
    rtc::CleanupSSL();
    ALOGD("InteractionEngine Clean exit completed");
    return 0;
}

int InteractionEngine::start() {
    while (mStopping) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    mStarting = true;
    rtcStandby(mSecret, mIsPrd);
    mStarting = false;
    return 0;
}
int InteractionEngine::stop() {
    while (mStarting) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    mStopping = true;
    // Cleanup WebRTC
    if (mConductor) {
        mConductor->Disconnect();
    }
    if (mThread) {
        mThread->Stop();
    }
    mStopping = false;
    return 0;
}
int InteractionEngine::setPlayStatus(int status) {
    ALOGD("InteractionEngine::setPlayStatus : %d", status);
    mPlayStatus = status;
    return 0;
}

int InteractionEngine::setTtsStatus(int status) {
    ALOGD("InteractionEngine::setTtsStatus : %d", status);
    mTtsStatus = status;
    updateServerTtsStatus();
    return 0;
}

void InteractionEngine::updateServerTtsStatus() {
    std::vector<uint8_t> packedData;

    packedData.push_back(static_cast<uint8_t>(2));

    rapidjson::Document doc;
    doc.SetObject();
    rapidjson::Document::AllocatorType& allocator = doc.GetAllocator();
    doc.AddMember("update_tts_config", mTtsStatus, allocator);

    rapidjson::StringBuffer buffer_str;
    rapidjson::Writer<rapidjson::StringBuffer> writer(buffer_str);
    doc.Accept(writer);
    std::string ParamJsonStr = buffer_str.GetString();
    ALOGD("InteractionEngine updateServerTtsStatus ParamJsonStr : %s", ParamJsonStr.c_str());

    uint32_t metaLength = static_cast<uint32_t>(ParamJsonStr.size());
    packedData.push_back(static_cast<uint8_t>((metaLength >> 24) & 0xFF));
    packedData.push_back(static_cast<uint8_t>((metaLength >> 16) & 0xFF));
    packedData.push_back(static_cast<uint8_t>((metaLength >> 8) & 0xFF));
    packedData.push_back(static_cast<uint8_t>(metaLength & 0xFF));

    packedData.insert(packedData.end(), ParamJsonStr.begin(), ParamJsonStr.end());

    mConductor->OnAudioDataCapture(reinterpret_cast<const char*>(packedData.data()), 5 + ParamJsonStr.size());
}

int InteractionEngine::trigger(bool trigger) {
    ALOGD("InteractionEngine::trigger : %d", trigger);
    if (gWakeUpCallback) {
        if (trigger) {
            gWakeUpCallback(gKwsHandle, 1, 2);
        } else {
            gWakeUpCallback(gKwsHandle, 0, 1);
        }
    }
    // isVadWakeUp = trigger;
    shouldPushed = trigger;
    shouldAmp = trigger;
    return 0;
}

int InteractionEngine::openStageOne(int openflag) {
    ALOGD("InteractionEngine::openStageOne : %d", openflag);
    if (kwsInterface_ptr) {
        kwsInterface_ptr->stageOneF(kws_handle, openflag);
        // // 将openflag记录到系统属性中
        // char prop_value[PROPERTY_VALUE_MAX];
        // snprintf(prop_value, sizeof(prop_value), "%d", openflag);
        // property_set("persist.vendor.czur.ai.interaction.stage.status", prop_value);
    }
    return 0;
}

void InteractionEngine::setOnWakeUpCallback(OnWakeUpCallback callback) {
    onWakeUpCallback = callback;
}

void InteractionEngine::setOnFirstStageWakeUpCallback(OnFirstStageWakeUpCallback callback) {
    onFirstStageWakeUpCallback = callback;
}

void InteractionEngine::setOnStreamStopedCallback(OnStreamStopedCallback callback) {
    onStreamStopedCallback = callback;
}

void InteractionEngine::setOnAudioMetaCallback(OnAudioMetaCallback callback) {
    onAudioMetaCallback = callback;
}
void InteractionEngine::setOnMicAmpCallback(OnMicAmpCallback callback) {
    onMicAmpCallback = callback;
}

void InteractionEngine::setOnInstructCallback(OnInstructCallback callback) {
    onInstructCallback = callback;
}

void InteractionEngine::setOnStartCallback(OnStartCallback callback) {
    onStartCallback = callback;
}

void InteractionEngine::setOnChatMsgCallback(OnChatMsgCallback callback) {
    onChatMsgCallback = callback;
}

void InteractionEngine::setOnTtsCallback(OnTtsCallback callback) {
    onTtsCallback = callback;
}

void InteractionEngine::setOnErrorCallback(OnErrorCallback callback) {
    onErrorCallback = callback;
}

int InteractionEngine::test1() {
    ALOGD("InteractionEngine::test1");
    mConductor->OnAudioDataCapture("0400000000", 16);
    return 0;
}
int InteractionEngine::test2() {
    ALOGD("InteractionEngine::test2");
    // onWakeUpCallback("onWakeUpCallback");
    // onAudioMetaCallback("onAudioMetaCallback");
    // onInstructCallback("onInstructCallback");
    // onChatMsgCallback("onChatMsgCallback");
    onFirstStageWakeUpCallback("onFirstStageWakeUpCallback");
    return 0;
}
int InteractionEngine::test3() {
    ALOGD("InteractionEngine::test3");
    isVadWakeUp = false;
    shouldPushed = false;
    return 0;
}
int InteractionEngine::test4() {
    ALOGD("InteractionEngine::test4");
    onStreamStopedCallback("stop");
    return 0;
}

int InteractionEngine::interaction(const char *src, int start, int length) {
    // 创建数据副本，避免修改原始数据
    unsigned char *dataCopy = new unsigned char[length];
    memcpy(dataCopy, src + start, length);
    
    if (kwsInterface_ptr == nullptr || kwsInterface_ptr->pushF == nullptr) {
        ALOGD("InteractionEngine::interaction can't push");
        return 0;
    }
    // 使用数据副本而不是原始数据
    if (kwsInterface_ptr->pushF(kws_handle, dataCopy, length) != 0) {
        fprintf(stderr, "InteractionEngine Failed to push buffer\n");
    }
    ALOGD("InteractionEngine::interaction stream is pushed");
    
    // 继续使用原始数据进行其他操作
    mAudioBuffer->write(src + start, length);
    calculateMicAmplitude(src + start, length);
    
    // 释放数据副本
    delete[] dataCopy;
    return 0;
}

void InteractionEngine::calculateMicAmplitude(const char *src, int length) {
    if (shouldAmp) {
        double rms = calculateAmplitude(src, length);

        rapidjson::Document doc;
        doc.SetObject();
        rapidjson::Document::AllocatorType& allocator = doc.GetAllocator();
        doc.AddMember("audioAmp", rms, allocator);
        rapidjson::StringBuffer buffer_str;
        rapidjson::Writer<rapidjson::StringBuffer> writer(buffer_str);
        doc.Accept(writer);
        std::string metaJson = buffer_str.GetString();
        ALOGD("InteractionEngine calculateMicAmplitude : %s", metaJson.c_str());

        if (onMicAmpCallback) {
            onMicAmpCallback(metaJson);
        }
    }
}

InteractionEngine::KeepKwsRunThread::KeepKwsRunThread(const sp<InteractionEngine>& engine)
    : mEngine(engine) {
    ALOGD("KeepKwsRunThread construct");
}
InteractionEngine::KeepKwsRunThread::~KeepKwsRunThread() {
    ALOGD("~KeepKwsRunThread");
}
bool InteractionEngine::KeepKwsRunThread::threadLoop() {
    ALOGD("KeepKwsRunThread threadLoop");
    if (mEngine->mIsInited) {
        ALOGD("KeepKwsRunThread threadLoop isInited");
        return false;
    }
    if (mEngine->kws_handle) {
        // int status;
        // 绑定回调函数（如果需要）
        if (mEngine->kwsInterface_ptr->bindF(mEngine->kws_handle, first_stage_callback, on_data_callback) != 0) {
            ALOGE("KeepKwsRunThread bindF err");
        }

        // 初始化
        if (mEngine->kwsInterface_ptr->initF(mEngine->kws_handle, "/vendor/etc/czslkws_rc/") != 0) {
            ALOGE("KeepKwsRunThread initF err");
        }

        // 运行唤醒阶段
        if (mEngine->kwsInterface_ptr->runkwsF(mEngine->kws_handle) != 0) {
            ALOGE("KeepKwsRunThread runkwsF err");
        }

        // 从系统属性中读取之前保存的openflag值
        char prop_value[PROPERTY_VALUE_MAX];
        int status = 0;
        if (property_get("persist.vendor.czur.ai.interaction.stage.status", prop_value, "0") > 0) {
            status = atoi(prop_value);
            ALOGD("KeepKwsRunThread loaded stage status from prop: %d", status);
        }
        mEngine->mIsInited = true;
        mEngine->kwsInterface_ptr->stageOneF(mEngine->kws_handle, status);
    } else {
        ALOGE("InteractionEngine threadLoop init failed");
        return -1;
    }
    return false;
}

InteractionEngine::WakeUpTaskThread::WakeUpTaskThread(const sp<InteractionEngine>& engine)
    : mEngine(engine) {
    ALOGD("WakeUpTaskThread construct");
}
InteractionEngine::WakeUpTaskThread::~WakeUpTaskThread() {
    ALOGD("~WakeUpTaskThread");
}
bool InteractionEngine::WakeUpTaskThread::threadLoop() {
    ALOGD("WakeUpTaskThread threadLoop isVadWakeUp : %d, isDataChannelOpened : %d", isVadWakeUp, isDataChannelOpened);
    if (isVadWakeUp && isDataChannelOpened) {
        isVadWakeUp = false;
        int64_t timestamp = gTimestamp;

        std::vector<char> bufferData = mEngine->mAudioBuffer->readAll();
        // mEngine->mAudioBuffer->writePcmFile("/sdcard/output.pcm", bufferData);

        unsigned char* pcmData = reinterpret_cast<unsigned char*>(bufferData.data());

        czsl_kws::PackMeta pkm;

        std::string uuid = mEngine->mAudioBuffer->generateUuid();

        int currentPlayStatus = mEngine->mPlayStatus;

        size_t numChunks = (bufferData.size() + PCM_CHUNK_SIZE - 1) / PCM_CHUNK_SIZE; // 计算总块数
        for (size_t i = 0; i < numChunks; ++i) {
            // 计算当前块的起始位置和大小
            size_t offset = i * PCM_CHUNK_SIZE;
            size_t chunkSize = std::min(PCM_CHUNK_SIZE, bufferData.size() - offset);

            rapidjson::Document doc;
            doc.SetObject();
            rapidjson::Document::AllocatorType& allocator = doc.GetAllocator();
            doc.AddMember("audio_type", 0, allocator);
            doc.AddMember("total_chunks", numChunks, allocator);
            doc.AddMember("current_chunk", i, allocator);
            doc.AddMember("timestamp", timestamp, allocator);
            doc.AddMember("is_talking", currentPlayStatus, allocator);

            rapidjson::Value cid;
            cid.SetString(uuid.c_str(), uuid.size(), allocator);
            doc.AddMember("chunk_id", cid, allocator);

            rapidjson::StringBuffer buffer_str;
            rapidjson::Writer<rapidjson::StringBuffer> writer(buffer_str);
            doc.Accept(writer);
            std::string ParamJsonStr = buffer_str.GetString();
            ALOGD("InteractionEngine first_stage_callback ParamJsonStr : %s", ParamJsonStr.c_str());

            unsigned char* pack_data;
            // 封装pcm数据
            pkm.pack_pcm(0, pcmData + offset, chunkSize, ParamJsonStr.size(), ParamJsonStr.data(), pack_data);
            mEngine->mConductor->OnAudioDataCapture(reinterpret_cast<const char*>(pack_data), chunkSize + 5 + ParamJsonStr.size());
            delete[] pack_data;
        }
    }
    sleep(1);
    return true;
}

InteractionEngine::PushStreamThread::PushStreamThread(const sp<InteractionEngine>& engine)
    : mEngine(engine) {
    ALOGD("PushStreamThread construct");
}
InteractionEngine::PushStreamThread::~PushStreamThread() {
    ALOGD("~PushStreamThread");
}
bool InteractionEngine::PushStreamThread::threadLoop() {
    if (shouldPushed && isDataChannelOpened) {
        std::vector<unsigned char> data_vec;
        mEngine->get_data(data_vec);
        if (!data_vec.empty()) {
            unsigned char* pack_data = data_vec.data();
            if (pack_data != nullptr) {
                int32_t json_size_be;
                memcpy(&json_size_be, pack_data + 1, sizeof(int32_t));
                int32_t json_size = ntohl(json_size_be); // 转换为本地字节序
                ALOGD("json_size: %d", json_size);
                std::string json_data = std::string(pack_data + 5, pack_data + 5 + json_size);
                ALOGD("json_data: %s", json_data.c_str());
                mEngine->mConductor->OnAudioDataCapture(reinterpret_cast<const char*>(pack_data), data_vec.size());
            }
        }
    } else {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    return true;
} 

void InteractionEngine::first_stage_callback(int64_t timestamp, float prob_conf) {
    ALOGD("InteractionEngine first_stage_callback 一阶段唤醒. timestamp : %" PRId64 "", timestamp);
    ALOGD("InteractionEngine first_stage_callback 一阶段唤醒. prob_conf : %f", prob_conf);
    if (onFirstStageWakeUpCallback && prob_conf >= 0.9) {
        onFirstStageWakeUpCallback(std::to_string(timestamp));
        gTimestamp = timestamp;
        isVadWakeUp = true;
        shouldPushed = true;
    }
}

void InteractionEngine::on_data_callback(unsigned char* pack_ptr, int32_t len) {
    ALOGD("InteractionEngine on_data_callback 数据回调");
    if (!shouldPushed) {
        return;
    }
    {
        std::unique_lock<std::mutex> lockkwsmtx(datamtx);
        std::vector<unsigned char> vec(pack_ptr, pack_ptr + len);
        data_queue.push_back(vec);
    }
    data_cond.notify_one();
}

void InteractionEngine::get_data(std::vector<unsigned char>& data)
{
    std::unique_lock<std::mutex> lockkwsmtx(datamtx);

    if (data_queue.empty()) {
        // 等待最多100毫秒，如果没有数据到达，也会返回
        data_cond.wait_for(lockkwsmtx, std::chrono::milliseconds(100), []{ return !data_queue.empty(); });
    }
    if (!data_queue.empty()) {
        data = data_queue.front();
        data_queue.pop_front();
    } else {
        // 确保返回空向量
        data.clear();
    }
}

int InteractionEngine::getStageStatus() {
    char prop_value[PROPERTY_VALUE_MAX];
    int status = 0;
    
    // 从系统属性中读取stage status，如果不存在则返回0
    if (property_get("persist.vendor.czur.ai.interaction.stage.status", prop_value, "") > 0) {
        status = atoi(prop_value);
        ALOGD("InteractionEngine::getStageStatus: %d", status);
    } else {
        ALOGD("InteractionEngine::getStageStatus: property not found, returning 0");
    }
    
    return status;
}

}

