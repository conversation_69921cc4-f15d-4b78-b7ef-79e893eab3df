#ifndef SDK_CODE_RK3576_VOICEINTERACTIONENGINE_H
#define SDK_CODE_RK3576_VOICEINTERACTIONENGINE_H

#include <utils/RefBase.h>
#include "rtc_base/thread.h"
#include <utils/threads.h>
#include "Conductor.h"
#include "AudioBuffer.h"
// #include "KwsInterFace.h"
#include "KwsInterFaceCapi.h"

// using namespace czsl_kws;

namespace android::server::interactionai {

typedef void (*OnWakeUpCallback)(const std::string meta);
typedef void (*OnFirstStageWakeUpCallback)(const std::string meta);
typedef void (*OnStreamStopedCallback)(const std::string meta);
typedef void (*OnAudioMetaCallback)(const std::string metaJson);
typedef void (*OnMicAmpCallback)(const std::string json);
typedef void (*OnInstructCallback)(const std::string instructJson);
typedef void (*OnStartCallback)(const std::string flag);
typedef void (*OnChatMsgCallback)(const std::string msgJson);
typedef void (*OnTtsCallback)(const std::vector<uint8_t> audioData, const std::string meta);
typedef void (*OnErrorCallback)(int errorCode, const std::string errorMsg);

typedef KwsHandle (*KwsInterFace_Create_Func)();
typedef void (*KwsInterFace_Delete_Func)(KwsHandle handle);
typedef int (*KwsInterFace_BindCallback_Func)(KwsHandle handle, TriggerFirstStageCallback first_stage, PostWakeupCallback post_wakeup);
typedef int (*KwsInterFace_Init_Func)(KwsHandle handle, const char* resource_dir);
typedef int (*KwsInterFace_RunOneStageWakeup_Func)(KwsHandle handle);
typedef int (*KwsInterFace_PushBuffer_Func)(KwsHandle handle, unsigned char* pcm_data, size_t len);
typedef int (*KwsInterFace_WakeUpStatus_Func)(KwsHandle handle, int flag, int wakeup_mode);
typedef void (*KwsInterFace_SetStop_Func)(KwsHandle handle, int stop);
typedef void (*KwsInterFace_OpenStageOne_Func)(KwsHandle handle, int openflag);

typedef struct kws_interface {
    KwsInterFace_Create_Func createF;
    KwsInterFace_Delete_Func deleteF;
    KwsInterFace_BindCallback_Func bindF;
    KwsInterFace_Init_Func initF;
    KwsInterFace_RunOneStageWakeup_Func runkwsF;
    KwsInterFace_PushBuffer_Func pushF;
    KwsInterFace_WakeUpStatus_Func wakeUpF;
    KwsInterFace_SetStop_Func stopF;
    KwsInterFace_OpenStageOne_Func stageOneF;
} kws_interface_t;

class InteractionEngine : public RefBase {
public:
    InteractionEngine();
    ~InteractionEngine();

    int init(std::string secret, bool isPrd);
    int unInit();
    int start();
    int stop();
    int trigger(bool trigger);
    int setPlayStatus(int status);
    int setTtsStatus(int status);

    int openStageOne(int openflag);

    int test1();
    int test2();
    int test3();
    int test4();

    int interaction(const char *src, int start, int length);

    static void onLocalDataChannelReady();
    static void onLocalDataChannelClosed();
    static void OnDataFromDataChannelReady(const uint8_t* data, size_t len);
    static void OnAudioDataCapture(const char* data, int length);

    void setOnWakeUpCallback(OnWakeUpCallback callback);
    void setOnFirstStageWakeUpCallback(OnFirstStageWakeUpCallback callback);
    void setOnStreamStopedCallback(OnStreamStopedCallback callback);
    void setOnAudioMetaCallback(OnAudioMetaCallback callback);
    void setOnMicAmpCallback(OnMicAmpCallback callback);
    void setOnInstructCallback(OnInstructCallback callback);
    void setOnStartCallback(OnStartCallback callback);
    void setOnChatMsgCallback(OnChatMsgCallback callback);
    void setOnTtsCallback(OnTtsCallback callback);
    void setOnErrorCallback(OnErrorCallback callback);

    // 新增方法：获取当前的stage status
    int getStageStatus();

private:
    std::shared_ptr<kws_interface_t> kwsInterface_ptr = nullptr;
    KwsHandle kws_handle;
    void *handle;
    char *error;
    static void first_stage_callback(int64_t timestamp, float prob_conf);
    static void on_data_callback(unsigned char* pack_ptr, int32_t len);
    void get_data(std::vector<unsigned char>& data);

    class KeepKwsRunThread : public Thread {
        public:
            KeepKwsRunThread(const sp<InteractionEngine>& engine);
            virtual ~KeepKwsRunThread();
            virtual bool threadLoop();

        private:
            sp<InteractionEngine> mEngine;
    };
    sp<KeepKwsRunThread> mKeepKwsRunThread;

    class WakeUpTaskThread : public Thread {
        public:
            WakeUpTaskThread(const sp<InteractionEngine>& engine);
            virtual ~WakeUpTaskThread();
            virtual bool threadLoop();

        private:
            sp<InteractionEngine> mEngine;
    };
    sp<WakeUpTaskThread> mWakeUpTaskThread;

    class PushStreamThread : public Thread {
        public:
            PushStreamThread(const sp<InteractionEngine>& engine);
            virtual ~PushStreamThread();
            virtual bool threadLoop();

        private:
            sp<InteractionEngine> mEngine;
    };
    sp<PushStreamThread> mPushStreamThread;

    std::unique_ptr<rtc::Thread> mThread;
    rtc::Thread* mCurrent;
    rtc::scoped_refptr<Conductor> mConductor;
    void rtcStandby(std::string secret, bool isPrd);
    AudioBuffer *mAudioBuffer;
    int getAudioChunkMsSize();
    int AUDIO_SAMPLE_RATE = 16000;
    int AUDIO_CHUNK_MS = 2000;
    std::string mSecret;
    // bool isRtcSuccessed = false;

    int mTtsStatus = 1;
    int mPlayStatus = 0;
    bool mIsPrd = false;
    bool mIsInited = false;
    bool mStarting = false;
    bool mStopping = false;

    void calculateMicAmplitude(const char *src, int length);
    void updateServerTtsStatus();
};

}

#endif // !SDK_CODE_RK3576_VOICEINTERACTIONENGINE_H
