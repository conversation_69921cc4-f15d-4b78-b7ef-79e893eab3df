package com.android.server.audioai.aitrigger;

import static com.android.server.audioai.aitrigger.AudioRecorder.TriggerAction.ACTION_OTHER;
import static com.android.server.audioai.aitrigger.AudioRecorder.TriggerAction.ACTION_SELF;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.media.AudioManager;
import android.os.SystemProperties;
import android.audioai.IAudioAiService;
import android.audioai.IAudioAiServiceCallback;
import android.audioai.IInteractionAiServiceCallback;
import com.android.server.SystemService;
import com.android.server.SystemServerInitThreadPool;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import android.util.Slog;
import com.android.server.audioai.aitrigger.AudioRecorder.RecorderHandler;

import android.os.Handler;
import android.os.Looper;

import rockchip.hardware.hdmi.V1_0.IHdmi;
import rockchip.hardware.hdmi.V1_0.IHdmiCallback;
import android.os.RemoteException;

import com.android.server.audioai.translate.Translate;
import com.android.server.audioai.interaction.Interaction;

public class AiTriggerEngine extends IAudioAiService.Stub {
    private static final String TAG = "AudioAI.AiTriggerEngine";
    private static final boolean DEBUG = true;
    private static final boolean SYSTEM_TRIGGER_SELF = false;

    private final Context mContext;

    private Handler mHandler = new Handler(Looper.getMainLooper());

    private AudioRecorder mRecorder;
    private Translate mTranslate;
    private Interaction mInteraction;

    boolean mIsEnable = false;
    boolean mMicIsRecording = false;

    private int asrMode = 1; // 1:local, 2:local_remote, 3:remote

    private IAudioAiServiceCallback mCallback;

    private IHdmi mHdmiService;

    public static class Lifecycle extends SystemService {
        private AiTriggerEngine mTriggerEngine;

        private final CompletableFuture<Void> mOnStartFinished = new CompletableFuture<>();

        private final CompletableFuture<Void> mOnActivityManagerPhaseFinished =
                new CompletableFuture<>();

        public Lifecycle(Context context) {
            super(context);
        }

        @Override
        public void onStart() {
            SystemServerInitThreadPool.submit(() -> {
                mTriggerEngine = new AiTriggerEngine(getContext());
                publishBinderService(Context.AUDIOAI_SERVICE, mTriggerEngine);
                mOnStartFinished.complete(null);
            }, "AiTriggerEngine$Lifecycle#onStart");
        }

        @Override
        public void onBootPhase(int phase) {
            if (phase == SystemService.PHASE_ACTIVITY_MANAGER_READY) {
                SystemServerInitThreadPool.submit(() -> {
                    mOnStartFinished.join();
                    mTriggerEngine.systemReady();
                    mOnActivityManagerPhaseFinished.complete(null);
                }, "AudioAiTriggerEngine$Lifecycle#onBootPhase");
            } else if (phase == SystemService.PHASE_BOOT_COMPLETED) {
                mOnActivityManagerPhaseFinished.join();
                mTriggerEngine.bootCompleted();
            }
        }

    }

    private final BroadcastReceiver mBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            Slog.d(TAG, "onReceive:" + intent.getAction());
        }
    };

    public AiTriggerEngine(Context context) {
        mContext = context;
        Slog.i(TAG, "AiTriggerEngine init");
        mTranslate = new Translate(context);
        initHdmiService();
    }

    private boolean isInteractionAiEnabled() {
        return SystemProperties.getBoolean("persist.vendor.czur.ai.interaction.enabled", true);
    }

    public void handleInteractionMethod() {
        mInteraction.onWakeUpCallback(null);
        mInteraction.onFirstStageWakeUpCallback(null);
        mInteraction.onStreamStopedCallback(null);
        mInteraction.onAudioMetaCallback(null);
        mInteraction.onInstructCallback(null);
        mInteraction.onChatMsgCallback(null);
        mInteraction.onTtSCallback(null, null);
        mInteraction.onErrorCallback(-100, null);
        mInteraction.onMicAmpCallback(null);
        mInteraction.onStartCallback(null);
    }

    public void systemReady() {
        Slog.i(TAG, "systemReady");
        final IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_ON);
        filter.addAction(Intent.ACTION_SCREEN_OFF);
        filter.addAction(AudioManager.ACTION_MICROPHONE_MUTE_CHANGED);
        filter.setPriority(IntentFilter.SYSTEM_HIGH_PRIORITY);
        mContext.registerReceiver(mBroadcastReceiver, filter);

    }

    public void bootCompleted() {
        Slog.i(TAG, "bootCompleted");
        mRecorder = AudioRecorder.getInstance();
        mRecorder.createDefaultAudio();
        mRecorder.setTranslateListener(mTranslate);
        
        // 启动录音
        boolean boot = SystemProperties.getBoolean("persist.vendor.czur.ai.autoboot", false);
        if (boot) {
            Slog.i(TAG, "bootCompleted autoboot");
            mRecorder.startRecord();
        }
    }

    private boolean isSystemTriggerEnable() {
        boolean enable = SystemProperties.getBoolean("persist.vendor.czur.ai.manual", false);
        return SYSTEM_TRIGGER_SELF || enable;
    }

    @Override
    public int asrSdkInit(String userId, boolean isPrd) {
        Slog.i(TAG, "asrSdkInit:" + userId + ", isPrd : " + isPrd);
        mTranslate.init(userId, isPrd);
        return 0;
    }

    @Override
    public int asrSdkUnInit() {
        mTranslate.deInit();
        return 0;
    }

    @Override
    public int setAsrMode(int mode) {
        Slog.i(TAG, "setAsrMode:" + mode);
        // 当前只支持本地模式
        asrMode = mode;
        return 0;
    }

    @Override
    public String getSessionId() {
        Slog.i(TAG, "getSessionId");
        return mTranslate.getSessionId();
    }

    @Override
    public List<String> getAsrLangs() {
        Slog.i(TAG, "getAsrLangs");
        return mTranslate.getSpAsrLang();
    }

    @Override
    public List<String> getTranslateOrigLangs() {
        Slog.i(TAG, "getTranslateOrigLangs");
        return mTranslate.getSpOrigLang();
    }

    @Override
    public List<String> getTranslateDestLangs() {
        Slog.i(TAG, "getTranslateDestLangs");
        return mTranslate.getSpTargetLang();
    }

    @Override
    public int setAsrLang(String lang) {
        Slog.i(TAG, "setAsrLang:" + lang);
        mTranslate.setSpAsrLang(lang);
        return 0;
    }

    @Override
    public int setTranslateLang(String originalLang, String destLang) {
        Slog.i(TAG, "setTranslateLang:" + originalLang + " <=> " + destLang);
        mTranslate.setSpTranslateLang(originalLang, destLang);
        return 0;
    }

    @Override
    public int asrStart() {
        Slog.i(TAG, "asrStart");
        if (isHdmiUsed()) {
            return 1;
        }
        mTranslate.startAsr();
        mIsEnable = true;
        mRecorder.startAi();
        return 0;
    }

    @Override
    public int asrStop() {
        Slog.i(TAG, "asrStop");
        mIsEnable = false;
        mRecorder.stopAi();
        return 0;
    }

    @Override
    public int getAsrEnabled() {
        return mIsEnable ? 1 : 0;
    }

    @Override
    public int setTranslateEnabled(boolean enable) {
        Slog.i(TAG, "setTranslateEnable:" + enable);
        mTranslate.setTranslateEnable(enable);
        return 0;
    }

    @Override
    public int setSummaryEnabled(boolean enable) {
        Slog.i(TAG, "setSummaryEnable:" + enable);
        mTranslate.setSummaryEnable(enable);
        return 0;
    }

    @Override
    public void setServiceCallback(IAudioAiServiceCallback callback) {
        mCallback = callback;
        mTranslate.setCallback(callback);
    }

    @Override
    public void registerCallback(IInteractionAiServiceCallback callback) {
        Slog.i(TAG, "registerCallback : " + callback);
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            mInteraction.setCallback(callback);
        }
    }

    @Override
    public void unregisterCallback(IInteractionAiServiceCallback callback) {
        Slog.i(TAG, "unregisterCallback : " + callback);
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            mInteraction.setCallback(null);
        }
    }

    @Override
    public void interactionMicTrigger(int micStatus) {
        Slog.i(TAG, "===AudioRecord===interactionMicTrigger:" + micStatus);
        mRecorder.mMicStatus = micStatus;
        if (isHdmiUsed()) {
            return;
        }
        /*mRecorder.changeAiStream(micStatus);*/
        /**
         * 第三方应用触发
         *      --> 开启
         *          --> AI字幕开启中 : 将数据流更改为三方
         *          --> AI字幕未开启 : 关闭小星
         *      --> 关闭
         *          --> AI字幕开启中 : 将数据流更改为自有
         *          --> AI字幕未开启 : 打开小星
         */
        mHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                handleMic(micStatus);
            }
        }, 1000);
    }

    private void handleMic(int micStatus) {
        if (micStatus == 1) {
            if (mIsEnable) {
                mRecorder.changeAiStream(micStatus);
                mRecorder.mKwsStarted = false;
            } else {
                boolean enabled = isInteractionAiEnabled();
                if (enabled) {
                    mRecorder.stopInteraction();
                }
            }
        } else {
            if (mIsEnable) {
                mRecorder.changeAiStream(micStatus);
                mRecorder.mKwsStarted = true;
            } else {
                boolean enabled = isInteractionAiEnabled();
                if (enabled) {
                    mRecorder.startInteraction();
                }
            }
        }
    }

    // 添加 HDMI 服务初始化方法
    private void initHdmiService() {
        try {
            Slog.d(TAG, "Initializing HDMI service");
            mHdmiService = IHdmi.getService(true /* retry */);
            if (mHdmiService != null) {
                Slog.d(TAG, "HDMI service connected");
                registerHdmiCallback();
            } else {
                Slog.e(TAG, "Failed to get HDMI service");
            }
        } catch (RemoteException e) {
            Slog.e(TAG, "Failed to initialize HDMI service", e);
        }
    }
    
    // 添加 HDMI 回调注册方法
    private void registerHdmiCallback() {
        if (mHdmiService == null) {
            Slog.e(TAG, "Cannot register callback, HDMI service is null");
            return;
        }
        
        try {
            IHdmiCallback callback = new IHdmiCallback.Stub() {
                @Override
                public void onConnect(String cameraId) {
                    Slog.d(TAG, "===AudioRecord===HDMI connected: " + cameraId);
                    SystemProperties.set("persist.vendor.czur.ai.interaction.hdmi.status", "1");
                    interactionStop();
                }
                
                @Override
                public void onFormatChange(String cameraId, int width, int height) {
                    Slog.d(TAG, "===AudioRecord===HDMI format changed: " + width + "x" + height);
                }
                
                @Override
                public void onDisconnect(String cameraId) {
                    Slog.d(TAG, "===AudioRecord===HDMI disconnected: " + cameraId);
                    SystemProperties.set("persist.vendor.czur.ai.interaction.hdmi.status", "0");
                    interactionStart();
                }
            };
            
            mHdmiService.registerListener("AudioAI", callback);
        } catch (RemoteException e) {
            Slog.e(TAG, "Failed to register HDMI callback", e);
        }
    }

    @Override
    public void micStatusChanged(int micStatus) {
        Slog.i(TAG, "micStatusChanged:" + micStatus);
        mMicIsRecording = micStatus == 1;
    }

    @Override
    public void copyMicData(byte[] data, int size) {
        mRecorder.saveMicData(data, size);
    }

    @Override
    public void setAudioFormat(int sampleRate, int format, int channelMask) {
        mRecorder.setAudioFormat(sampleRate, format, channelMask);
    }

    @Override
    public void interactionInit(String secret, boolean isPrd) {
        Slog.i(TAG, "interactionInit");
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            mInteraction = new Interaction(mContext);
            handleInteractionMethod();
            mInteraction.interactionInit(secret, isPrd);
            mInteraction.setRecoder(mRecorder);
            mRecorder.setInteractionListener(mInteraction);
        }
    }

    @Override
    public void interactionUnInit() {
        Slog.i(TAG, "interactionUnInit");
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            if (mInteraction == null) {
                return;
            }
            mInteraction.interactionUnInit();
        }
    }

    @Override
    public void interactionStart() {
        Slog.i(TAG, "interactionStart");
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            if (mRecorder != null && mInteraction != null) {
                mInteraction.interactionStart();
            }
            if (mRecorder == null || mInteraction == null || mRecorder.mKwsStarted) {
                return;
            }
            if (isHdmiUsed()) {
                return;
            }
            mRecorder.startInteraction();
        }
    }

    private boolean isHdmiUsed() {
        return SystemProperties.get("persist.vendor.czur.ai.interaction.hdmi.status", "0").equals("1");
    }

    @Override
    public void interactionStop() {
        Slog.i(TAG, "interactionStop");
        boolean enabled = isInteractionAiEnabled();
        if (enabled) {
            if (mRecorder == null || mInteraction == null || !mRecorder.mKwsStarted) {
                return;
            }
            mRecorder.stopInteraction();
            mInteraction.interactionStop();
        }
    }

    @Override
    public void interactionTrigger(boolean trigger) {
        Slog.i(TAG, "interactionTrigger");
        boolean enabled = isInteractionAiEnabled();
        if (enabled && mInteraction != null && !isHdmiUsed() && mRecorder.mKwsStarted) {
            mInteraction.interactionTrigger(trigger);
        }
    }

    @Override
    public void setPlayStatus(int status) {
        Slog.i(TAG, "setPlayStatus");
        boolean enabled = isInteractionAiEnabled();
        if (enabled && mInteraction != null) {
            mInteraction.setPlayStatus(status);
        }
    }
    @Override
    public void interactionKwsStatus(int status) {
        Slog.i(TAG, "interactionKwsStatus");
        SystemProperties.set("persist.vendor.czur.ai.interaction.stage.status", String.valueOf(status));
        if (mInteraction == null) {
            return;
        }
        mInteraction.interactionKwsStatus(status);
    }

    @Override
    public void powerStateChanged(int state) {
        Slog.i(TAG, "powerStateChanged:" + state);
        if (mInteraction == null) {
            return;
        }
        if (state == 0) {
            mInteraction.onErrorCallback(201, "power off");
            interactionStop();
        } else {
            interactionStart();
        }
    }

    @Override
    public void test1() {
        Slog.i(TAG, "interaction test1");
        mInteraction.test1();
    }
    @Override
    public void test2() {
        Slog.i(TAG, "interaction test2");
        mInteraction.test2();
    }
    @Override
    public void test3() {
        Slog.i(TAG, "interaction test3");
        mInteraction.test3();
    }
    @Override
    public void test4() {
        Slog.i(TAG, "interaction test4");
        mInteraction.test4();
    }

    @Override
    public int getInteractionStageStatus() {
        String status = SystemProperties.get("persist.vendor.czur.ai.interaction.stage.status", "0");
        Slog.i(TAG, "getInteractionStageStatus : " + status);
        return Integer.parseInt(status);
    }

    @Override
    public int getTtsStatus() {
        String status = SystemProperties.get("persist.vendor.czur.ai.interaction.tts.status", "1");
        Slog.i(TAG, "getTtsStatus : " + status);
        return Integer.parseInt(status);
    }

    @Override
    public void setTtsStatus(int status) {
        Slog.i(TAG, "setTtsStatus");
        SystemProperties.set("persist.vendor.czur.ai.interaction.tts.status", String.valueOf(status));
        if (mInteraction == null) {
            return;
        }
        mInteraction.setTtsStatus(status);
    }

    @Override
    public int getInteractionStatus() {
        String status = SystemProperties.get("persist.vendor.czur.ai.interaction.status", "0");
        Slog.i(TAG, "getInteractionStatus : " + status);
        return Integer.parseInt(status);
    }

    @Override
    public void setInteractionStatus(int status) {
        Slog.i(TAG, "setInteractionStatus");
        SystemProperties.set("persist.vendor.czur.ai.interaction.status", String.valueOf(status));
    }

    @Override
    public int setAsrName(String name) {
        Slog.i(TAG, "setAsrName:" + name);
        mTranslate.setAsrName(name);
        return 0;
    }

    @Override
    public int setTimezone(String timezone) {
        Slog.i(TAG, "setTimezone:" + timezone);
        mTranslate.setTimezone(timezone);
        return 0;
    }
}
