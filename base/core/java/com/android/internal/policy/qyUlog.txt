2025-07-29 11:06:49.182  1269-1269  KeyButtonView           com.android.systemui                 I  qyA KeyButtonView sendEvent: keyCode=24 action=ACTION_DOWN flags=0 when=3548432
2025-07-29 11:06:49.346  1269-1269  KeyButtonView           com.android.systemui                 I  qyA KeyButtonView sendEvent: keyCode=24 action=ACTION_UP flags=0 when=3548594
2025-07-29 11:06:53.635  1269-1269  KeyButtonView           com.android.systemui                 I  qyA KeyButtonView sendEvent: keyCode=24 action=ACTION_DOWN flags=0 when=3552884
2025-07-29 11:06:53.791  1829-1829  PhoneWindow             com.czur.starry.device.launcher      I  qyA PhoneWindow onKeyDown: keyCode=24 action=0 mMediaController=false mVolumeControlStreamType=-2147483648 packageName=com.czur.starry.device.launcher
2025-07-29 11:06:53.791  1829-1829  PhoneWindow             com.czur.starry.device.launcher      I  qyA PhoneWindow: 分发音量键事件到系统音量控制, streamType=-2147483648
2025-07-29 11:06:53.791  1269-1269  KeyButtonView           com.android.systemui                 I  qyA KeyButtonView sendEvent: keyCode=24 action=ACTION_UP flags=0 when=3553040
2025-07-29 11:06:53.791   784-3852  MediaSessionService     system_server                        I  qyA MediaSessionService dispatchVolumeKeyEvent: packageName=com.czur.starry.device.launcher keyCode=24 action=0 stream=-2147483648 musicOnly=false asSystemService=true
2025-07-29 11:06:53.792   784-3852  MediaSessionService     system_server                        I  qyA VolumeKeyEventHandler.handleVolumeKeyEventLocked: packageName=com.czur.starry.device.launcher keyCode=24 action=0 stream=-2147483648 musicOnly=false
2025-07-29 11:06:53.792   784-3852  MediaSessionService     system_server                        I  qyA dispatchVolumeKeyEventLocked: packageName=com.czur.starry.device.launcher keyCode=24 action=0 down=true up=false direction=1 stream=-2147483648 musicOnly=false
2025-07-29 11:06:53.792   784-3852  MediaSessionService     system_server                        I  qyA 调用dispatchAdjustVolumeLocked: direction=1 flags=4113 stream=-2147483648
2025-07-29 11:06:53.792   784-3852  MediaSessionService     system_server                        I  qyA dispatchAdjustVolumeLocked: packageName=com.czur.starry.device.launcher suggestedStream=-2147483648 direction=1 flags=4113 session=null preferSuggestedStream=false musicOnly=false
2025-07-29 11:06:53.793   784-784   MediaSessionService     system_server                        I  qyA 调用AudioManager.adjustSuggestedStreamVolumeForUid: suggestedStream=-2147483648 direction=1 flags=4113 callingOpPackageName=android callingUid=1000
2025-07-29 11:06:53.804   784-2536  MediaSessionService     system_server                        I  qyA MediaSessionService dispatchVolumeKeyEvent: packageName=com.czur.starry.device.launcher keyCode=24 action=1 stream=-2147483648 musicOnly=false asSystemService=true
2025-07-29 11:06:53.805   784-2536  MediaSessionService     system_server                        I  qyA VolumeKeyEventHandler.handleVolumeKeyEventLocked: packageName=com.czur.starry.device.launcher keyCode=24 action=1 stream=-2147483648 musicOnly=false
2025-07-29 11:06:53.805   784-2536  MediaSessionService     system_server                        I  qyA dispatchVolumeKeyEventLocked: packageName=com.czur.starry.device.launcher keyCode=24 action=1 down=false up=true direction=1 stream=-2147483648 musicOnly=false
2025-07-29 11:06:53.805   784-2536  MediaSessionService     system_server                        I  qyA 调用dispatchAdjustVolumeLocked: direction=0 flags=4116 stream=-2147483648
2025-07-29 11:06:53.805   784-2536  MediaSessionService     system_server                        I  qyA dispatchAdjustVolumeLocked: packageName=com.czur.starry.device.launcher suggestedStream=-2147483648 direction=0 flags=4116 session=null preferSuggestedStream=false musicOnly=false
2025-07-29 11:06:53.810   784-784   MediaSessionService     system_server                        I  qyA AudioManager.adjustSuggestedStreamVolumeForUid 调用成功
2025-07-29 11:06:53.813   784-784   MediaSessionService     system_server                        I  qyA 调用AudioManager.adjustSuggestedStreamVolumeForUid: suggestedStream=-2147483648 direction=0 flags=4116 callingOpPackageName=android callingUid=1000
2025-07-29 11:06:53.843   784-784   MediaSessionService     system_server                        I  qyA AudioManager.adjustSuggestedStreamVolumeForUid 调用成功
