<!--
/* Copyright 2012, The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources>
  <!-- Private symbols that we need to reference from framework code.  See
       frameworks/base/core/res/MakeJavaSymbols.sed for how to easily generate
       this.

       Can be referenced in java code as: com.android.internal.R.<type>.<name>
       and in layout xml as: "@*android:<type>/<name>"
  -->
  <java-symbol type="id" name="account_name" />
  <java-symbol type="id" name="account_row_icon" />
  <java-symbol type="id" name="account_row_text" />
  <java-symbol type="id" name="account_type" />
  <java-symbol type="id" name="action_bar" />
  <java-symbol type="id" name="action_bar_container" />
  <java-symbol type="id" name="action_bar_title" />
  <java-symbol type="id" name="action_bar_subtitle" />
  <java-symbol type="id" name="action_context_bar" />
  <java-symbol type="id" name="action_menu_presenter" />
  <java-symbol type="id" name="action_mode_close_button" />
  <java-symbol type="id" name="activity_chooser_view_content" />
  <java-symbol type="id" name="alertTitle" />
  <java-symbol type="id" name="allow_button" />
  <java-symbol type="id" name="alwaysUse" />
  <java-symbol type="id" name="amPm" />
  <java-symbol type="id" name="authtoken_type" />
  <java-symbol type="id" name="back_button" />
  <java-symbol type="id" name="button_bar" />
  <java-symbol type="id" name="buttonPanel" />
  <java-symbol type="id" name="by_common" />
  <java-symbol type="id" name="by_org" />
  <java-symbol type="id" name="by_org_unit" />
  <java-symbol type="id" name="calendar_view" />
  <java-symbol type="id" name="cancel" />
  <java-symbol type="id" name="characterPicker" />
  <java-symbol type="id" name="clearDefaultHint" />
  <java-symbol type="id" name="contentPanel" />
  <java-symbol type="id" name="content_preview_file_area" />
  <java-symbol type="id" name="content_preview_file_icon" />
  <java-symbol type="id" name="content_preview_file_layout" />
  <java-symbol type="id" name="content_preview_file_thumbnail" />
  <java-symbol type="id" name="content_preview_filename" />
  <java-symbol type="id" name="content_preview_image_area" />
  <java-symbol type="id" name="content_preview_image_1_large" />
  <java-symbol type="id" name="content_preview_image_2_large" />
  <java-symbol type="id" name="content_preview_image_2_small" />
  <java-symbol type="id" name="content_preview_image_3_small" />
  <java-symbol type="id" name="content_preview_thumbnail" />
  <java-symbol type="id" name="content_preview_text" />
  <java-symbol type="id" name="content_preview_text_area" />
  <java-symbol type="id" name="content_preview_text_layout" />
  <java-symbol type="id" name="content_preview_title" />
  <java-symbol type="id" name="content_preview_title_layout" />
  <java-symbol type="id" name="chooser_action_row" />
  <java-symbol type="id" name="current_scene" />
  <java-symbol type="id" name="scene_layoutid_cache" />
  <java-symbol type="id" name="customPanel" />
  <java-symbol type="id" name="datePicker" />
  <java-symbol type="id" name="day" />
  <java-symbol type="id" name="day_names" />
  <java-symbol type="id" name="decrement" />
  <java-symbol type="id" name="decor_content_parent" />
  <java-symbol type="id" name="default_activity_button" />
  <java-symbol type="id" name="deny_button" />
  <java-symbol type="id" name="description" />
  <java-symbol type="id" name="divider" />
  <java-symbol type="id" name="drag" />
  <java-symbol type="id" name="profile_pager" />
  <java-symbol type="id" name="chooser_header" />
  <java-symbol type="id" name="content_preview_container" />
  <java-symbol type="id" name="profile_tabhost" />
  <java-symbol type="id" name="edit_query" />
  <java-symbol type="id" name="edittext_container" />
  <java-symbol type="id" name="expand_activities_button" />
  <java-symbol type="id" name="expires_on" />
  <java-symbol type="id" name="find_next" />
  <java-symbol type="id" name="find_prev" />
  <java-symbol type="id" name="ffwd" />
  <java-symbol type="id" name="fillInIntent" />
  <java-symbol type="id" name="find" />
  <java-symbol type="id" name="fullscreenArea" />
  <java-symbol type="id" name="group_divider" />
  <java-symbol type="id" name="hard_keyboard_section" />
  <java-symbol type="id" name="hard_keyboard_switch" />
  <java-symbol type="id" name="headers" />
  <java-symbol type="id" name="hour" />
  <java-symbol type="id" name="icon" />
  <java-symbol type="id" name="image" />
  <java-symbol type="id" name="increment" />
  <java-symbol type="id" name="indicator" />
  <java-symbol type="id" name="internalEmpty" />
  <java-symbol type="id" name="inputExtractAccessories" />
  <java-symbol type="id" name="inputExtractAction" />
  <java-symbol type="id" name="issued_on" />
  <java-symbol type="id" name="language_item" />
  <java-symbol type="id" name="left_icon" />
  <java-symbol type="id" name="leftSpacer" />
  <java-symbol type="id" name="line1" />
  <java-symbol type="id" name="list_footer" />
  <java-symbol type="id" name="list_item" />
  <java-symbol type="id" name="listContainer" />
  <java-symbol type="id" name="locale" />
  <java-symbol type="id" name="locale_native" />
  <java-symbol type="id" name="locale_secondary" />
  <java-symbol type="id" name="matches" />
  <java-symbol type="id" name="mediacontroller_progress" />
  <java-symbol type="id" name="minute" />
  <java-symbol type="id" name="mode_normal" />
  <java-symbol type="id" name="month" />
  <java-symbol type="id" name="month_name" />
  <java-symbol type="id" name="next" />
  <java-symbol type="id" name="next_button" />
  <java-symbol type="id" name="new_app_action" />
  <java-symbol type="id" name="new_app_description" />
  <java-symbol type="id" name="new_app_icon" />
  <java-symbol type="id" name="no_permissions" />
  <java-symbol type="id" name="numberpicker_input" />
  <java-symbol type="id" name="old_app_action" />
  <java-symbol type="id" name="old_app_icon" />
  <java-symbol type="id" name="overlay_display_window_texture" />
  <java-symbol type="id" name="overlay_display_window_title" />
  <java-symbol type="id" name="package_label" />
  <java-symbol type="id" name="packages_list" />
  <java-symbol type="id" name="parentPanel" />
  <java-symbol type="id" name="pause" />
  <java-symbol type="id" name="perms_list" />
  <java-symbol type="id" name="perm_icon" />
  <java-symbol type="id" name="perm_name" />
  <java-symbol type="id" name="permission_group" />
  <java-symbol type="id" name="permission_list" />
  <java-symbol type="id" name="pickers" />
  <java-symbol type="id" name="prefs" />
  <java-symbol type="id" name="prefs_container" />
  <java-symbol type="id" name="prefs_frame" />
  <java-symbol type="id" name="prev" />
  <java-symbol type="id" name="progress" />
  <java-symbol type="id" name="progress_circular" />
  <java-symbol type="id" name="progress_horizontal" />
  <java-symbol type="id" name="progress_number" />
  <java-symbol type="id" name="progress_percent" />
  <java-symbol type="id" name="progress_dialog_message" />
  <java-symbol type="id" name="progressContainer" />
  <java-symbol type="id" name="rew" />
  <java-symbol type="id" name="rightSpacer" />
  <java-symbol type="id" name="rowTypeId" />
  <java-symbol type="id" name="scrollView" />
  <java-symbol type="id" name="search_app_icon" />
  <java-symbol type="id" name="search_badge" />
  <java-symbol type="id" name="search_bar" />
  <java-symbol type="id" name="search_button" />
  <java-symbol type="id" name="search_close_btn" />
  <java-symbol type="id" name="search_edit_frame" />
  <java-symbol type="id" name="search_go_btn" />
  <java-symbol type="id" name="search_mag_icon" />
  <java-symbol type="id" name="search_plate" />
  <java-symbol type="id" name="search_src_text" />
  <java-symbol type="id" name="search_view" />
  <java-symbol type="id" name="search_voice_btn" />
  <java-symbol type="id" name="select_all" />
  <java-symbol type="id" name="serial_number" />
  <java-symbol type="id" name="seekbar" />
  <java-symbol type="id" name="sha1_fingerprint" />
  <java-symbol type="id" name="sha256_fingerprint" />
  <java-symbol type="id" name="share" />
  <java-symbol type="id" name="shortcut" />
  <java-symbol type="id" name="skip_button" />
  <java-symbol type="id" name="split_action_bar" />
  <java-symbol type="id" name="submenuarrow" />
  <java-symbol type="id" name="submit_area" />
  <java-symbol type="id" name="switch_new" />
  <java-symbol type="id" name="switch_old" />
  <java-symbol type="id" name="switch_widget" />
  <java-symbol type="id" name="text" />
  <java-symbol type="id" name="time" />
  <java-symbol type="id" name="time_current" />
  <java-symbol type="id" name="titleDivider" />
  <java-symbol type="id" name="titleDividerTop" />
  <java-symbol type="id" name="timePicker" />
  <java-symbol type="id" name="title_template" />
  <java-symbol type="id" name="to_common" />
  <java-symbol type="id" name="to_org" />
  <java-symbol type="id" name="to_org_unit" />
  <java-symbol type="id" name="topPanel" />
  <java-symbol type="id" name="up" />
  <java-symbol type="id" name="value" />
  <java-symbol type="id" name="websearch" />
  <java-symbol type="id" name="year" />
  <java-symbol type="id" name="zoomControls" />
  <java-symbol type="id" name="zoomIn" />
  <java-symbol type="id" name="zoomMagnify" />
  <java-symbol type="id" name="zoomOut" />
  <java-symbol type="id" name="actions" />
  <java-symbol type="id" name="action0" />
  <java-symbol type="id" name="action1" />
  <java-symbol type="id" name="action2" />
  <java-symbol type="id" name="action3" />
  <java-symbol type="id" name="action4" />
  <java-symbol type="id" name="notification_media_content" />
  <java-symbol type="id" name="big_picture" />
  <java-symbol type="id" name="big_text" />
  <java-symbol type="id" name="chronometer" />
  <java-symbol type="id" name="inbox_text0" />
  <java-symbol type="id" name="inbox_text1" />
  <java-symbol type="id" name="inbox_text2" />
  <java-symbol type="id" name="inbox_text3" />
  <java-symbol type="id" name="inbox_text4" />
  <java-symbol type="id" name="inbox_text5" />
  <java-symbol type="id" name="inbox_text6" />
  <java-symbol type="id" name="status_bar_latest_event_content" />
  <java-symbol type="id" name="notification_main_column" />
  <java-symbol type="id" name="notification_headerless_view_column" />
  <java-symbol type="id" name="sms_short_code_confirm_message" />
  <java-symbol type="id" name="sms_short_code_detail_layout" />
  <java-symbol type="id" name="sms_short_code_detail_message" />
  <java-symbol type="id" name="sms_short_code_remember_choice_checkbox" />
  <java-symbol type="id" name="sms_short_code_remember_undo_instruction" />
  <java-symbol type="id" name="breadcrumb_section" />
  <java-symbol type="id" name="action_bar_spinner" />
  <java-symbol type="id" name="pin_cancel_button" />
  <java-symbol type="id" name="pin_ok_button" />
  <java-symbol type="id" name="pin_text" />
  <java-symbol type="id" name="pin_new_text" />
  <java-symbol type="id" name="pin_confirm_text" />
  <java-symbol type="id" name="pin_error_message" />
  <java-symbol type="id" name="timePickerLayout" />
  <java-symbol type="id" name="phishing_alert" />
  <java-symbol type="id" name="profile_badge" />
  <java-symbol type="id" name="alerted_icon" />
  <java-symbol type="id" name="transitionPosition" />
  <java-symbol type="id" name="selection_start_handle" />
  <java-symbol type="id" name="selection_end_handle" />
  <java-symbol type="id" name="insertion_handle" />
  <java-symbol type="id" name="accessibilityActionClickOnClickableSpan" />
  <java-symbol type="id" name="camera" />
  <java-symbol type="id" name="mic" />
  <java-symbol type="id" name="overlay" />
  <java-symbol type="id" name="app_ops" />
  <java-symbol type="id" name="feedback" />
  <java-symbol type="id" name="profile_pager" />
  <java-symbol type="id" name="content_preview_container" />
  <java-symbol type="id" name="profile_tabhost" />
  <java-symbol type="id" name="tabs" />
  <java-symbol type="id" name="tabcontent" />

  <java-symbol type="attr" name="actionModeShareDrawable" />
  <java-symbol type="attr" name="alertDialogCenterButtons" />
  <java-symbol type="attr" name="fragmentBreadCrumbsStyle" />
  <java-symbol type="attr" name="gestureOverlayViewStyle" />
  <java-symbol type="attr" name="keyboardViewStyle" />
  <java-symbol type="attr" name="numberPickerStyle" />
  <java-symbol type="attr" name="preferenceFrameLayoutStyle" />
  <java-symbol type="attr" name="searchDialogTheme" />
  <java-symbol type="attr" name="textAppearanceAutoCorrectionSuggestion" />
  <java-symbol type="attr" name="textAppearanceEasyCorrectSuggestion" />
  <java-symbol type="attr" name="textAppearanceGrammarErrorSuggestion" />
  <java-symbol type="attr" name="textAppearanceMisspelledSuggestion" />
  <java-symbol type="attr" name="textColorSearchUrl" />
  <java-symbol type="attr" name="timePickerStyle" />
  <java-symbol type="attr" name="windowFixedWidthMajor" />
  <java-symbol type="attr" name="windowFixedWidthMinor" />
  <java-symbol type="attr" name="windowFixedHeightMajor" />
  <java-symbol type="attr" name="windowFixedHeightMinor" />
  <java-symbol type="attr" name="accessibilityFocusedDrawable"/>
  <java-symbol type="attr" name="isLightTheme"/>
  <java-symbol type="attr" name="autofilledHighlight"/>
  <java-symbol type="attr" name="autofillDatasetPickerMaxWidth"/>
  <java-symbol type="attr" name="autofillDatasetPickerMaxHeight"/>
  <java-symbol type="attr" name="autofillSaveCustomSubtitleMaxHeight"/>
  <java-symbol type="bool" name="action_bar_embed_tabs" />
  <java-symbol type="bool" name="action_bar_expanded_action_views_exclusive" />
  <java-symbol type="integer" name="config_audio_notif_vol_default" />
  <java-symbol type="integer" name="config_audio_notif_vol_steps" />
  <java-symbol type="integer" name="config_audio_ring_vol_default" />
  <java-symbol type="integer" name="config_audio_ring_vol_steps" />
  <java-symbol type="bool" name="config_spatial_audio_head_tracking_enabled_default" />
  <java-symbol type="bool" name="config_avoidGfxAccel" />
  <java-symbol type="bool" name="config_bluetooth_address_validation" />
  <java-symbol type="integer" name="config_chooser_max_targets_per_row" />
  <java-symbol type="bool" name="config_flipToScreenOffEnabled" />
  <java-symbol type="integer" name="config_flipToScreenOffMaxLatencyMicros" />
  <java-symbol type="bool" name="config_bluetooth_sco_off_call" />
  <java-symbol type="bool" name="config_cellBroadcastAppLinks" />
  <java-symbol type="bool" name="config_duplicate_port_omadm_wappush" />
  <java-symbol type="bool" name="config_disableTransitionAnimation" />
  <java-symbol type="bool" name="config_enableAutoPowerModes" />
  <java-symbol type="integer" name="config_autoPowerModeThresholdAngle" />
  <java-symbol type="integer" name="config_autoPowerModeAnyMotionSensor" />
  <java-symbol type="bool" name="config_autoPowerModePreferWristTilt" />
  <java-symbol type="bool" name="config_autoPowerModePrefetchLocation" />
  <java-symbol type="bool" name="config_autoPowerModeUseMotionSensor" />
  <java-symbol type="bool" name="config_enable_emergency_call_while_sim_locked" />
  <java-symbol type="bool" name="config_enable_puk_unlock_screen" />
  <java-symbol type="bool" name="config_disableLockscreenByDefault" />
  <java-symbol type="bool" name="config_enableBurnInProtection" />
  <java-symbol type="bool" name="config_hotswapCapable" />
  <java-symbol type="bool" name="config_mms_content_disposition_support" />
  <java-symbol type="bool" name="config_networkSamplingWakesDevice" />
  <java-symbol type="bool" name="config_showMenuShortcutsWhenKeyboardPresent" />
  <java-symbol type="bool" name="config_sip_wifi_only" />
  <java-symbol type="bool" name="config_sms_ask_every_time_support" />
  <java-symbol type="bool" name="config_sms_capable" />
  <java-symbol type="bool" name="config_sms_utf8_support" />
  <java-symbol type="bool" name="config_smppsim_response_via_ims" />
  <java-symbol type="bool" name="config_mobile_data_capable" />
  <java-symbol type="bool" name="config_suspendWhenScreenOffDueToProximity" />
  <java-symbol type="bool" name="config_swipeDisambiguation" />
  <java-symbol type="bool" name="config_syncstorageengine_masterSyncAutomatically" />
  <java-symbol type="bool" name="config_ui_enableFadingMarquee" />
  <java-symbol type="bool" name="config_enableHapticTextHandle" />
  <java-symbol type="bool" name="config_use_strict_phone_number_comparation" />
  <java-symbol type="bool" name="config_use_strict_phone_number_comparation_for_russia" />
  <java-symbol type="bool" name="config_use_strict_phone_number_comparation_for_kazakhstan" />
  <java-symbol type="integer" name="config_phonenumber_compare_min_match" />
  <java-symbol type="bool" name="config_single_volume" />
  <java-symbol type="bool" name="config_volume_down_to_enter_silent" />
  <java-symbol type="bool" name="config_voice_capable" />
  <java-symbol type="bool" name="config_requireCallCapableAccountForHandle" />
  <java-symbol type="bool" name="config_user_notification_of_restrictied_mobile_access" />
  <java-symbol type="bool" name="config_wifiDisplaySupportsProtectedBuffers" />
  <java-symbol type="bool" name="preferences_prefer_dual_pane" />
  <java-symbol type="bool" name="skip_restoring_network_selection" />
  <java-symbol type="bool" name="split_action_bar_is_narrow" />
  <java-symbol type="bool" name="config_useVolumeKeySounds" />
  <java-symbol type="bool" name="config_enableWallpaperService" />
  <java-symbol type="bool" name="config_checkWallpaperAtBoot" />
  <java-symbol type="string" name="config_wallpaperManagerServiceName" />
  <java-symbol type="string" name="config_inputEventCompatProcessorOverrideClassName" />
  <java-symbol type="string" name="config_defaultHealthConnectApp" />
  <java-symbol type="bool" name="config_sendAudioBecomingNoisy" />
  <java-symbol type="bool" name="config_enableScreenshotChord" />
  <java-symbol type="bool" name="config_fold_lock_behavior" />
  <java-symbol type="bool" name="config_enableWifiDisplay" />
  <java-symbol type="bool" name="config_allowAnimationsInLowPowerMode" />
  <java-symbol type="bool" name="config_useDevInputEventForAudioJack" />
  <java-symbol type="bool" name="config_safe_media_volume_enabled" />
  <java-symbol type="bool" name="config_safe_media_disable_on_volume_up" />
  <java-symbol type="bool" name="config_safe_sound_dosage_enabled" />
  <java-symbol type="bool" name="config_camera_sound_forced" />
  <java-symbol type="bool" name="config_dontPreferApn" />
  <java-symbol type="bool" name="config_restartRadioAfterProvisioning" />
  <java-symbol type="bool" name="config_speed_up_audio_on_mt_calls" />
  <java-symbol type="bool" name="config_useFixedVolume" />
  <java-symbol type="bool" name="config_isMainUserPermanentAdmin"/>
  <java-symbol type="bool" name="config_canSwitchToHeadlessSystemUser"/>
  <java-symbol type="bool" name="config_enableMultiUserUI"/>
  <java-symbol type="bool" name="config_enableMultipleAdmins"/>
  <java-symbol type="bool" name="config_enableNewAutoSelectNetworkUI"/>
  <java-symbol type="bool" name="config_disableUsbPermissionDialogs"/>
  <java-symbol type="dimen" name="config_highResTaskSnapshotScale" />
  <java-symbol type="dimen" name="config_lowResTaskSnapshotScale" />
  <java-symbol type="dimen" name="config_resActivitySnapshotScale" />
  <java-symbol type="dimen" name="config_qsTileStrokeWidthInactive" />
  <java-symbol type="dimen" name="config_qsTileStrokeWidthActive" />
  <java-symbol type="bool" name="config_use16BitTaskSnapshotPixelFormat" />
  <java-symbol type="bool" name="config_hasRecents" />
  <java-symbol type="string" name="config_recentsComponentName" />
  <java-symbol type="string" name="config_systemUIServiceComponent" />
  <java-symbol type="string" name="config_controlsPackage" />
  <java-symbol type="string" name="config_screenRecorderComponent" />
  <java-symbol type="string" name="config_somnambulatorComponent" />
  <java-symbol type="string" name="config_screenshotAppClipsServiceComponent" />
  <java-symbol type="string" name="config_screenshotServiceComponent" />
  <java-symbol type="string" name="config_screenshotErrorReceiverComponent" />
  <java-symbol type="string" name="config_slicePermissionComponent" />
  <java-symbol type="string" name="config_usbContaminantActivity" />
  <java-symbol type="string" name="config_usbPermissionActivity" />
  <java-symbol type="string" name="config_usbAccessoryUriActivity" />
  <java-symbol type="string" name="config_usbConfirmActivity" />
  <java-symbol type="string" name="config_usbResolverActivity" />
  <java-symbol type="string" name="config_sensorUseStartedActivity" />
  <java-symbol type="string" name="config_sensorUseStartedActivity_hwToggle" />
  <java-symbol type="string" name="config_sensorStateChangedActivity" />
  <java-symbol type="string" name="config_hdmiCecSetMenuLanguageActivity" />
  <java-symbol type="integer" name="config_minNumVisibleRecentTasks_lowRam" />
  <java-symbol type="integer" name="config_maxNumVisibleRecentTasks_lowRam" />
  <java-symbol type="integer" name="config_minNumVisibleRecentTasks_grid" />
  <java-symbol type="integer" name="config_maxNumVisibleRecentTasks_grid" />
  <java-symbol type="integer" name="config_minNumVisibleRecentTasks" />
  <java-symbol type="integer" name="config_maxNumVisibleRecentTasks" />
  <java-symbol type="integer" name="config_activeTaskDurationHours" />
  <java-symbol type="bool" name="config_windowShowCircularMask" />
  <java-symbol type="bool" name="config_predictShowStartingSurface" />
  <java-symbol type="bool" name="config_windowEnableCircularEmulatorDisplayOverlay" />
  <java-symbol type="bool" name="config_supportMicNearUltrasound" />
  <java-symbol type="bool" name="config_supportSpeakerNearUltrasound" />
  <java-symbol type="bool" name="config_supportAudioSourceUnprocessed" />
  <java-symbol type="bool" name="config_freeformWindowManagement" />
  <java-symbol type="bool" name="config_supportsBubble" />
  <java-symbol type="bool" name="config_supportsMultiWindow" />
  <java-symbol type="bool" name="config_supportsSplitScreenMultiWindow" />
  <java-symbol type="bool" name="config_supportsMultiDisplay" />
  <java-symbol type="integer" name="config_supportsNonResizableMultiWindow" />
  <java-symbol type="integer" name="config_respectsActivityMinWidthHeightMultiWindow" />
  <java-symbol type="dimen" name="config_minPercentageMultiWindowSupportHeight" />
  <java-symbol type="dimen" name="config_minPercentageMultiWindowSupportWidth" />
  <java-symbol type="bool" name="config_useLegacySplit" />
  <java-symbol type="bool" name="config_noHomeScreen" />
  <java-symbol type="bool" name="config_supportsSystemDecorsOnSecondaryDisplays" />
  <java-symbol type="bool" name="config_supportsInsecureLockScreen" />
  <java-symbol type="bool" name="config_guestUserEphemeral" />
  <java-symbol type="bool" name="config_guestUserAutoCreated" />
  <java-symbol type="bool" name="config_guestUserAllowEphemeralStateChange" />
  <java-symbol type="bool" name="config_localDisplaysMirrorContent" />
  <java-symbol type="bool" name="config_ignoreUdfpsVote" />
  <java-symbol type="array" name="config_localPrivateDisplayPorts" />
  <java-symbol type="integer" name="config_defaultDisplayDefaultColorMode" />
  <java-symbol type="bool" name="config_enableAppWidgetService" />
  <java-symbol type="dimen" name="config_pictureInPictureMinAspectRatio" />
  <java-symbol type="dimen" name="config_pictureInPictureMaxAspectRatio" />
  <java-symbol type="integer" name="config_pictureInPictureMaxNumberOfActions" />
  <java-symbol type="dimen" name="config_pictureInPictureExpandedHorizontalHeight" />
  <java-symbol type="dimen" name="config_pictureInPictureExpandedVerticalWidth" />
  <java-symbol type="bool" name="config_dockBigOverlayWindows" />
  <java-symbol type="dimen" name="config_closeToSquareDisplayMaxAspectRatio" />
  <java-symbol type="integer" name="config_burnInProtectionMinHorizontalOffset" />
  <java-symbol type="integer" name="config_burnInProtectionMaxHorizontalOffset" />
  <java-symbol type="integer" name="config_burnInProtectionMinVerticalOffset" />
  <java-symbol type="integer" name="config_burnInProtectionMaxVerticalOffset" />
  <java-symbol type="integer" name="config_burnInProtectionMaxRadius" />
  <java-symbol type="integer" name="config_bluetooth_idle_cur_ma" />
  <java-symbol type="integer" name="config_bluetooth_rx_cur_ma" />
  <java-symbol type="integer" name="config_bluetooth_tx_cur_ma" />
  <java-symbol type="integer" name="config_bluetooth_operating_voltage_mv" />
  <java-symbol type="integer" name="config_cursorWindowSize" />
  <java-symbol type="integer" name="config_drawLockTimeoutMillis" />
  <java-symbol type="integer" name="config_doublePressOnPowerBehavior" />
  <java-symbol type="integer" name="config_extraFreeKbytesAdjust" />
  <java-symbol type="integer" name="config_extraFreeKbytesAbsolute" />
  <java-symbol type="integer" name="config_immersive_mode_confirmation_panic" />
  <java-symbol type="integer" name="config_longPressOnPowerBehavior" />
  <java-symbol type="integer" name="config_longPressOnPowerDurationMs" />
  <java-symbol type="array" name="config_longPressOnPowerDurationSettings" />
  <java-symbol type="bool" name="config_longPressOnPowerForAssistantSettingAvailable" />
  <java-symbol type="integer" name="config_veryLongPressOnPowerBehavior" />
  <java-symbol type="integer" name="config_veryLongPressTimeout" />
  <java-symbol type="integer" name="config_longPressOnBackBehavior" />
  <java-symbol type="bool" name="config_allowStartActivityForLongPressOnPowerInSetup" />
  <java-symbol type="integer" name="config_keyChordPowerVolumeUp" />
  <java-symbol type="integer" name="config_wakeUpToLastStateTimeoutMillis" />
  <java-symbol type="integer" name="config_lowMemoryKillerMinFreeKbytesAdjust" />
  <java-symbol type="integer" name="config_lowMemoryKillerMinFreeKbytesAbsolute" />
  <java-symbol type="integer" name="config_ntpPollingInterval" />
  <java-symbol type="integer" name="config_ntpPollingIntervalShorter" />
  <java-symbol type="integer" name="config_ntpRetry" />
  <java-symbol type="integer" name="config_ntpTimeout" />
  <java-symbol type="integer" name="config_shortPressOnPowerBehavior" />
  <java-symbol type="integer" name="config_toastDefaultGravity" />
  <java-symbol type="integer" name="config_triplePressOnPowerBehavior" />
  <java-symbol type="integer" name="config_shortPressOnSleepBehavior" />
  <java-symbol type="integer" name="config_longPressOnStemPrimaryBehavior" />
  <java-symbol type="integer" name="config_shortPressOnStemPrimaryBehavior" />
  <java-symbol type="integer" name="config_doublePressOnStemPrimaryBehavior" />
  <java-symbol type="integer" name="config_triplePressOnStemPrimaryBehavior" />
  <java-symbol type="string" name="config_doublePressOnPowerTargetActivity" />
  <java-symbol type="integer" name="config_searchKeyBehavior" />
  <java-symbol type="string" name="config_searchKeyTargetActivity" />
  <java-symbol type="integer" name="config_windowOutsetBottom" />
  <java-symbol type="integer" name="db_connection_pool_size" />
  <java-symbol type="integer" name="db_journal_size_limit" />
  <java-symbol type="integer" name="db_wal_autocheckpoint" />
  <java-symbol type="integer" name="db_default_idle_connection_timeout" />
  <java-symbol type="integer" name="config_soundEffectVolumeDb" />
  <java-symbol type="integer" name="config_lockSoundVolumeDb" />
  <java-symbol type="integer" name="config_multiuserMaximumUsers" />
  <java-symbol type="integer" name="config_multiuserMaxRunningUsers" />
  <java-symbol type="bool" name="config_multiuserDelayUserDataLocking" />
  <java-symbol type="bool" name="config_multiuserVisibleBackgroundUsers" />
  <java-symbol type="bool" name="config_multiuserVisibleBackgroundUsersOnDefaultDisplay" />
  <java-symbol type="bool" name="config_enableAppCloningBuildingBlocks" />
  <java-symbol type="bool" name="config_enableTimeoutToDockUserWhenDocked" />
  <java-symbol type="integer" name="config_userTypePackageWhitelistMode"/>
  <java-symbol type="xml" name="config_user_types" />
  <java-symbol type="integer" name="config_safe_media_volume_index" />
  <java-symbol type="integer" name="config_safe_media_volume_usb_mB" />
  <java-symbol type="integer" name="config_mobile_mtu" />
  <java-symbol type="array"   name="config_mobile_tcp_buffers" />
  <java-symbol type="integer" name="config_volte_replacement_rat"/>
  <java-symbol type="integer" name="config_valid_wappush_index" />
  <java-symbol type="integer" name="config_overrideHasPermanentMenuKey" />
  <java-symbol type="integer" name="config_mdc_initial_max_retry" />
  <java-symbol type="integer" name="config_keepPreloadsMinDays" />
  <java-symbol type="bool" name="config_hasPermanentDpad" />
  <java-symbol type="bool" name="config_useDefaultFocusHighlight" />
  <java-symbol type="array" name="config_deviceSpecificSystemServices" />
  <java-symbol type="string" name="config_deviceSpecificDevicePolicyManagerService" />
  <java-symbol type="string" name="config_deviceSpecificAudioService" />
  <java-symbol type="string" name="config_deviceSpecificInputMethodManagerService" />
  <java-symbol type="integer" name="config_num_physical_slots" />
  <java-symbol type="integer" name="config_default_cellular_usage_setting" />
  <java-symbol type="array" name="config_supported_cellular_usage_settings" />
  <java-symbol type="array" name="config_integrityRuleProviderPackages" />
  <java-symbol type="bool" name="config_useAssistantVolume" />
  <java-symbol type="integer" name="config_smartSelectionInitializedTimeoutMillis" />
  <java-symbol type="integer" name="config_smartSelectionInitializingTimeoutMillis" />
  <java-symbol type="bool" name="config_preferKeepClearForFocus" />
  <java-symbol type="bool" name="config_hibernationDeletesOatArtifactsEnabled"/>
  <java-symbol type="integer" name="config_defaultAnalogClockSecondsHandFps"/>

  <java-symbol type="color" name="tab_indicator_text_v4" />

  <java-symbol type="dimen" name="accessibility_touch_slop" />
  <java-symbol type="dimen" name="alert_dialog_round_padding"/>
  <java-symbol type="dimen" name="config_minScrollbarTouchTarget" />
  <java-symbol type="dimen" name="config_prefDialogWidth" />
  <java-symbol type="dimen" name="config_viewConfigurationTouchSlop" />
  <java-symbol type="dimen" name="config_viewConfigurationHandwritingSlop" />
  <java-symbol type="dimen" name="config_viewConfigurationHoverSlop" />
  <java-symbol type="dimen" name="config_ambiguousGestureMultiplier" />
  <java-symbol type="dimen" name="config_autoKeyboardBrightnessSmoothingConstant" />
  <java-symbol type="dimen" name="config_viewMinFlingVelocity" />
  <java-symbol type="dimen" name="config_viewMaxFlingVelocity" />
  <java-symbol type="dimen" name="config_viewMinRotaryEncoderFlingVelocity" />
  <java-symbol type="dimen" name="config_viewMaxRotaryEncoderFlingVelocity" />
  <java-symbol type="dimen" name="config_scrollbarSize" />
  <java-symbol type="dimen" name="config_horizontalScrollFactor" />
  <java-symbol type="dimen" name="config_verticalScrollFactor" />
  <java-symbol type="dimen" name="config_scrollFactor" />
  <java-symbol type="dimen" name="default_app_widget_padding_bottom" />
  <java-symbol type="dimen" name="default_app_widget_padding_left" />
  <java-symbol type="dimen" name="default_app_widget_padding_right" />
  <java-symbol type="dimen" name="default_app_widget_padding_top" />
  <java-symbol type="dimen" name="default_gap" />
  <java-symbol type="dimen" name="dropdownitem_icon_width" />
  <java-symbol type="dimen" name="dropdownitem_text_padding_left" />
  <java-symbol type="dimen" name="password_keyboard_spacebar_vertical_correction" />
  <java-symbol type="dimen" name="search_view_preferred_width" />
  <java-symbol type="dimen" name="search_view_preferred_height" />
  <java-symbol type="dimen" name="textview_error_popup_default_width" />
  <java-symbol type="dimen" name="toast_y_offset" />
  <java-symbol type="dimen" name="tooltip_precise_anchor_threshold" />
  <java-symbol type="dimen" name="tooltip_precise_anchor_extra_offset" />
  <java-symbol type="dimen" name="tooltip_y_offset_touch" />
  <java-symbol type="dimen" name="tooltip_y_offset_non_touch" />
  <java-symbol type="dimen" name="action_bar_stacked_max_height" />
  <java-symbol type="dimen" name="action_bar_stacked_tab_max_width" />
  <java-symbol type="dimen" name="notification_text_size" />
  <java-symbol type="dimen" name="notification_title_text_size" />
  <java-symbol type="dimen" name="notification_subtext_size" />
  <java-symbol type="dimen" name="notification_top_pad" />
  <java-symbol type="dimen" name="notification_top_pad_narrow" />
  <java-symbol type="dimen" name="notification_top_pad_large_text" />
  <java-symbol type="dimen" name="notification_top_pad_large_text_narrow" />
  <java-symbol type="dimen" name="notification_badge_size" />
  <java-symbol type="dimen" name="immersive_mode_cling_width" />
  <java-symbol type="dimen" name="accessibility_magnification_indicator_width" />
  <java-symbol type="dimen" name="circular_display_mask_thickness" />
  <java-symbol type="dimen" name="user_icon_size" />

  <java-symbol type="string" name="add_account_button_label" />
  <java-symbol type="string" name="addToDictionary" />
  <java-symbol type="string" name="action_bar_home_description" />
  <java-symbol type="string" name="action_bar_up_description" />
  <java-symbol type="string" name="activity_resolver_work_profiles_support" />
  <java-symbol type="string" name="app_running_notification_title" />
  <java-symbol type="string" name="app_running_notification_text" />
  <java-symbol type="string" name="delete" />
  <java-symbol type="string" name="deleteText" />
  <java-symbol type="string" name="grant_permissions_header_text" />
  <java-symbol type="string" name="menu_alt_shortcut_label" />
  <java-symbol type="string" name="menu_ctrl_shortcut_label" />
  <java-symbol type="string" name="menu_delete_shortcut_label" />
  <java-symbol type="string" name="menu_enter_shortcut_label" />
  <java-symbol type="string" name="menu_function_shortcut_label" />
  <java-symbol type="string" name="menu_meta_shortcut_label" />
  <java-symbol type="string" name="menu_space_shortcut_label" />
  <java-symbol type="string" name="menu_shift_shortcut_label" />
  <java-symbol type="string" name="menu_sym_shortcut_label" />
  <java-symbol type="string" name="mobile_no_internet" />
  <java-symbol type="string" name="notification_title" />
  <java-symbol type="string" name="other_networks_no_internet" />
  <java-symbol type="string" name="permission_request_notification_with_subtitle" />
  <java-symbol type="string" name="permission_request_notification_for_app_with_subtitle" />
  <java-symbol type="string" name="prepend_shortcut_label" />
  <java-symbol type="string" name="private_dns_broken_detailed" />
  <java-symbol type="string" name="paste_as_plain_text" />
  <java-symbol type="string" name="pasted_from_clipboard" />
  <java-symbol type="string" name="replace" />
  <java-symbol type="string" name="undo" />
  <java-symbol type="string" name="redo" />
  <java-symbol type="string" name="textSelectionCABTitle" />
  <java-symbol type="string" name="BaMmi" />
  <java-symbol type="string" name="CLIRDefaultOffNextCallOff" />
  <java-symbol type="string" name="CLIRDefaultOffNextCallOn" />
  <java-symbol type="string" name="CLIRDefaultOnNextCallOff" />
  <java-symbol type="string" name="CLIRDefaultOnNextCallOn" />
  <java-symbol type="string" name="CLIRPermanent" />
  <java-symbol type="string" name="CfMmi" />
  <java-symbol type="string" name="ClipMmi" />
  <java-symbol type="string" name="ClirMmi" />
  <java-symbol type="string" name="ColpMmi" />
  <java-symbol type="string" name="ColrMmi" />
  <java-symbol type="string" name="CwMmi" />
  <java-symbol type="string" name="Midnight" />
  <java-symbol type="string" name="Noon" />
  <java-symbol type="string" name="PinMmi" />
  <java-symbol type="string" name="PwdMmi" />
  <java-symbol type="string" name="NetworkPreferenceSwitchSummary" />
  <java-symbol type="string" name="NetworkPreferenceSwitchTitle" />
  <java-symbol type="string" name="EmergencyCallWarningTitle" />
  <java-symbol type="string" name="EmergencyCallWarningSummary" />
  <java-symbol type="string" name="RestrictedOnAllVoiceTitle" />
  <java-symbol type="string" name="RestrictedOnDataTitle" />
  <java-symbol type="string" name="RestrictedOnEmergencyTitle" />
  <java-symbol type="string" name="RestrictedOnNormalTitle" />
  <java-symbol type="string" name="RestrictedStateContent" />
  <java-symbol type="string" name="auto_data_switch_title" />
  <java-symbol type="string" name="auto_data_switch_content" />
  <java-symbol type="string" name="RestrictedStateContentMsimTemplate" />
  <java-symbol type="string" name="notification_channel_network_alert" />
  <java-symbol type="string" name="notification_channel_call_forward" />
  <java-symbol type="string" name="notification_channel_emergency_callback" />
  <java-symbol type="string" name="notification_channel_mobile_data_status" />
  <java-symbol type="string" name="notification_channel_sms" />
  <java-symbol type="string" name="notification_channel_voice_mail" />
  <java-symbol type="string" name="notification_channel_wfc" />
  <java-symbol type="string" name="notification_channel_sim" />
  <java-symbol type="string" name="notification_channel_sim_high_prio" />
  <java-symbol type="string" name="SetupCallDefault" />
  <java-symbol type="string" name="accept" />
  <java-symbol type="string" name="activity_chooser_view_see_all" />
  <java-symbol type="string" name="activitychooserview_choose_application" />
  <java-symbol type="string" name="activitychooserview_choose_application_error" />
  <java-symbol type="string" name="alternate_eri_file" />
  <java-symbol type="string" name="alwaysUse" />
  <java-symbol type="string" name="autofill_window_title" />
  <java-symbol type="string" name="badPin" />
  <java-symbol type="string" name="badPuk" />
  <java-symbol type="string" name="byteShort" />
  <java-symbol type="string" name="capability_title_canRequestFilterKeyEvents" />
  <java-symbol type="string" name="capability_desc_canRequestTouchExploration" />
  <java-symbol type="string" name="capability_desc_canRetrieveWindowContent" />
  <java-symbol type="string" name="capability_desc_canRequestFilterKeyEvents" />
  <java-symbol type="string" name="capability_title_canRequestTouchExploration" />
  <java-symbol type="string" name="capability_title_canRetrieveWindowContent" />
  <java-symbol type="string" name="capability_desc_canControlMagnification" />
  <java-symbol type="string" name="capability_title_canControlMagnification" />
  <java-symbol type="string" name="capability_desc_canPerformGestures" />
  <java-symbol type="string" name="capability_title_canPerformGestures" />
  <java-symbol type="string" name="capital_off" />
  <java-symbol type="string" name="capital_on" />
  <java-symbol type="string" name="cfTemplateForwarded" />
  <java-symbol type="string" name="cfTemplateForwardedTime" />
  <java-symbol type="string" name="cfTemplateNotForwarded" />
  <java-symbol type="string" name="cfTemplateRegistered" />
  <java-symbol type="string" name="cfTemplateRegisteredTime" />
  <java-symbol type="string" name="chooseActivity" />
  <java-symbol type="string" name="checked" />
  <java-symbol type="array" name="config_companionDevicePackages" />
  <java-symbol type="array" name="config_companionDeviceCerts" />
  <java-symbol type="string" name="config_default_dns_server" />
  <java-symbol type="string" name="config_ethernet_iface_regex" />
  <java-symbol type="string" name="not_checked" />
  <java-symbol type="array" name="config_ethernet_interfaces" />
  <java-symbol type="bool" name="config_vehicleInternalNetworkAlwaysRequested" />
  <java-symbol type="string" name="config_forceVoiceInteractionServicePackage" />
  <java-symbol type="string" name="config_mms_user_agent" />
  <java-symbol type="string" name="config_mms_user_agent_profile_url" />
  <java-symbol type="array" name="config_ntpServers" />
  <java-symbol type="string" name="config_useragentprofile_url" />
  <java-symbol type="string" name="config_appsNotReportingCrashes" />
  <java-symbol type="string" name="contentServiceSync" />
  <java-symbol type="string" name="contentServiceSyncNotificationTitle" />
  <java-symbol type="string" name="contentServiceTooManyDeletesNotificationDesc" />
  <java-symbol type="string" name="csd_dose_reached_warning" />
  <java-symbol type="string" name="csd_momentary_exposure_warning" />
  <java-symbol type="string" name="date_and_time" />
  <java-symbol type="string" name="date_picker_decrement_day_button" />
  <java-symbol type="string" name="date_picker_decrement_month_button" />
  <java-symbol type="string" name="date_picker_decrement_year_button" />
  <java-symbol type="string" name="date_picker_dialog_title" />
  <java-symbol type="string" name="date_picker_increment_day_button" />
  <java-symbol type="string" name="date_picker_increment_month_button" />
  <java-symbol type="string" name="date_picker_increment_year_button" />
  <java-symbol type="string" name="date_time" />
  <java-symbol type="string" name="date_time_set" />
  <java-symbol type="string" name="date_time_done" />
  <java-symbol type="string" name="db_default_journal_mode" />
  <java-symbol type="string" name="db_default_sync_mode" />
  <java-symbol type="string" name="db_wal_sync_mode" />
  <java-symbol type="string" name="decline" />
  <java-symbol type="string" name="description_target_unlock_tablet" />
  <java-symbol type="string" name="display_manager_built_in_display_name" />
  <java-symbol type="string" name="display_manager_hdmi_display_name" />
  <java-symbol type="string" name="display_manager_overlay_display_name" />
  <java-symbol type="string" name="display_manager_overlay_display_secure_suffix" />
  <java-symbol type="string" name="display_manager_overlay_display_title" />
  <java-symbol type="string" name="elapsed_time_short_format_h_mm_ss" />
  <java-symbol type="string" name="elapsed_time_short_format_mm_ss" />
  <java-symbol type="string" name="emailTypeCustom" />
  <java-symbol type="string" name="emailTypeHome" />
  <java-symbol type="string" name="emailTypeMobile" />
  <java-symbol type="string" name="emailTypeOther" />
  <java-symbol type="string" name="emailTypeWork" />
  <java-symbol type="string" name="emergency_call_dialog_number_for_display" />
  <java-symbol type="string" name="widget_default_package_name" />
  <java-symbol type="string" name="widget_default_class_name" />
  <java-symbol type="string" name="emergency_calls_only" />
  <java-symbol type="array" name="config_ephemeralResolverPackage" />
  <java-symbol type="array" name="config_forceQueryablePackages" />
  <java-symbol type="bool" name="config_forceSystemPackagesQueryable" />
  <java-symbol type="string" name="eventTypeAnniversary" />
  <java-symbol type="string" name="eventTypeBirthday" />
  <java-symbol type="string" name="eventTypeCustom" />
  <java-symbol type="string" name="eventTypeOther" />
  <java-symbol type="string" name="fileSizeSuffix" />
  <java-symbol type="string" name="force_close" />
  <java-symbol type="string" name="gadget_host_error_inflating" />
  <java-symbol type="string" name="gpsNotifMessage" />
  <java-symbol type="string" name="gpsNotifTicker" />
  <java-symbol type="string" name="gpsNotifTitle" />
  <java-symbol type="string" name="gpsVerifNo" />
  <java-symbol type="string" name="gpsVerifYes" />
  <java-symbol type="string" name="gsm_alphabet_default_charset" />
  <java-symbol type="string" name="httpError" />
  <java-symbol type="string" name="httpErrorAuth" />
  <java-symbol type="string" name="httpErrorConnect" />
  <java-symbol type="string" name="httpErrorFailedSslHandshake" />
  <java-symbol type="string" name="httpErrorFile" />
  <java-symbol type="string" name="httpErrorFileNotFound" />
  <java-symbol type="string" name="httpErrorIO" />
  <java-symbol type="string" name="httpErrorLookup" />
  <java-symbol type="string" name="httpErrorOk" />
  <java-symbol type="string" name="httpErrorProxyAuth" />
  <java-symbol type="string" name="httpErrorRedirectLoop" />
  <java-symbol type="string" name="httpErrorTimeout" />
  <java-symbol type="string" name="httpErrorTooManyRequests" />
  <java-symbol type="string" name="httpErrorUnsupportedAuthScheme" />
  <java-symbol type="string" name="imProtocolAim" />
  <java-symbol type="string" name="imProtocolCustom" />
  <java-symbol type="string" name="imProtocolGoogleTalk" />
  <java-symbol type="string" name="imProtocolIcq" />
  <java-symbol type="string" name="imProtocolJabber" />
  <java-symbol type="string" name="imProtocolMsn" />
  <java-symbol type="string" name="imProtocolNetMeeting" />
  <java-symbol type="string" name="imProtocolQq" />
  <java-symbol type="string" name="imProtocolSkype" />
  <java-symbol type="string" name="imProtocolYahoo" />
  <java-symbol type="string" name="imTypeCustom" />
  <java-symbol type="string" name="imTypeHome" />
  <java-symbol type="string" name="imTypeOther" />
  <java-symbol type="string" name="imTypeWork" />
  <java-symbol type="string" name="ime_action_default" />
  <java-symbol type="string" name="ime_action_done" />
  <java-symbol type="string" name="ime_action_go" />
  <java-symbol type="string" name="ime_action_next" />
  <java-symbol type="string" name="ime_action_previous" />
  <java-symbol type="string" name="ime_action_search" />
  <java-symbol type="string" name="ime_action_send" />
  <java-symbol type="string" name="in_progress" />
  <java-symbol type="string" name="invalidPin" />
  <java-symbol type="string" name="js_dialog_before_unload_positive_button" />
  <java-symbol type="string" name="js_dialog_before_unload_negative_button" />
  <java-symbol type="string" name="js_dialog_before_unload_title" />
  <java-symbol type="string" name="js_dialog_before_unload" />
  <java-symbol type="string" name="js_dialog_title" />
  <java-symbol type="string" name="js_dialog_title_default" />
  <java-symbol type="string" name="keyboardview_keycode_alt" />
  <java-symbol type="string" name="keyboardview_keycode_cancel" />
  <java-symbol type="string" name="keyboardview_keycode_delete" />
  <java-symbol type="string" name="keyboardview_keycode_done" />
  <java-symbol type="string" name="keyboardview_keycode_enter" />
  <java-symbol type="string" name="keyboardview_keycode_mode_change" />
  <java-symbol type="string" name="keyboardview_keycode_shift" />
  <java-symbol type="string" name="last_month" />
  <java-symbol type="string" name="launchBrowserDefault" />
  <java-symbol type="string" name="lock_to_app_unlock_pin" />
  <java-symbol type="string" name="lock_to_app_unlock_pattern" />
  <java-symbol type="string" name="lock_to_app_unlock_password" />
  <java-symbol type="string" name="package_installed_device_owner" />
  <java-symbol type="string" name="package_updated_device_owner" />
  <java-symbol type="string" name="package_deleted_device_owner" />
  <java-symbol type="string" name="lockscreen_access_pattern_cell_added" />
  <java-symbol type="string" name="lockscreen_access_pattern_cell_added_verbose" />
  <java-symbol type="string" name="lockscreen_access_pattern_cleared" />
  <java-symbol type="string" name="lockscreen_access_pattern_detected" />
  <java-symbol type="string" name="lockscreen_access_pattern_start" />
  <java-symbol type="string" name="lockscreen_emergency_call" />
  <java-symbol type="string" name="lockscreen_return_to_call" />
  <java-symbol type="string" name="low_memory" />
  <java-symbol type="string" name="mic_access_off_toast" />
  <java-symbol type="string" name="mic_access_on_toast" />
  <java-symbol type="string" name="midnight" />
  <java-symbol type="string" name="mismatchPin" />
  <java-symbol type="string" name="mmiComplete" />
  <java-symbol type="string" name="mmiError" />
  <java-symbol type="string" name="mmiErrorNotSupported" />
  <java-symbol type="string" name="mmiFdnError" />
  <java-symbol type="string" name="mmiErrorWhileRoaming" />
  <java-symbol type="string" name="month_day_year" />
  <java-symbol type="string" name="more_item_label" />
  <java-symbol type="string" name="needPuk" />
  <java-symbol type="string" name="needPuk2" />
  <java-symbol type="string" name="enablePin" />
  <java-symbol type="string" name="new_app_action" />
  <java-symbol type="string" name="new_app_description" />
  <java-symbol type="string" name="noApplications" />
  <java-symbol type="string" name="no_file_chosen" />
  <java-symbol type="string" name="no_matches" />
  <java-symbol type="string" name="noon" />
  <java-symbol type="string" name="not_selected" />
  <java-symbol type="string" name="number_picker_increment_scroll_action" />
  <java-symbol type="string" name="number_picker_increment_scroll_mode" />
  <java-symbol type="string" name="old_app_action" />
  <java-symbol type="string" name="older" />
  <java-symbol type="string" name="orgTypeCustom" />
  <java-symbol type="string" name="orgTypeOther" />
  <java-symbol type="string" name="orgTypeWork" />
  <java-symbol type="string" name="passwordIncorrect" />
  <java-symbol type="string" name="perms_description_app" />
  <java-symbol type="string" name="perms_new_perm_prefix" />
  <java-symbol type="string" name="peerTtyModeFull" />
  <java-symbol type="string" name="peerTtyModeHco" />
  <java-symbol type="string" name="peerTtyModeVco" />
  <java-symbol type="string" name="peerTtyModeOff" />
  <java-symbol type="string" name="phoneTypeAssistant" />
  <java-symbol type="string" name="phoneTypeCallback" />
  <java-symbol type="string" name="phoneTypeCar" />
  <java-symbol type="string" name="phoneTypeCompanyMain" />
  <java-symbol type="string" name="phoneTypeCustom" />
  <java-symbol type="string" name="phoneTypeFaxHome" />
  <java-symbol type="string" name="phoneTypeFaxWork" />
  <java-symbol type="string" name="phoneTypeHome" />
  <java-symbol type="string" name="phoneTypeIsdn" />
  <java-symbol type="string" name="phoneTypeMain" />
  <java-symbol type="string" name="phoneTypeMms" />
  <java-symbol type="string" name="phoneTypeMobile" />
  <java-symbol type="string" name="phoneTypeOther" />
  <java-symbol type="string" name="phoneTypeOtherFax" />
  <java-symbol type="string" name="phoneTypePager" />
  <java-symbol type="string" name="phoneTypeRadio" />
  <java-symbol type="string" name="phoneTypeTelex" />
  <java-symbol type="string" name="phoneTypeTtyTdd" />
  <java-symbol type="string" name="phoneTypeWork" />
  <java-symbol type="string" name="phoneTypeWorkMobile" />
  <java-symbol type="string" name="phoneTypeWorkPager" />
  <java-symbol type="string" name="wfcRegErrorTitle" />
  <java-symbol type="array" name="wfcOperatorErrorAlertMessages" />
  <java-symbol type="array" name="wfcOperatorErrorNotificationMessages" />
  <java-symbol type="array" name="wfcSpnFormats" />
  <java-symbol type="string" name="wifi_calling_off_summary" />
  <java-symbol type="string" name="wfc_mode_wifi_preferred_summary" />
  <java-symbol type="string" name="wfc_mode_cellular_preferred_summary" />
  <java-symbol type="string" name="wfc_mode_wifi_only_summary" />
  <java-symbol type="array" name="crossSimSpnFormats" />
  <java-symbol type="string" name="policydesc_disableCamera" />
  <java-symbol type="string" name="policydesc_encryptedStorage" />
  <java-symbol type="string" name="policydesc_expirePassword" />
  <java-symbol type="string" name="policydesc_forceLock" />
  <java-symbol type="string" name="policydesc_limitPassword" />
  <java-symbol type="string" name="policydesc_resetPassword" />
  <java-symbol type="string" name="policydesc_setGlobalProxy" />
  <java-symbol type="string" name="policydesc_watchLogin" />
  <java-symbol type="string" name="policydesc_watchLogin_secondaryUser" />
  <java-symbol type="string" name="policydesc_wipeData" />
  <java-symbol type="string" name="policydesc_wipeData_secondaryUser" />
  <java-symbol type="string" name="policydesc_disableKeyguardFeatures" />
  <java-symbol type="string" name="policylab_disableCamera" />
  <java-symbol type="string" name="policylab_encryptedStorage" />
  <java-symbol type="string" name="policylab_expirePassword" />
  <java-symbol type="string" name="policylab_forceLock" />
  <java-symbol type="string" name="policylab_limitPassword" />
  <java-symbol type="string" name="policylab_resetPassword" />
  <java-symbol type="string" name="policylab_setGlobalProxy" />
  <java-symbol type="string" name="policylab_watchLogin" />
  <java-symbol type="string" name="policylab_wipeData" />
  <java-symbol type="string" name="policylab_wipeData_secondaryUser" />
  <java-symbol type="string" name="policylab_disableKeyguardFeatures" />
  <java-symbol type="string" name="postalTypeCustom" />
  <java-symbol type="string" name="postalTypeHome" />
  <java-symbol type="string" name="postalTypeOther" />
  <java-symbol type="string" name="postalTypeWork" />
  <java-symbol type="string" name="power_off" />
  <java-symbol type="string" name="preposition_for_date" />
  <java-symbol type="string" name="preposition_for_time" />
  <java-symbol type="string" name="print_service_installed_title" />
  <java-symbol type="string" name="print_service_installed_message" />
  <java-symbol type="string" name="printing_disabled_by" />
  <java-symbol type="string" name="progress_erasing" />
  <java-symbol type="string" name="mobile_provisioning_apn" />
  <java-symbol type="string" name="mobile_provisioning_url" />
  <java-symbol type="string" name="quick_contacts_not_available" />
  <java-symbol type="string" name="rating_label" />
  <java-symbol type="string" name="reboot_to_update_package" />
  <java-symbol type="string" name="reboot_to_update_prepare" />
  <java-symbol type="string" name="reboot_to_update_title" />
  <java-symbol type="string" name="reboot_to_update_reboot" />
  <java-symbol type="string" name="reboot_to_reset_title" />
  <java-symbol type="string" name="reboot_to_reset_message" />
  <java-symbol type="string" name="reboot_safemode_confirm" />
  <java-symbol type="string" name="reboot_safemode_title" />
  <java-symbol type="string" name="relationTypeAssistant" />
  <java-symbol type="string" name="relationTypeBrother" />
  <java-symbol type="string" name="relationTypeChild" />
  <java-symbol type="string" name="relationTypeDomesticPartner" />
  <java-symbol type="string" name="relationTypeFather" />
  <java-symbol type="string" name="relationTypeFriend" />
  <java-symbol type="string" name="relationTypeManager" />
  <java-symbol type="string" name="relationTypeMother" />
  <java-symbol type="string" name="relationTypeParent" />
  <java-symbol type="string" name="relationTypePartner" />
  <java-symbol type="string" name="relationTypeReferredBy" />
  <java-symbol type="string" name="relationTypeRelative" />
  <java-symbol type="string" name="relationTypeSister" />
  <java-symbol type="string" name="relationTypeSpouse" />
  <java-symbol type="string" name="relative_time" />
  <java-symbol type="string" name="reset" />
  <java-symbol type="string" name="revoke" />
  <java-symbol type="string" name="ringtone_default" />
  <java-symbol type="string" name="ringtone_default_with_actual" />
  <java-symbol type="string" name="ringtone_picker_title" />
  <java-symbol type="string" name="ringtone_picker_title_alarm" />
  <java-symbol type="string" name="ringtone_picker_title_notification" />
  <java-symbol type="string" name="ringtone_silent" />
  <java-symbol type="string" name="ringtone_unknown" />
  <java-symbol type="string" name="roamingText0" />
  <java-symbol type="string" name="roamingText1" />
  <java-symbol type="string" name="roamingText10" />
  <java-symbol type="string" name="roamingText11" />
  <java-symbol type="string" name="roamingText12" />
  <java-symbol type="string" name="roamingText2" />
  <java-symbol type="string" name="roamingText3" />
  <java-symbol type="string" name="roamingText4" />
  <java-symbol type="string" name="roamingText5" />
  <java-symbol type="string" name="roamingText6" />
  <java-symbol type="string" name="roamingText7" />
  <java-symbol type="string" name="roamingText8" />
  <java-symbol type="string" name="roamingText9" />
  <java-symbol type="string" name="roamingTextSearching" />
  <java-symbol type="string" name="selected" />
  <java-symbol type="string" name="sendText" />
  <java-symbol type="string" name="sending" />
  <java-symbol type="string" name="serviceClassData" />
  <java-symbol type="string" name="serviceClassDataAsync" />
  <java-symbol type="string" name="serviceClassDataSync" />
  <java-symbol type="string" name="serviceClassFAX" />
  <java-symbol type="string" name="serviceClassPAD" />
  <java-symbol type="string" name="serviceClassPacket" />
  <java-symbol type="string" name="serviceClassSMS" />
  <java-symbol type="string" name="serviceClassVoice" />
  <java-symbol type="string" name="serviceDisabled" />
  <java-symbol type="string" name="serviceEnabled" />
  <java-symbol type="string" name="serviceEnabledFor" />
  <java-symbol type="string" name="serviceErased" />
  <java-symbol type="string" name="serviceNotProvisioned" />
  <java-symbol type="string" name="serviceRegistered" />
  <java-symbol type="string" name="share" />
  <java-symbol type="string" name="shareactionprovider_share_with" />
  <java-symbol type="string" name="shareactionprovider_share_with_application" />
  <java-symbol type="string" name="shutdown_confirm" />
  <java-symbol type="string" name="shutdown_confirm_question" />
  <java-symbol type="string" name="shutdown_progress" />
  <java-symbol type="string" name="sim_added_message" />
  <java-symbol type="string" name="sim_added_title" />
  <java-symbol type="string" name="sim_removed_message" />
  <java-symbol type="string" name="sim_removed_title" />
  <java-symbol type="string" name="sim_restart_button" />
  <java-symbol type="string" name="sipAddressTypeCustom" />
  <java-symbol type="string" name="sipAddressTypeHome" />
  <java-symbol type="string" name="sipAddressTypeOther" />
  <java-symbol type="string" name="sipAddressTypeWork" />
  <java-symbol type="string" name="default_sms_application" />
  <java-symbol type="string" name="default_browser" />
  <java-symbol type="string" name="sms_control_message" />
  <java-symbol type="string" name="sms_control_title" />
  <java-symbol type="string" name="sms_control_no" />
  <java-symbol type="string" name="sms_control_yes" />
  <java-symbol type="string" name="sms_short_code_confirm_allow" />
  <java-symbol type="string" name="sms_short_code_confirm_deny" />
  <java-symbol type="string" name="sms_short_code_confirm_always_allow" />
  <java-symbol type="string" name="sms_short_code_confirm_never_allow" />
  <java-symbol type="string" name="sms_short_code_confirm_message" />
  <java-symbol type="string" name="sms_short_code_details" />
  <java-symbol type="string" name="sms_premium_short_code_details" />
  <java-symbol type="string" name="sms_short_code_remember_undo_instruction" />
  <java-symbol type="string" name="submit" />
  <java-symbol type="string" name="sync_binding_label" />
  <java-symbol type="string" name="sync_do_nothing" />
  <java-symbol type="string" name="sync_really_delete" />
  <java-symbol type="string" name="sync_too_many_deletes_desc" />
  <java-symbol type="string" name="sync_undo_deletes" />
  <java-symbol type="string" name="time_of_day" />
  <java-symbol type="string" name="time_picker_decrement_hour_button" />
  <java-symbol type="string" name="time_picker_decrement_minute_button" />
  <java-symbol type="string" name="time_picker_decrement_set_am_button" />
  <java-symbol type="string" name="time_picker_dialog_title" />
  <java-symbol type="string" name="time_picker_increment_hour_button" />
  <java-symbol type="string" name="time_picker_increment_minute_button" />
  <java-symbol type="string" name="time_picker_increment_set_pm_button" />
  <java-symbol type="string" name="upload_file" />
  <java-symbol type="string" name="user_creation_account_exists" />
  <java-symbol type="string" name="user_creation_adding" />
  <java-symbol type="string" name="user_switched" />
  <java-symbol type="string" name="user_switching_message" />
  <java-symbol type="string" name="user_logging_out_message" />
  <java-symbol type="string" name="volume_alarm" />
  <java-symbol type="string" name="volume_icon_description_bluetooth" />
  <java-symbol type="string" name="volume_icon_description_incall" />
  <java-symbol type="string" name="volume_icon_description_media" />
  <java-symbol type="string" name="volume_icon_description_notification" />
  <java-symbol type="string" name="volume_icon_description_ringer" />
  <java-symbol type="string" name="volume_dialog_ringer_guidance_vibrate" />
  <java-symbol type="string" name="volume_dialog_ringer_guidance_silent" />
  <java-symbol type="string" name="wait" />
  <java-symbol type="string" name="webpage_unresponsive" />
  <java-symbol type="string" name="whichApplication" />
  <java-symbol type="string" name="whichHomeApplication" />
  <java-symbol type="string" name="wifi_available_sign_in" />
  <java-symbol type="string" name="network_available_sign_in" />
  <java-symbol type="string" name="network_available_sign_in_detailed" />
  <java-symbol type="string" name="network_switch_metered" />
  <java-symbol type="string" name="network_switch_metered_detail" />
  <java-symbol type="string" name="network_switch_metered_toast" />
  <java-symbol type="array" name="network_switch_type_name" />
  <java-symbol type="string" name="network_switch_type_name_unknown" />
  <java-symbol type="string" name="wifi_no_internet" />
  <java-symbol type="string" name="wifi_no_internet_detailed" />
  <java-symbol type="string" name="imei" />
  <java-symbol type="string" name="meid" />
  <java-symbol type="string" name="granularity_label_character" />
  <java-symbol type="string" name="granularity_label_word" />
  <java-symbol type="string" name="granularity_label_link" />
  <java-symbol type="string" name="granularity_label_line" />
  <java-symbol type="string" name="default_audio_route_id" />
  <java-symbol type="string" name="default_audio_route_name" />
  <java-symbol type="string" name="default_audio_route_name_dock_speakers" />
  <java-symbol type="string" name="default_audio_route_name_external_device" />
  <java-symbol type="string" name="default_audio_route_name_headphones" />
  <java-symbol type="string" name="default_audio_route_name_usb" />
  <java-symbol type="string" name="default_audio_route_category_name" />
  <java-symbol type="string" name="stk_cc_ss_to_dial" />
  <java-symbol type="string" name="stk_cc_ss_to_ss" />
  <java-symbol type="string" name="stk_cc_ss_to_ussd" />
  <java-symbol type="string" name="stk_cc_ss_to_dial_video" />
  <java-symbol type="string" name="stk_cc_ussd_to_dial" />
  <java-symbol type="string" name="stk_cc_ussd_to_ss" />
  <java-symbol type="string" name="stk_cc_ussd_to_ussd" />
  <java-symbol type="string" name="stk_cc_ussd_to_dial_video" />
  <java-symbol type="string" name="safe_media_volume_warning" />
  <java-symbol type="string" name="media_route_status_scanning" />
  <java-symbol type="string" name="media_route_status_connecting" />
  <java-symbol type="string" name="media_route_status_available" />
  <java-symbol type="string" name="media_route_status_not_available" />
  <java-symbol type="string" name="media_route_status_in_use" />
  <java-symbol type="string" name="owner_name" />
  <java-symbol type="string" name="guest_name" />
  <java-symbol type="string" name="config_chooseAccountActivity" />
  <java-symbol type="string" name="config_chooseTypeAndAccountActivity" />
  <java-symbol type="string" name="config_chooserActivity" />
  <java-symbol type="string" name="config_customResolverActivity" />
  <java-symbol type="string" name="config_appsAuthorizedForSharedAccounts" />
  <java-symbol type="string" name="error_message_title" />
  <java-symbol type="string" name="error_message_change_not_allowed" />
  <java-symbol type="string" name="action_bar_home_description_format" />
  <java-symbol type="string" name="action_bar_home_subtitle_description_format" />
  <java-symbol type="string" name="wireless_display_route_description" />
  <java-symbol type="string" name="user_owner_label" />
  <java-symbol type="string" name="user_owner_app_label" />
  <java-symbol type="string" name="managed_profile_label" />
  <java-symbol type="string" name="managed_profile_app_label" />
  <java-symbol type="string" name="managed_profile_label_badge" />
  <java-symbol type="string" name="managed_profile_label_badge_2" />
  <java-symbol type="string" name="managed_profile_label_badge_3" />
  <java-symbol type="string" name="clone_profile_label_badge" />
  <java-symbol type="string" name="mediasize_unknown_portrait" />
  <java-symbol type="string" name="mediasize_unknown_landscape" />
  <java-symbol type="string" name="mediasize_iso_a0" />
  <java-symbol type="string" name="mediasize_iso_a1" />
  <java-symbol type="string" name="mediasize_iso_a2" />
  <java-symbol type="string" name="mediasize_iso_a3" />
  <java-symbol type="string" name="mediasize_iso_a4" />
  <java-symbol type="string" name="mediasize_iso_a5" />
  <java-symbol type="string" name="mediasize_iso_a6" />
  <java-symbol type="string" name="mediasize_iso_a7" />
  <java-symbol type="string" name="mediasize_iso_a8" />
  <java-symbol type="string" name="mediasize_iso_a9" />
  <java-symbol type="string" name="mediasize_iso_a10" />
  <java-symbol type="string" name="mediasize_iso_b0" />
  <java-symbol type="string" name="mediasize_iso_b1" />
  <java-symbol type="string" name="mediasize_iso_b2" />
  <java-symbol type="string" name="mediasize_iso_b3" />
  <java-symbol type="string" name="mediasize_iso_b4" />
  <java-symbol type="string" name="mediasize_iso_b5" />
  <java-symbol type="string" name="mediasize_iso_b6" />
  <java-symbol type="string" name="mediasize_iso_b7" />
  <java-symbol type="string" name="mediasize_iso_b8" />
  <java-symbol type="string" name="mediasize_iso_b9" />
  <java-symbol type="string" name="mediasize_iso_b10" />
  <java-symbol type="string" name="mediasize_iso_c0" />
  <java-symbol type="string" name="mediasize_iso_c1" />
  <java-symbol type="string" name="mediasize_iso_c2" />
  <java-symbol type="string" name="mediasize_iso_c3" />
  <java-symbol type="string" name="mediasize_iso_c4" />
  <java-symbol type="string" name="mediasize_iso_c5" />
  <java-symbol type="string" name="mediasize_iso_c6" />
  <java-symbol type="string" name="mediasize_iso_c7" />
  <java-symbol type="string" name="mediasize_iso_c8" />
  <java-symbol type="string" name="mediasize_iso_c9" />
  <java-symbol type="string" name="mediasize_iso_c10" />
  <java-symbol type="string" name="mediasize_na_letter" />
  <java-symbol type="string" name="mediasize_na_gvrnmt_letter" />
  <java-symbol type="string" name="mediasize_na_legal" />
  <java-symbol type="string" name="mediasize_na_junior_legal" />
  <java-symbol type="string" name="mediasize_na_ledger" />
  <java-symbol type="string" name="mediasize_na_tabloid" />
  <java-symbol type="string" name="mediasize_na_index_3x5" />
  <java-symbol type="string" name="mediasize_na_index_4x6" />
  <java-symbol type="string" name="mediasize_na_index_5x8" />
  <java-symbol type="string" name="mediasize_na_monarch" />
  <java-symbol type="string" name="mediasize_na_quarto" />
  <java-symbol type="string" name="mediasize_na_foolscap" />
  <java-symbol type="string" name="mediasize_na_ansi_c" />
  <java-symbol type="string" name="mediasize_na_ansi_d" />
  <java-symbol type="string" name="mediasize_na_ansi_e" />
  <java-symbol type="string" name="mediasize_na_ansi_f" />
  <java-symbol type="string" name="mediasize_na_arch_a" />
  <java-symbol type="string" name="mediasize_na_arch_b" />
  <java-symbol type="string" name="mediasize_na_arch_c" />
  <java-symbol type="string" name="mediasize_na_arch_d" />
  <java-symbol type="string" name="mediasize_na_arch_e" />
  <java-symbol type="string" name="mediasize_na_arch_e1" />
  <java-symbol type="string" name="mediasize_na_super_b" />
  <java-symbol type="string" name="mediasize_chinese_roc_8k" />
  <java-symbol type="string" name="mediasize_chinese_roc_16k" />
  <java-symbol type="string" name="mediasize_chinese_prc_1" />
  <java-symbol type="string" name="mediasize_chinese_prc_2" />
  <java-symbol type="string" name="mediasize_chinese_prc_3" />
  <java-symbol type="string" name="mediasize_chinese_prc_4" />
  <java-symbol type="string" name="mediasize_chinese_prc_5" />
  <java-symbol type="string" name="mediasize_chinese_prc_6" />
  <java-symbol type="string" name="mediasize_chinese_prc_7" />
  <java-symbol type="string" name="mediasize_chinese_prc_8" />
  <java-symbol type="string" name="mediasize_chinese_prc_9" />
  <java-symbol type="string" name="mediasize_chinese_prc_10" />
  <java-symbol type="string" name="mediasize_chinese_prc_16k" />
  <java-symbol type="string" name="mediasize_chinese_om_pa_kai" />
  <java-symbol type="string" name="mediasize_chinese_om_dai_pa_kai" />
  <java-symbol type="string" name="mediasize_chinese_om_jurro_ku_kai" />
  <java-symbol type="string" name="mediasize_japanese_jis_b10" />
  <java-symbol type="string" name="mediasize_japanese_jis_b9" />
  <java-symbol type="string" name="mediasize_japanese_jis_b8" />
  <java-symbol type="string" name="mediasize_japanese_jis_b7" />
  <java-symbol type="string" name="mediasize_japanese_jis_b6" />
  <java-symbol type="string" name="mediasize_japanese_jis_b5" />
  <java-symbol type="string" name="mediasize_japanese_jis_b4" />
  <java-symbol type="string" name="mediasize_japanese_jis_b3" />
  <java-symbol type="string" name="mediasize_japanese_jis_b2" />
  <java-symbol type="string" name="mediasize_japanese_jis_b1" />
  <java-symbol type="string" name="mediasize_japanese_jis_b0" />
  <java-symbol type="string" name="mediasize_japanese_jis_exec" />
  <java-symbol type="string" name="mediasize_japanese_chou4" />
  <java-symbol type="string" name="mediasize_japanese_chou3" />
  <java-symbol type="string" name="mediasize_japanese_chou2" />
  <java-symbol type="string" name="mediasize_japanese_hagaki" />
  <java-symbol type="string" name="mediasize_japanese_oufuku" />
  <java-symbol type="string" name="mediasize_japanese_kahu" />
  <java-symbol type="string" name="mediasize_japanese_kaku2" />
  <java-symbol type="string" name="mediasize_japanese_you4" />
  <java-symbol type="string" name="mediasize_japanese_l" />
  <java-symbol type="string" name="network_partial_connectivity" />
  <java-symbol type="string" name="network_partial_connectivity_detailed" />
  <java-symbol type="string" name="reason_service_unavailable" />
  <java-symbol type="string" name="reason_unknown" />
  <java-symbol type="string" name="restr_pin_enter_admin_pin" />
  <java-symbol type="string" name="restr_pin_enter_pin" />
  <java-symbol type="string" name="restr_pin_incorrect" />
  <java-symbol type="string" name="restr_pin_try_later" />
  <java-symbol type="string" name="write_fail_reason_cancelled" />
  <java-symbol type="string" name="write_fail_reason_cannot_write" />
  <java-symbol type="string" name="ssl_ca_cert_noti_by_unknown" />
  <java-symbol type="string" name="ssl_ca_cert_noti_by_administrator" />
  <java-symbol type="string" name="ssl_ca_cert_noti_managed" />
  <java-symbol type="string" name="work_profile_deleted" />
  <java-symbol type="string" name="work_profile_deleted_details" />
  <java-symbol type="string" name="work_profile_deleted_description_dpm_wipe" />
  <java-symbol type="string" name="work_profile_deleted_reason_maximum_password_failure" />
  <java-symbol type="string" name="device_ownership_relinquished" />
  <java-symbol type="string" name="network_logging_notification_title" />
  <java-symbol type="string" name="network_logging_notification_text" />
  <java-symbol type="string" name="location_changed_notification_title" />
  <java-symbol type="string" name="location_changed_notification_text" />
  <java-symbol type="string" name="personal_apps_suspension_title" />
  <java-symbol type="string" name="personal_apps_suspension_soon_text" />
  <java-symbol type="string" name="personal_apps_suspension_text" />
  <java-symbol type="string" name="personal_apps_suspended_turn_profile_on" />
  <java-symbol type="string" name="work_profile_telephony_paused_title" />
  <java-symbol type="string" name="work_profile_telephony_paused_text" />
  <java-symbol type="string" name="work_profile_telephony_paused_turn_on_button" />
  <java-symbol type="string" name="notification_work_profile_content_description" />
  <java-symbol type="string" name="factory_reset_warning" />
  <java-symbol type="string" name="factory_reset_message" />
  <java-symbol type="string" name="lockscreen_transport_play_description" />
  <java-symbol type="string" name="lockscreen_transport_pause_description" />
  <java-symbol type="string" name="config_ethernet_tcp_buffers" />
  <java-symbol type="string" name="demo_starting_message" />
  <java-symbol type="string" name="demo_restarting_message" />
  <java-symbol type="string" name="conference_call" />
  <java-symbol type="string" name="tooltip_popup_title" />

  <java-symbol type="string" name="bugreport_countdown" />
  <java-symbol type="string" name="file_count" />
  <java-symbol type="string" name="last_num_days" />
  <java-symbol type="string" name="matches_found" />
  <java-symbol type="plurals" name="pinpuk_attempts" />
  <java-symbol type="string" name="ssl_ca_cert_warning" />

  <java-symbol type="array" name="carrier_properties" />
  <java-symbol type="array" name="config_sms_enabled_locking_shift_tables" />
  <java-symbol type="array" name="config_sms_enabled_single_shift_tables" />
  <java-symbol type="array" name="config_twoDigitNumberPattern" />
  <java-symbol type="array" name="networkAttributes" />
  <java-symbol type="array" name="preloaded_color_state_lists" />
  <java-symbol type="array" name="preloaded_drawables" />
  <java-symbol type="array" name="preloaded_freeform_multi_window_drawables" />
  <java-symbol type="array" name="sim_colors" />
  <java-symbol type="array" name="special_locale_codes" />
  <java-symbol type="array" name="special_locale_names" />
  <java-symbol type="array" name="supported_locales" />
  <java-symbol type="array" name="config_cdma_dun_supported_types" />
  <java-symbol type="array" name="config_disabledUntilUsedPreinstalledImes" />
  <java-symbol type="array" name="config_callBarringMMI" />
  <java-symbol type="array" name="config_callBarringMMI_for_ims" />
  <java-symbol type="array" name="config_globalActionsList" />
  <java-symbol type="array" name="config_telephonyEuiccDeviceCapabilities" />
  <java-symbol type="array" name="config_telephonyHardware" />
  <java-symbol type="array" name="config_keySystemUuidMapping" />
  <java-symbol type="array" name="required_apps_managed_user" />
  <java-symbol type="array" name="required_apps_managed_profile" />
  <java-symbol type="array" name="required_apps_managed_device" />
  <java-symbol type="array" name="disallowed_apps_managed_user" />
  <java-symbol type="array" name="disallowed_apps_managed_profile" />
  <java-symbol type="array" name="disallowed_apps_managed_device" />
  <java-symbol type="array" name="vendor_required_apps_managed_user" />
  <java-symbol type="array" name="vendor_required_apps_managed_profile" />
  <java-symbol type="array" name="vendor_required_apps_managed_device" />
  <java-symbol type="array" name="vendor_required_attestation_certificates" />
  <java-symbol type="string" name="vendor_required_attestation_revocation_list_url" />
  <java-symbol type="array" name="vendor_disallowed_apps_managed_user" />
  <java-symbol type="array" name="vendor_disallowed_apps_managed_profile" />
  <java-symbol type="array" name="vendor_disallowed_apps_managed_device" />
  <java-symbol type="array" name="cross_profile_apps" />
  <java-symbol type="array" name="vendor_cross_profile_apps" />
  <java-symbol type="array" name="policy_exempt_apps" />
  <java-symbol type="array" name="vendor_policy_exempt_apps" />
  <java-symbol type="array" name="cloneable_apps" />

  <java-symbol type="drawable" name="default_wallpaper" />
  <java-symbol type="drawable" name="default_lock_wallpaper" />
  <java-symbol type="drawable" name="indicator_input_error" />
  <java-symbol type="drawable" name="ic_file_copy" />
  <java-symbol type="drawable" name="popup_bottom_dark" />
  <java-symbol type="drawable" name="popup_bottom_bright" />
  <java-symbol type="drawable" name="popup_bottom_medium" />
  <java-symbol type="drawable" name="popup_center_dark" />
  <java-symbol type="drawable" name="popup_center_bright" />
  <java-symbol type="drawable" name="popup_full_dark" />
  <java-symbol type="drawable" name="popup_full_bright" />
  <java-symbol type="drawable" name="popup_top_dark" />
  <java-symbol type="drawable" name="popup_top_bright" />
  <java-symbol type="drawable" name="search_spinner" />
  <java-symbol type="drawable" name="sym_app_on_sd_unavailable_icon" />
  <java-symbol type="drawable" name="text_edit_side_paste_window" />
  <java-symbol type="drawable" name="text_edit_paste_window" />
  <java-symbol type="drawable" name="btn_check_off" />
  <java-symbol type="color" name="lock_pattern_view_regular_color" />
  <java-symbol type="color" name="lock_pattern_view_success_color" />
  <java-symbol type="dimen" name="lock_pattern_dot_line_width" />
  <java-symbol type="dimen" name="lock_pattern_dot_size" />
  <java-symbol type="dimen" name="lock_pattern_dot_size_activated" />
  <java-symbol type="dimen" name="lock_pattern_dot_hit_factor" />
  <java-symbol type="dimen" name="lock_pattern_fade_away_gradient_width" />
  <java-symbol type="integer" name="lock_pattern_line_fade_out_duration" />
  <java-symbol type="integer" name="lock_pattern_line_fade_out_delay" />
  <java-symbol type="drawable" name="clock_dial" />
  <java-symbol type="drawable" name="clock_hand_hour" />
  <java-symbol type="drawable" name="clock_hand_minute" />
  <java-symbol type="drawable" name="emo_im_angel" />
  <java-symbol type="drawable" name="emo_im_cool" />
  <java-symbol type="drawable" name="emo_im_crying" />
  <java-symbol type="drawable" name="emo_im_embarrassed" />
  <java-symbol type="drawable" name="emo_im_foot_in_mouth" />
  <java-symbol type="drawable" name="emo_im_happy" />
  <java-symbol type="drawable" name="emo_im_kissing" />
  <java-symbol type="drawable" name="emo_im_laughing" />
  <java-symbol type="drawable" name="emo_im_lips_are_sealed" />
  <java-symbol type="drawable" name="emo_im_money_mouth" />
  <java-symbol type="drawable" name="emo_im_sad" />
  <java-symbol type="drawable" name="emo_im_surprised" />
  <java-symbol type="drawable" name="emo_im_tongue_sticking_out" />
  <java-symbol type="drawable" name="emo_im_undecided" />
  <java-symbol type="drawable" name="emo_im_winking" />
  <java-symbol type="drawable" name="emo_im_wtf" />
  <java-symbol type="drawable" name="emo_im_yelling" />
  <java-symbol type="drawable" name="expander_close_holo_dark" />
  <java-symbol type="drawable" name="expander_open_holo_dark" />
  <java-symbol type="drawable" name="ic_audio_alarm" />
  <java-symbol type="drawable" name="ic_audio_alarm_mute" />
  <java-symbol type="drawable" name="ic_audio_media" />
  <java-symbol type="drawable" name="ic_audio_media_mute" />
  <java-symbol type="drawable" name="ic_audio_notification" />
  <java-symbol type="drawable" name="ic_audio_notification_mute" />
  <java-symbol type="drawable" name="ic_audio_ring_notif" />
  <java-symbol type="drawable" name="ic_audio_ring_notif_mute" />
  <java-symbol type="drawable" name="ic_audio_ring_notif_vibrate" />
  <java-symbol type="drawable" name="ic_audio_vol" />
  <java-symbol type="drawable" name="ic_audio_vol_mute" />
  <java-symbol type="drawable" name="ic_bullet_key_permission" />
  <java-symbol type="drawable" name="ic_check_circle_24px" />
  <java-symbol type="drawable" name="ic_contact_picture" />
  <java-symbol type="drawable" name="ic_dialog_usb" />
  <java-symbol type="drawable" name="ic_emergency" />
  <java-symbol type="drawable" name="ic_info_outline" />
  <java-symbol type="drawable" name="ic_media_stop" />
  <java-symbol type="drawable" name="ic_text_dot" />
  <java-symbol type="drawable" name="ic_print" />
  <java-symbol type="drawable" name="ic_print_error" />
  <java-symbol type="drawable" name="ic_lock" />
  <java-symbol type="drawable" name="ic_lock_open" />
  <java-symbol type="drawable" name="jog_dial_arrow_long_left_green" />
  <java-symbol type="drawable" name="jog_dial_arrow_long_right_red" />
  <java-symbol type="drawable" name="jog_dial_arrow_short_left_and_right" />
  <java-symbol type="drawable" name="jog_dial_bg" />
  <java-symbol type="drawable" name="jog_dial_dimple" />
  <java-symbol type="drawable" name="jog_dial_dimple_dim" />
  <java-symbol type="drawable" name="jog_tab_bar_left_generic" />
  <java-symbol type="drawable" name="jog_tab_bar_right_generic" />
  <java-symbol type="drawable" name="jog_tab_left_generic" />
  <java-symbol type="drawable" name="jog_tab_right_generic" />
  <java-symbol type="drawable" name="jog_tab_target_gray" />
  <java-symbol type="drawable" name="picture_emergency" />
  <java-symbol type="drawable" name="platlogo" />
  <java-symbol type="drawable" name="stat_notify_sync_error" />
  <java-symbol type="drawable" name="stat_notify_wifi_in_range" />
  <java-symbol type="drawable" name="ic_wifi_signal_0" />
  <java-symbol type="drawable" name="ic_wifi_signal_1" />
  <java-symbol type="drawable" name="ic_wifi_signal_2" />
  <java-symbol type="drawable" name="ic_wifi_signal_3" />
  <java-symbol type="drawable" name="ic_wifi_signal_4" />
  <java-symbol type="drawable" name="ic_signal_wifi_transient_animation" />
  <java-symbol type="drawable" name="ic_hotspot_transient_animation" />
  <java-symbol type="drawable" name="ic_bluetooth_transient_animation" />
  <java-symbol type="drawable" name="ic_signal_cellular" />
  <java-symbol type="drawable" name="stat_notify_rssi_in_range" />
  <java-symbol type="drawable" name="stat_sys_gps_on" />
  <java-symbol type="drawable" name="stat_sys_tether_wifi" />
  <java-symbol type="drawable" name="stat_sys_certificate_info" />
  <java-symbol type="drawable" name="status_bar_background" />
  <java-symbol type="drawable" name="sym_keyboard_shift" />
  <java-symbol type="drawable" name="sym_keyboard_shift_locked" />
  <java-symbol type="drawable" name="sym_keyboard_return_holo" />
  <java-symbol type="drawable" name="tab_bottom_left" />
  <java-symbol type="drawable" name="tab_bottom_left_v4" />
  <java-symbol type="drawable" name="tab_bottom_right" />
  <java-symbol type="drawable" name="tab_bottom_right_v4" />
  <java-symbol type="drawable" name="tab_indicator_v4" />
  <java-symbol type="drawable" name="unknown_image" />
  <java-symbol type="drawable" name="unlock_default" />
  <java-symbol type="drawable" name="unlock_halo" />
  <java-symbol type="drawable" name="unlock_ring" />
  <java-symbol type="drawable" name="unlock_wave" />
  <java-symbol type="drawable" name="notification_template_icon_bg" />
  <java-symbol type="drawable" name="notification_template_icon_low_bg" />
  <java-symbol type="drawable" name="ic_media_route_off_holo_dark" />
  <java-symbol type="drawable" name="ic_media_route_off_holo_light" />
  <java-symbol type="drawable" name="cling_button" />
  <java-symbol type="drawable" name="cling_arrow_up" />
  <java-symbol type="drawable" name="cling_bg" />
  <java-symbol type="drawable" name="ic_corp_badge" />
  <java-symbol type="drawable" name="ic_corp_badge_color" />
  <java-symbol type="drawable" name="ic_corp_badge_case" />
  <java-symbol type="drawable" name="ic_corp_icon" />
  <java-symbol type="drawable" name="ic_corp_badge_off" />
  <java-symbol type="drawable" name="ic_corp_icon_badge_shadow" />
  <java-symbol type="drawable" name="ic_corp_icon_badge_color" />
  <java-symbol type="drawable" name="ic_corp_icon_badge_case" />
  <java-symbol type="drawable" name="ic_corp_user_badge" />
  <java-symbol type="drawable" name="ic_corp_badge_no_background" />
  <java-symbol type="drawable" name="ic_corp_statusbar_icon" />
  <java-symbol type="drawable" name="ic_test_badge_experiment" />
  <java-symbol type="drawable" name="ic_test_badge_no_background" />
  <java-symbol type="drawable" name="ic_test_icon_badge_experiment" />
  <java-symbol type="drawable" name="ic_instant_icon_badge_bolt" />
  <java-symbol type="drawable" name="emulator_circular_window_overlay" />
  <java-symbol type="drawable" name="ic_qs_battery_saver" />
  <java-symbol type="drawable" name="ic_qs_bluetooth" />
  <java-symbol type="drawable" name="ic_qs_airplane" />
  <java-symbol type="drawable" name="ic_qs_flashlight" />
  <java-symbol type="drawable" name="ic_qs_auto_rotate" />
  <java-symbol type="drawable" name="ic_qs_dnd" />
  <java-symbol type="drawable" name="ic_qs_one_handed_mode" />
  <java-symbol type="drawable" name="ic_clone_icon_badge" />
  <java-symbol type="drawable" name="ic_clone_badge" />

  <java-symbol type="drawable" name="sim_light_blue" />
  <java-symbol type="drawable" name="sim_light_green" />
  <java-symbol type="drawable" name="sim_light_orange" />
  <java-symbol type="drawable" name="sim_light_purple" />
  <java-symbol type="drawable" name="sim_dark_blue" />
  <java-symbol type="drawable" name="sim_dark_green" />
  <java-symbol type="drawable" name="sim_dark_orange" />
  <java-symbol type="drawable" name="sim_dark_purple" />

  <java-symbol type="drawable" name="ic_sim_card_multi_24px_clr" />
  <java-symbol type="drawable" name="ic_sim_card_multi_48px_clr" />
  <java-symbol type="drawable" name="ic_signal_cellular_alt_24px" />

  <java-symbol type="drawable" name="btn_borderless_rect" />
  <java-symbol type="drawable" name="ic_phone" />
  <java-symbol type="drawable" name="ic_phone_disabled" />
  <java-symbol type="drawable" name="ic_bt_headphones_a2dp" />
  <java-symbol type="drawable" name="ic_bt_headset_hfp" />
  <java-symbol type="drawable" name="ic_bt_hearing_aid" />
  <java-symbol type="drawable" name="ic_bt_laptop" />
  <java-symbol type="drawable" name="ic_bt_misc_hid" />
  <java-symbol type="drawable" name="ic_bt_network_pan" />
  <java-symbol type="drawable" name="ic_bt_pointing_hid" />
  <java-symbol type="drawable" name="ic_expand_more" />
  <java-symbol type="drawable" name="ic_lockscreen_ime" />
  <java-symbol type="drawable" name="ic_menu" />
  <java-symbol type="drawable" name="ic_minus" />
  <java-symbol type="drawable" name="ic_mode_edit" />
  <java-symbol type="drawable" name="ic_plus" />
  <java-symbol type="drawable" name="ic_qs_night_display_on" />
  <java-symbol type="drawable" name="ic_settings_bluetooth" />
  <java-symbol type="drawable" name="ic_settings_print" />
  <java-symbol type="drawable" name="ic_signal_location" />
  <java-symbol type="drawable" name="ic_info_outline_24" />
  <java-symbol type="drawable" name="ic_qs_ui_mode_night" />

  <java-symbol type="drawable" name="stat_notify_mmcc_indication_icn" />
  <java-symbol type="drawable" name="autofilled_highlight"/>
  <java-symbol type="drawable" name="ic_camera" />
  <java-symbol type="drawable" name="ic_mic" />
  <java-symbol type="drawable" name="ic_alert_window_layer" />
  <java-symbol type="drawable" name="ic_feedback_indicator" />
  <java-symbol type="drawable" name="ic_feedback_alerted" />
  <java-symbol type="drawable" name="ic_feedback_silenced" />
  <java-symbol type="drawable" name="ic_feedback_uprank" />
  <java-symbol type="drawable" name="ic_feedback_downrank" />

  <java-symbol type="drawable" name="ic_account_circle" />
  <java-symbol type="drawable" name="ic_dual_screen" />
  <java-symbol type="drawable" name="ic_thermostat" />
  <java-symbol type="color" name="user_icon_1" />
  <java-symbol type="color" name="user_icon_2" />
  <java-symbol type="color" name="user_icon_3" />
  <java-symbol type="color" name="user_icon_4" />
  <java-symbol type="color" name="user_icon_5" />
  <java-symbol type="color" name="user_icon_6" />
  <java-symbol type="color" name="user_icon_7" />
  <java-symbol type="color" name="user_icon_8" />
  <java-symbol type="color" name="user_icon_default_gray" />
  <java-symbol type="color" name="user_icon_default_white" />
  <java-symbol type="color" name="profile_badge_1" />
  <java-symbol type="color" name="profile_badge_2" />
  <java-symbol type="color" name="profile_badge_3" />
  <java-symbol type="color" name="profile_badge_1_dark" />
  <java-symbol type="color" name="profile_badge_2_dark" />
  <java-symbol type="color" name="profile_badge_3_dark" />
  <java-symbol type="color" name="instant_app_badge" />

  <java-symbol type="layout" name="action_bar_home" />
  <java-symbol type="layout" name="action_bar_title_item" />
  <java-symbol type="layout" name="action_menu_item_layout" />
  <java-symbol type="layout" name="action_menu_layout" />
  <java-symbol type="layout" name="action_mode_close_item" />
  <java-symbol type="layout" name="alert_dialog" />
  <java-symbol type="layout" name="cascading_menu_item_layout" />
  <java-symbol type="layout" name="cascading_menu_item_layout_material" />
  <java-symbol type="layout" name="choose_account" />
  <java-symbol type="layout" name="choose_account_row" />
  <java-symbol type="layout" name="choose_account_type" />
  <java-symbol type="layout" name="choose_type_and_account" />
  <java-symbol type="layout" name="grant_credentials_permission" />
  <java-symbol type="layout" name="number_picker" />
  <java-symbol type="layout" name="permissions_package_list_item" />
  <java-symbol type="layout" name="popup_menu_item_layout" />
  <java-symbol type="layout" name="popup_menu_header_item_layout" />
  <java-symbol type="layout" name="remote_views_adapter_default_loading_view" />
  <java-symbol type="layout" name="search_bar" />
  <java-symbol type="layout" name="search_dropdown_item_icons_2line" />
  <java-symbol type="layout" name="search_view" />
  <java-symbol type="layout" name="select_dialog" />
  <java-symbol type="layout" name="simple_dropdown_hint" />
  <java-symbol type="layout" name="status_bar_latest_event_content" />
  <java-symbol type="layout" name="system_user_home" />
  <java-symbol type="layout" name="text_edit_action_popup_text" />
  <java-symbol type="layout" name="text_drag_thumbnail" />
  <java-symbol type="layout" name="typing_filter" />
  <java-symbol type="layout" name="activity_chooser_view" />
  <java-symbol type="layout" name="activity_chooser_view_list_item" />
  <java-symbol type="layout" name="activity_list" />
  <java-symbol type="layout" name="activity_list_item_2" />
  <java-symbol type="layout" name="alert_dialog_progress" />
  <java-symbol type="layout" name="always_use_checkbox" />
  <java-symbol type="layout" name="app_permission_item" />
  <java-symbol type="layout" name="app_permission_item_money" />
  <java-symbol type="layout" name="app_permission_item_old" />
  <java-symbol type="layout" name="app_perms_summary" />
  <java-symbol type="layout" name="calendar_view" />
  <java-symbol type="layout" name="character_picker" />
  <java-symbol type="layout" name="character_picker_button" />
  <java-symbol type="layout" name="date_picker_legacy" />
  <java-symbol type="layout" name="date_picker_dialog" />
  <java-symbol type="layout" name="expanded_menu_layout" />
  <java-symbol type="layout" name="fragment_bread_crumb_item" />
  <java-symbol type="layout" name="fragment_bread_crumbs" />
  <java-symbol type="layout" name="heavy_weight_switcher" />
  <java-symbol type="layout" name="icon_menu_item_layout" />
  <java-symbol type="layout" name="icon_menu_layout" />
  <java-symbol type="layout" name="input_method" />
  <java-symbol type="layout" name="input_method_extract_view" />
  <java-symbol type="layout" name="input_method_switch_item" />
  <java-symbol type="layout" name="input_method_switch_dialog_title" />
  <java-symbol type="layout" name="js_prompt" />
  <java-symbol type="layout" name="list_content_simple" />
  <java-symbol type="layout" name="list_menu_item_checkbox" />
  <java-symbol type="layout" name="list_menu_item_icon" />
  <java-symbol type="layout" name="list_menu_item_fixed_size_icon" />
  <java-symbol type="layout" name="list_menu_item_layout" />
  <java-symbol type="layout" name="list_menu_item_radio" />
  <java-symbol type="layout" name="locale_picker_item" />
  <java-symbol type="layout" name="media_controller" />
  <java-symbol type="layout" name="overlay_display_window" />
  <java-symbol type="layout" name="preference" />
  <java-symbol type="layout" name="preference_header_item" />
  <java-symbol type="layout" name="preference_list_content" />
  <java-symbol type="layout" name="preference_list_content_single" />
  <java-symbol type="layout" name="preference_list_fragment" />
  <java-symbol type="layout" name="preference_widget_seekbar" />
  <java-symbol type="layout" name="progress_dialog" />
  <java-symbol type="layout" name="resolve_list_item" />
  <java-symbol type="layout" name="select_dialog_singlechoice_holo" />
  <java-symbol type="layout" name="ssl_certificate" />
  <java-symbol type="layout" name="tab_content" />
  <java-symbol type="layout" name="tab_indicator_holo" />
  <java-symbol type="layout" name="textview_hint" />
  <java-symbol type="layout" name="time_picker_legacy" />
  <java-symbol type="layout" name="time_picker_dialog" />
  <java-symbol type="layout" name="tooltip" />
  <java-symbol type="layout" name="transient_notification" />
  <java-symbol type="layout" name="transient_notification_with_icon" />
  <java-symbol type="layout" name="voice_interaction_session" />
  <java-symbol type="layout" name="web_text_view_dropdown" />
  <java-symbol type="layout" name="webview_find" />
  <java-symbol type="layout" name="webview_select_singlechoice" />
  <java-symbol type="layout" name="zoom_container" />
  <java-symbol type="layout" name="zoom_controls" />
  <java-symbol type="layout" name="zoom_magnify" />
  <java-symbol type="layout" name="notification_intruder_content" />
  <java-symbol type="layout" name="sms_short_code_confirmation_dialog" />
  <java-symbol type="layout" name="action_bar_up_container" />
  <java-symbol type="layout" name="app_not_authorized" />
  <java-symbol type="layout" name="restrictions_pin_challenge" />
  <java-symbol type="layout" name="restrictions_pin_setup" />
  <java-symbol type="layout" name="immersive_mode_cling" />
  <java-symbol type="layout" name="user_switching_dialog" />
  <java-symbol type="layout" name="common_tab_settings" />
  <java-symbol type="layout" name="resolver_list_per_profile" />
  <java-symbol type="layout" name="chooser_list_per_profile" />
  <java-symbol type="layout" name="resolver_empty_states" />
  <java-symbol type="id" name="open_cross_profile" />
  <java-symbol type="string" name="miniresolver_open_in_personal" />
  <java-symbol type="string" name="miniresolver_open_in_work" />
  <java-symbol type="string" name="miniresolver_open_work" />
  <java-symbol type="string" name="miniresolver_use_personal_browser" />
  <java-symbol type="string" name="miniresolver_use_work_browser" />
  <java-symbol type="string" name="miniresolver_call_in_work" />
  <java-symbol type="string" name="miniresolver_switch_to_work" />
  <java-symbol type="string" name="miniresolver_call" />
  <java-symbol type="string" name="miniresolver_switch" />
  <java-symbol type="string" name="miniresolver_call_information" />
  <java-symbol type="string" name="miniresolver_sms_information" />
  <java-symbol type="id" name="miniresolver_info_section" />
  <java-symbol type="id" name="miniresolver_info_section_text" />
  <java-symbol type="id" name="button_open" />
  <java-symbol type="id" name="use_same_profile_browser" />

  <java-symbol type="anim" name="slide_in_child_bottom" />
  <java-symbol type="anim" name="slide_in_right" />
  <java-symbol type="anim" name="slide_out_left" />

  <java-symbol type="menu" name="webview_copy" />
  <java-symbol type="menu" name="webview_find" />

  <java-symbol type="xml" name="password_kbd_qwerty" />
  <java-symbol type="xml" name="autotext" />
  <java-symbol type="xml" name="password_kbd_numeric" />
  <java-symbol type="xml" name="password_kbd_qwerty_shifted" />
  <java-symbol type="xml" name="password_kbd_symbols" />
  <java-symbol type="xml" name="password_kbd_symbols_shift" />
  <java-symbol type="xml" name="power_profile" />
  <java-symbol type="xml" name="power_profile_test" />
  <java-symbol type="xml" name="irq_device_map" />
  <java-symbol type="xml" name="sms_short_codes" />
  <java-symbol type="xml" name="audio_assets" />
  <java-symbol type="xml" name="global_keys" />
  <java-symbol type="xml" name="default_zen_mode_config" />
  <java-symbol type="xml" name="sms_7bit_translation_table" />
  <java-symbol type="xml" name="color_extraction" />

  <java-symbol type="raw" name="color_fade_vert" />
  <java-symbol type="raw" name="color_fade_frag" />
  <java-symbol type="raw" name="loaderror" />
  <java-symbol type="raw" name="nodomain" />

  <java-symbol type="style" name="Animation.DropDownUp" />
  <java-symbol type="style" name="Animation.DropDownDown" />
  <java-symbol type="style" name="Animation.PopupWindow" />
  <java-symbol type="style" name="Animation.Tooltip" />
  <java-symbol type="style" name="Animation.TypingFilter" />
  <java-symbol type="style" name="Animation.TypingFilterRestore" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.Alert" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.Alert" />
  <java-symbol type="style" name="Theme.Dialog.Alert" />
  <java-symbol type="style" name="Theme.Holo.Dialog.Alert" />
  <java-symbol type="style" name="Theme.Holo.Light.Dialog.Alert" />
  <java-symbol type="style" name="ActiveWallpaperSettings" />
  <java-symbol type="style" name="Animation.InputMethodFancy" />
  <java-symbol type="style" name="Animation.Wallpaper" />
  <java-symbol type="style" name="Animation.ZoomButtons" />
  <java-symbol type="style" name="PreviewWallpaperSettings" />
  <java-symbol type="style" name="TextAppearance.SlidingTabActive" />
  <java-symbol type="style" name="TextAppearance.SlidingTabNormal" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.NoFrame" />
  <java-symbol type="style" name="Theme.IconMenu" />
  <java-symbol type="style" name="Theme.Dream" />
  <java-symbol type="style" name="Theme.DeviceDefault.VoiceInteractionSession" />
  <java-symbol type="style" name="Pointer" />
  <java-symbol type="style" name="LargePointer" />
  <java-symbol type="style" name="TextAppearance.DeviceDefault.Notification.Title" />
  <java-symbol type="style" name="TextAppearance.DeviceDefault.Notification.Info" />

  <java-symbol type="attr" name="mediaRouteButtonStyle" />
  <java-symbol type="attr" name="externalRouteEnabledDrawable" />
  <java-symbol type="layout" name="media_route_chooser_dialog" />
  <java-symbol type="layout" name="media_route_controller_dialog" />
  <java-symbol type="layout" name="media_route_list_item" />
  <java-symbol type="id" name="media_route_list" />
  <java-symbol type="id" name="media_route_volume_layout" />
  <java-symbol type="id" name="media_route_volume_slider" />
  <java-symbol type="id" name="media_route_control_frame" />
  <java-symbol type="id" name="media_route_extended_settings_button" />
  <java-symbol type="id" name="media_route_progress_bar" />
  <java-symbol type="string" name="media_route_chooser_title" />
  <java-symbol type="string" name="media_route_chooser_title_for_remote_display" />
  <java-symbol type="string" name="media_route_controller_disconnect" />
  <java-symbol type="string" name="bluetooth_a2dp_audio_route_id" />
  <java-symbol type="string" name="bluetooth_a2dp_audio_route_name" />

  <java-symbol type="dimen" name="config_minScalingSpan" />
  <java-symbol type="dimen" name="config_minScalingTouchMajor" />

  <!-- From android.policy -->
  <java-symbol type="anim" name="app_starting_exit" />
  <java-symbol type="anim" name="fade_in" />
  <java-symbol type="anim" name="fade_out" />
  <java-symbol type="anim" name="voice_activity_close_exit" />
  <java-symbol type="anim" name="voice_activity_close_enter" />
  <java-symbol type="anim" name="voice_activity_open_exit" />
  <java-symbol type="anim" name="voice_activity_open_enter" />
  <java-symbol type="anim" name="task_open_exit" />
  <java-symbol type="anim" name="task_open_enter" />
  <java-symbol type="anim" name="cross_profile_apps_thumbnail_enter" />
  <java-symbol type="anim" name="task_open_enter_cross_profile_apps" />
  <java-symbol type="anim" name="activity_translucent_open_enter" />
  <java-symbol type="anim" name="activity_translucent_close_exit" />
  <java-symbol type="anim" name="activity_open_enter" />
  <java-symbol type="anim" name="activity_open_exit" />
  <java-symbol type="anim" name="activity_close_enter" />
  <java-symbol type="anim" name="activity_close_exit" />
  <java-symbol type="anim" name="task_fragment_close_enter" />
  <java-symbol type="anim" name="task_fragment_close_exit" />
  <java-symbol type="anim" name="task_fragment_open_enter" />
  <java-symbol type="anim" name="task_fragment_open_exit" />
  <java-symbol type="anim" name="task_fragment_clear_top_close_enter" />
  <java-symbol type="anim" name="task_fragment_clear_top_close_exit" />
  <java-symbol type="anim" name="task_fragment_clear_top_open_enter" />
  <java-symbol type="anim" name="task_fragment_clear_top_open_exit" />

  <java-symbol type="array" name="config_autoRotationTiltTolerance" />
  <java-symbol type="array" name="config_longPressVibePattern" />
  <java-symbol type="array" name="config_virtualKeyVibePattern" />
  <java-symbol type="array" name="config_doubleClickVibePattern" />
  <java-symbol type="array" name="config_safeModeEnabledVibePattern" />
  <java-symbol type="attr" name="actionModePopupWindowStyle" />
  <java-symbol type="attr" name="dialogCustomTitleDecorLayout" />
  <java-symbol type="attr" name="dialogTitleDecorLayout" />
  <java-symbol type="attr" name="dialogTitleIconsDecorLayout" />
  <java-symbol type="bool" name="config_allowAllRotations" />
  <java-symbol type="bool" name="config_useCurrentRotationOnRotationLockChange"/>
  <java-symbol type="bool" name="config_annoy_dianne" />
  <java-symbol type="bool" name="config_startDreamImmediatelyOnDock" />
  <java-symbol type="bool" name="config_carDockEnablesAccelerometer" />
  <java-symbol type="bool" name="config_customUserSwitchUi" />
  <java-symbol type="bool" name="config_deskDockEnablesAccelerometer" />
  <java-symbol type="bool" name="config_disableMenuKeyInLockScreen" />
  <java-symbol type="bool" name="config_enableCarDockHomeLaunch" />
  <java-symbol type="bool" name="config_enableLockBeforeUnlockScreen" />
  <java-symbol type="bool" name="config_enableLockScreenRotation" />
  <java-symbol type="bool" name="config_remoteInsetsControllerControlsSystemBars" />
  <java-symbol type="bool" name="config_remoteInsetsControllerSystemBarsCanBeShownByUserAction" />
  <java-symbol type="bool" name="config_lidControlsScreenLock" />
  <java-symbol type="bool" name="config_lidControlsSleep" />
  <java-symbol type="bool" name="config_lockDayNightMode" />
  <java-symbol type="bool" name="config_lockUiMode" />
  <java-symbol type="bool" name="config_reverseDefaultRotation" />
  <java-symbol type="bool" name="config_perDisplayFocusEnabled" />
  <java-symbol type="bool" name="config_enableMotionPrediction" />
  <java-symbol type="integer" name="config_motionPredictionOffsetNanos" />
  <java-symbol type="bool" name="config_showNavigationBar" />
  <java-symbol type="bool" name="config_supportAutoRotation" />
  <java-symbol type="bool" name="config_allowRotationResolver" />
  <java-symbol type="bool" name="config_dockedStackDividerFreeSnapMode" />
  <java-symbol type="dimen" name="docked_stack_divider_thickness" />
  <java-symbol type="dimen" name="docked_stack_divider_insets" />
  <java-symbol type="dimen" name="docked_stack_minimize_thickness" />
  <java-symbol type="dimen" name="pip_minimized_visible_size" />
  <java-symbol type="integer" name="config_dockedStackDividerSnapMode" />
  <java-symbol type="fraction" name="docked_stack_divider_fixed_ratio" />
  <java-symbol type="fraction" name="thumbnail_fullscreen_scale" />
  <java-symbol type="integer" name="thumbnail_width_tv" />
  <java-symbol type="dimen" name="navigation_bar_height" />
  <java-symbol type="dimen" name="navigation_bar_height_landscape" />
  <java-symbol type="dimen" name="navigation_bar_height_studio_s_plus" />
  <java-symbol type="dimen" name="navigation_bar_width" />
  <java-symbol type="dimen" name="navigation_bar_frame_height" />
  <java-symbol type="dimen" name="navigation_bar_frame_height_landscape" />
  <java-symbol type="dimen" name="navigation_bar_frame_height_studio_s_plus" />
  <java-symbol type="dimen" name="navigation_bar_gesture_height" />
  <java-symbol type="dimen" name="navigation_bar_gesture_larger_height" />
  <java-symbol type="dimen" name="navigation_bar_height_car_mode" />
  <java-symbol type="dimen" name="navigation_bar_height_landscape_car_mode" />
  <java-symbol type="dimen" name="navigation_bar_width_car_mode" />
  <java-symbol type="dimen" name="taskbar_frame_height" />
  <java-symbol type="dimen" name="status_bar_height" />
  <java-symbol type="dimen" name="display_cutout_touchable_region_size" />
  <java-symbol type="dimen" name="system_gestures_start_threshold" />
  <java-symbol type="dimen" name="quick_qs_offset_height" />
  <java-symbol type="drawable" name="ic_jog_dial_sound_off" />
  <java-symbol type="drawable" name="ic_jog_dial_sound_on" />
  <java-symbol type="drawable" name="ic_jog_dial_unlock" />
  <java-symbol type="drawable" name="ic_jog_dial_vibrate_on" />
  <java-symbol type="drawable" name="ic_lock_airplane_mode" />
  <java-symbol type="drawable" name="ic_lock_airplane_mode_off" />
  <java-symbol type="drawable" name="ic_menu_cc" />
  <java-symbol type="drawable" name="jog_tab_bar_left_unlock" />
  <java-symbol type="drawable" name="jog_tab_bar_right_sound_off" />
  <java-symbol type="drawable" name="jog_tab_bar_right_sound_on" />
  <java-symbol type="drawable" name="jog_tab_left_unlock" />
  <java-symbol type="drawable" name="jog_tab_right_sound_off" />
  <java-symbol type="drawable" name="jog_tab_right_sound_on" />
  <java-symbol type="drawable" name="jog_tab_target_green" />
  <java-symbol type="drawable" name="jog_tab_target_yellow" />
  <java-symbol type="drawable" name="magnified_region_frame" />
  <java-symbol type="drawable" name="menu_background" />
  <java-symbol type="id" name="action_mode_bar_stub" />
  <java-symbol type="id" name="button0" />
  <java-symbol type="id" name="button4" />
  <java-symbol type="id" name="button5" />
  <java-symbol type="id" name="button6" />
  <java-symbol type="id" name="button7" />
  <java-symbol type="id" name="date" />
  <java-symbol type="id" name="eight" />
  <java-symbol type="id" name="five" />
  <java-symbol type="id" name="four" />
  <java-symbol type="id" name="icon_menu_presenter" />
  <java-symbol type="id" name="keyboard" />
  <java-symbol type="id" name="list_menu_presenter" />
  <java-symbol type="id" name="lock_screen" />
  <java-symbol type="id" name="nine" />
  <java-symbol type="id" name="no_applications_message" />
  <java-symbol type="id" name="ok" />
  <java-symbol type="id" name="one" />
  <java-symbol type="id" name="option1" />
  <java-symbol type="id" name="option2" />
  <java-symbol type="id" name="option3" />
  <java-symbol type="id" name="right_icon" />
  <java-symbol type="id" name="seven" />
  <java-symbol type="id" name="six" />
  <java-symbol type="id" name="status" />
  <java-symbol type="id" name="three" />
  <java-symbol type="id" name="title_container" />
  <java-symbol type="id" name="two" />
  <java-symbol type="id" name="zero" />
  <java-symbol type="integer" name="config_carDockRotation" />
  <java-symbol type="integer" name="config_defaultUiModeType" />
  <java-symbol type="integer" name="config_deskDockRotation" />
  <java-symbol type="integer" name="config_doubleTapOnHomeBehavior" />
  <java-symbol type="integer" name="config_lidKeyboardAccessibility" />
  <java-symbol type="integer" name="config_lidNavigationAccessibility" />
  <java-symbol type="integer" name="config_lidOpenRotation" />
  <java-symbol type="integer" name="config_longPressOnHomeBehavior" />
  <java-symbol type="layout" name="global_actions" />
  <java-symbol type="layout" name="global_actions_item" />
  <java-symbol type="layout" name="global_actions_silent_mode" />
  <java-symbol type="layout" name="recent_apps_dialog" />
  <java-symbol type="layout" name="screen_action_bar" />
  <java-symbol type="layout" name="screen_custom_title" />
  <java-symbol type="layout" name="screen_progress" />
  <java-symbol type="layout" name="screen_simple" />
  <java-symbol type="layout" name="screen_simple_overlay_action_mode" />
  <java-symbol type="layout" name="screen_title" />
  <java-symbol type="layout" name="screen_title_icons" />
  <java-symbol type="string" name="system_ui_date_pattern" />
  <java-symbol type="string" name="android_preparing_apk" />
  <java-symbol type="string" name="android_start_title" />
  <java-symbol type="string" name="android_upgrading_title" />
  <java-symbol type="string" name="bugreport_message" />
  <java-symbol type="string" name="bugreport_option_full_summary" />
  <java-symbol type="string" name="bugreport_option_full_title" />
  <java-symbol type="string" name="bugreport_option_interactive_summary" />
  <java-symbol type="string" name="bugreport_option_interactive_title" />
  <java-symbol type="string" name="bugreport_screenshot_failure_toast" />
  <java-symbol type="string" name="bugreport_screenshot_success_toast" />
  <java-symbol type="string" name="bugreport_status" />
  <java-symbol type="string" name="bugreport_title" />
  <java-symbol type="string" name="faceunlock_multiple_failures" />
  <java-symbol type="string" name="fp_power_button_bp_title" />
  <java-symbol type="string" name="fp_power_button_bp_message" />
  <java-symbol type="string" name="fp_power_button_bp_positive_button" />
  <java-symbol type="string" name="fp_power_button_bp_negative_button" />
  <java-symbol type="string" name="fp_power_button_enrollment_title" />
  <java-symbol type="string" name="fp_power_button_enrollment_message" />
  <java-symbol type="string" name="fp_power_button_enrollment_button_text" />
  <java-symbol type="string" name="global_actions" />
  <java-symbol type="string" name="global_action_power_off" />
  <java-symbol type="string" name="global_action_power_options" />
  <java-symbol type="string" name="global_action_restart" />
  <java-symbol type="string" name="global_actions_airplane_mode_off_status" />
  <java-symbol type="string" name="global_actions_airplane_mode_on_status" />
  <java-symbol type="string" name="global_actions_toggle_airplane_mode" />
  <java-symbol type="string" name="global_action_bug_report" />
  <java-symbol type="string" name="global_action_settings" />
  <java-symbol type="string" name="global_action_silent_mode_off_status" />
  <java-symbol type="string" name="global_action_silent_mode_on_status" />
  <java-symbol type="string" name="global_action_toggle_silent_mode" />
  <java-symbol type="string" name="global_action_lockdown" />
  <java-symbol type="string" name="global_action_voice_assist" />
  <java-symbol type="string" name="global_action_assist" />
  <java-symbol type="string" name="global_action_screenshot" />
  <java-symbol type="string" name="invalidPuk" />
  <java-symbol type="string" name="lockscreen_carrier_default" />
  <java-symbol type="style" name="Animation.LockScreen" />
  <java-symbol type="style" name="Theme.Dialog.RecentApplications" />
  <java-symbol type="style" name="Theme.ExpandedMenu" />
  <java-symbol type="string" name="forward_intent_to_owner" />
  <java-symbol type="string" name="forward_intent_to_work" />
  <java-symbol type="dimen" name="cross_profile_apps_thumbnail_size" />
  <java-symbol type="layout" name="splash_screen_view" />
  <java-symbol type="id" name="splashscreen_icon_view" />
  <java-symbol type="id" name="splashscreen_branding_view" />

  <!-- From services -->
  <java-symbol type="anim" name="screen_rotate_0_enter" />
  <java-symbol type="anim" name="screen_rotate_0_exit" />
  <java-symbol type="anim" name="screen_rotate_180_enter" />
  <java-symbol type="anim" name="screen_rotate_180_exit" />
  <java-symbol type="anim" name="screen_rotate_180_frame" />
  <java-symbol type="anim" name="screen_rotate_alpha"/>
  <java-symbol type="anim" name="screen_rotate_finish_enter" />
  <java-symbol type="anim" name="screen_rotate_finish_exit" />
  <java-symbol type="anim" name="screen_rotate_finish_frame" />
  <java-symbol type="anim" name="screen_rotate_minus_90_enter" />
  <java-symbol type="anim" name="screen_rotate_minus_90_exit" />
  <java-symbol type="anim" name="screen_rotate_plus_90_enter" />
  <java-symbol type="anim" name="screen_rotate_plus_90_exit" />
  <java-symbol type="anim" name="screen_rotate_start_enter" />
  <java-symbol type="anim" name="screen_rotate_start_exit" />
  <java-symbol type="anim" name="screen_rotate_start_frame" />
  <java-symbol type="anim" name="screen_user_exit" />
  <java-symbol type="anim" name="screen_user_enter" />
  <java-symbol type="anim" name="window_move_from_decor" />
  <java-symbol type="anim" name="rotation_animation_jump_exit" />
  <java-symbol type="anim" name="rotation_animation_xfade_exit" />
  <java-symbol type="anim" name="rotation_animation_enter" />
  <java-symbol type="anim" name="dream_activity_open_exit" />
  <java-symbol type="anim" name="dream_activity_open_enter" />
  <java-symbol type="anim" name="dream_activity_close_exit" />
  <java-symbol type="array" name="config_autoBrightnessButtonBacklightValues" />
  <java-symbol type="array" name="config_autoBrightnessLcdBacklightValues" />
  <java-symbol type="array" name="config_autoBrightnessLcdBacklightValues_doze" />
  <java-symbol type="array" name="config_autoBrightnessLevels" />
  <java-symbol type="array" name="config_autoBrightnessLevelsIdle" />
  <java-symbol type="array" name="config_autoKeyboardBacklightBrightnessValues" />
  <java-symbol type="array" name="config_autoKeyboardBacklightDecreaseLuxThreshold" />
  <java-symbol type="array" name="config_autoKeyboardBacklightIncreaseLuxThreshold" />
  <java-symbol type="array" name="config_ambientThresholdLevels" />
  <java-symbol type="array" name="config_ambientBrighteningThresholds" />
  <java-symbol type="array" name="config_ambientDarkeningThresholds" />
  <java-symbol type="array" name="config_screenThresholdLevels" />
  <java-symbol type="array" name="config_screenBrighteningThresholds" />
  <java-symbol type="array" name="config_screenDarkeningThresholds" />
  <java-symbol type="array" name="config_minimumBrightnessCurveLux" />
  <java-symbol type="array" name="config_minimumBrightnessCurveNits" />
  <java-symbol type="array" name="config_protectedNetworks" />
  <java-symbol type="array" name="config_statusBarIcons" />
  <java-symbol type="array" name="config_tether_bluetooth_regexs" />
  <java-symbol type="array" name="config_tether_dhcp_range" />
  <java-symbol type="array" name="config_tether_upstream_types" />
  <java-symbol type="array" name="config_tether_usb_regexs" />
  <java-symbol type="array" name="config_tether_wifi_regexs" />
  <java-symbol type="array" name="config_usbHostDenylist" />
  <java-symbol type="array" name="config_serialPorts" />
  <java-symbol type="array" name="radioAttributes" />
  <java-symbol type="array" name="config_oemUsbModeOverride" />
  <java-symbol type="array" name="config_locationProviderPackageNames" />
  <java-symbol type="array" name="config_locationDriverAssistancePackageNames" />
  <java-symbol type="array" name="config_locationExtraPackageNames" />
  <java-symbol type="array" name="config_testLocationProviders" />
  <java-symbol type="array" name="config_defaultNotificationVibePattern" />
  <java-symbol type="array" name="config_defaultNotificationVibeWaveform" />
  <java-symbol type="array" name="config_notificationFallbackVibePattern" />
  <java-symbol type="array" name="config_notificationFallbackVibeWaveform" />
  <java-symbol type="bool" name="config_enableServerNotificationEffectsForAutomotive" />
  <java-symbol type="bool" name="config_useAttentionLight" />
  <java-symbol type="bool" name="config_adaptive_sleep_available" />
  <java-symbol type="bool" name="config_camera_autorotate"/>
  <java-symbol type="bool" name="config_animateScreenLights" />
  <java-symbol type="bool" name="config_automatic_brightness_available" />
  <java-symbol type="bool" name="config_smart_battery_available" />
  <java-symbol type="bool" name="config_autoBrightnessResetAmbientLuxAfterWarmUp" />
  <java-symbol type="bool" name="config_notificationHeaderClickableForExpand" />
  <java-symbol type="bool" name="config_enableNightMode" />
  <java-symbol type="bool" name="config_tintNotificationActionButtons" />
  <java-symbol type="bool" name="config_dozeAfterScreenOffByDefault" />
  <java-symbol type="bool" name="config_enableActivityRecognitionHardwareOverlay" />
  <java-symbol type="bool" name="config_defaultAdasGnssLocationEnabled" />
  <java-symbol type="bool" name="config_enableFusedLocationOverlay" />
  <java-symbol type="bool" name="config_useGnssHardwareProvider" />
  <java-symbol type="bool" name="config_enableGeocoderOverlay" />
  <java-symbol type="bool" name="config_enableGeofenceOverlay" />
  <java-symbol type="bool" name="config_enableNetworkLocationOverlay" />
  <java-symbol type="bool" name="config_sf_limitedAlpha" />
  <java-symbol type="bool" name="config_unplugTurnsOnScreen" />
  <java-symbol type="bool" name="config_usbChargingMessage" />
  <java-symbol type="bool" name="config_skipScreenOnBrightnessRamp" />
  <java-symbol type="bool" name="config_allowAutoBrightnessWhileDozing" />
  <java-symbol type="bool" name="config_allowNormalBrightnessControllerFeature" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromUnplug" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromGesture" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromCameraLens" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromPowerKey" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromKey" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromMotion" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromMotionWhenNotDreaming" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromLidSwitch" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromDock" />
  <java-symbol type="bool" name="config_allowTheaterModeWakeFromWindowLayout" />
  <java-symbol type="bool" name="config_keepDreamingWhenUnplugging" />
  <java-symbol type="integer" name="config_keyguardDrawnTimeout" />
  <java-symbol type="bool" name="config_goToSleepOnButtonPressTheaterMode" />
  <java-symbol type="bool" name="config_supportLongPressPowerWhenNonInteractive" />
  <java-symbol type="bool" name="config_wimaxEnabled" />
  <java-symbol type="bool" name="show_ongoing_ime_switcher" />
  <java-symbol type="color" name="config_defaultNotificationColor" />
  <java-symbol type="color" name="decor_view_status_guard" />
  <java-symbol type="color" name="decor_view_status_guard_light" />
  <java-symbol type="drawable" name="ic_menu_refresh" />
  <java-symbol type="drawable" name="ic_settings" />
  <java-symbol type="drawable" name="ic_voice_search" />
  <java-symbol type="drawable" name="ic_action_assist_focused" />
  <java-symbol type="drawable" name="stat_notify_car_mode" />
  <java-symbol type="drawable" name="stat_notify_disabled_data" />
  <java-symbol type="drawable" name="stat_notify_disk_full" />
  <java-symbol type="drawable" name="stat_sys_adb" />
  <java-symbol type="drawable" name="stat_sys_battery" />
  <java-symbol type="drawable" name="stat_sys_battery_charge" />
  <java-symbol type="drawable" name="stat_sys_battery_unknown" />
  <java-symbol type="drawable" name="stat_sys_data_usb" />
  <java-symbol type="drawable" name="stat_sys_throttled" />
  <java-symbol type="drawable" name="vpn_connected" />
  <java-symbol type="drawable" name="vpn_disconnected" />
  <java-symbol type="id" name="ask_checkbox" />
  <java-symbol type="id" name="compat_checkbox" />
  <java-symbol type="id" name="original_app_icon" />
  <java-symbol type="id" name="original_message" />
  <java-symbol type="id" name="radio" />
  <java-symbol type="id" name="reask_hint" />
  <java-symbol type="id" name="replace_app_icon" />
  <java-symbol type="id" name="replace_message" />
  <java-symbol type="fraction" name="config_dimBehindFadeDuration" />
  <java-symbol type="dimen" name="default_minimal_size_resizable_task" />
  <java-symbol type="dimen" name="task_height_of_minimized_mode" />
  <java-symbol type="fraction" name="config_screenAutoBrightnessDozeScaleFactor" />
  <java-symbol type="bool" name="config_allowPriorityVibrationsInLowPowerMode" />
  <java-symbol type="fraction" name="config_autoBrightnessAdjustmentMaxGamma" />
  <java-symbol type="integer" name="config_autoBrightnessBrighteningLightDebounce"/>
  <java-symbol type="integer" name="config_autoBrightnessDarkeningLightDebounce"/>
  <java-symbol type="integer" name="config_autoBrightnessInitialLightSensorRate"/>
  <java-symbol type="integer" name="config_autoBrightnessLightSensorRate"/>
  <java-symbol type="integer" name="config_carDockKeepsScreenOn" />
  <java-symbol type="integer" name="config_criticalBatteryWarningLevel" />
  <java-symbol type="integer" name="config_datause_notification_type" />
  <java-symbol type="integer" name="config_datause_polling_period_sec" />
  <java-symbol type="integer" name="config_datause_threshold_bytes" />
  <java-symbol type="integer" name="config_datause_throttle_kbitsps" />
  <java-symbol type="integer" name="config_defaultNotificationLedOff" />
  <java-symbol type="integer" name="config_defaultNotificationLedOn" />
  <java-symbol type="integer" name="config_deskDockKeepsScreenOn" />
  <java-symbol type="integer" name="config_lightSensorWarmupTime" />
  <java-symbol type="integer" name="config_lowBatteryCloseWarningBump" />
  <java-symbol type="integer" name="config_lowBatteryWarningLevel" />
  <java-symbol type="integer" name="config_networkPolicyDefaultWarning" />
  <java-symbol type="integer" name="config_networkNotifySwitchType" />
  <java-symbol type="array" name="config_networkNotifySwitches" />
  <java-symbol type="integer" name="config_networkAvoidBadWifi" />
  <java-symbol type="integer" name="config_networkWakeupPacketMark" />
  <java-symbol type="integer" name="config_networkWakeupPacketMask" />
  <java-symbol type="integer" name="config_networkDefaultDailyMultipathQuotaBytes" />
  <java-symbol type="integer" name="config_networkMeteredMultipathPreference" />
  <java-symbol type="array" name="config_networkSupportedKeepaliveCount" />
  <java-symbol type="integer" name="config_reservedPrivilegedKeepaliveSlots" />
  <java-symbol type="integer" name="config_allowedUnprivilegedKeepalivePerUid" />
  <java-symbol type="integer" name="config_notificationsBatteryFullARGB" />
  <java-symbol type="integer" name="config_notificationsBatteryLedOff" />
  <java-symbol type="integer" name="config_notificationsBatteryLedOn" />
  <java-symbol type="integer" name="config_notificationsBatteryLowBehavior" />
  <java-symbol type="integer" name="config_notificationsBatteryLowARGB" />
  <java-symbol type="integer" name="config_notificationsBatteryMediumARGB" />
  <java-symbol type="integer" name="config_notificationsBatteryNearlyFullLevel" />
  <java-symbol type="integer" name="config_notificationServiceArchiveSize" />
  <java-symbol type="integer" name="config_previousVibrationsDumpLimit" />
  <java-symbol type="integer" name="config_defaultVibrationAmplitude" />
  <java-symbol type="dimen" name="config_hapticChannelMaxVibrationAmplitude" />
  <java-symbol type="integer" name="config_vibrationWaveformRampStepDuration" />
  <java-symbol type="integer" name="config_vibrationWaveformRampDownDuration" />
  <java-symbol type="integer" name="config_radioScanningTimeout" />
  <java-symbol type="integer" name="config_screenBrightnessSettingMinimum" />
  <java-symbol type="integer" name="config_screenBrightnessSettingMaximum" />
  <java-symbol type="integer" name="config_screenBrightnessSettingDefault" />
  <java-symbol type="dimen" name="config_screenBrightnessSettingMinimumFloat" />
  <java-symbol type="dimen" name="config_screenBrightnessSettingMaximumFloat" />
  <java-symbol type="dimen" name="config_screenBrightnessSettingDefaultFloat" />
  <java-symbol type="dimen" name="config_screenBrightnessDozeFloat" />
  <java-symbol type="dimen" name="config_screenBrightnessDimFloat" />
  <java-symbol type="dimen" name="config_screenBrightnessMinimumDimAmountFloat" />
  <java-symbol type="integer" name="config_screenBrightnessDark" />
  <java-symbol type="integer" name="config_screenBrightnessDim" />
  <java-symbol type="integer" name="config_screenBrightnessDoze" />
  <java-symbol type="integer" name="config_autoBrightnessShortTermModelTimeout" />
  <java-symbol type="integer" name="config_progressTimeoutFallbackHome" />
  <java-symbol type="integer" name="config_shutdownBatteryTemperature" />
  <java-symbol type="integer" name="config_undockedHdmiRotation" />
  <java-symbol type="integer" name="config_virtualKeyQuietTimeMillis" />
  <java-symbol type="integer" name="config_brightness_ramp_rate_fast" />
  <java-symbol type="integer" name="config_brightness_ramp_rate_slow" />
  <java-symbol type="integer" name="config_screen_rotation_color_transition" />
  <java-symbol type="layout" name="am_compat_mode_dialog" />
  <java-symbol type="layout" name="launch_warning" />
  <java-symbol type="layout" name="safe_mode" />
  <java-symbol type="layout" name="simple_list_item_2_single_choice" />
  <java-symbol type="layout" name="app_error_dialog" />
  <java-symbol type="string" name="accessibility_binding_label" />
  <java-symbol type="string" name="adb_active_notification_message" />
  <java-symbol type="string" name="adb_active_notification_title" />
  <java-symbol type="string" name="adbwifi_active_notification_message" />
  <java-symbol type="string" name="adbwifi_active_notification_title" />
  <java-symbol type="string" name="test_harness_mode_notification_title" />
  <java-symbol type="string" name="test_harness_mode_notification_message" />
  <java-symbol type="string" name="console_running_notification_title" />
  <java-symbol type="string" name="console_running_notification_message" />
  <java-symbol type="string" name="mte_override_notification_title" />
  <java-symbol type="string" name="mte_override_notification_message" />
  <java-symbol type="string" name="taking_remote_bugreport_notification_title" />
  <java-symbol type="string" name="share_remote_bugreport_notification_title" />
  <java-symbol type="string" name="sharing_remote_bugreport_notification_title" />
  <java-symbol type="string" name="share_remote_bugreport_notification_message_finished" />
  <java-symbol type="string" name="share_remote_bugreport_action" />
  <java-symbol type="string" name="decline_remote_bugreport_action" />
  <java-symbol type="string" name="aerr_application" />
  <java-symbol type="string" name="aerr_process" />
  <java-symbol type="string" name="aerr_application_repeated" />
  <java-symbol type="string" name="aerr_process_repeated" />
  <java-symbol type="string" name="android_upgrading_complete" />
  <java-symbol type="string" name="android_upgrading_starting_apps" />
  <java-symbol type="string" name="anr_activity_application" />
  <java-symbol type="string" name="anr_activity_process" />
  <java-symbol type="string" name="anr_application_process" />
  <java-symbol type="string" name="anr_process" />
  <java-symbol type="string" name="wait_activity_application" />
  <java-symbol type="string" name="wait_activity_process" />
  <java-symbol type="string" name="wait_application_process" />
  <java-symbol type="string" name="wait_anr_process" />
  <java-symbol type="string" name="aerr_quit_app" />
  <java-symbol type="string" name="anr_title" />
  <java-symbol type="string" name="car_mode_disable_notification_message" />
  <java-symbol type="string" name="car_mode_disable_notification_title" />
  <java-symbol type="string" name="chooser_wallpaper" />
  <java-symbol type="string" name="config_systemImageEditor" />
  <java-symbol type="string" name="config_datause_iface" />
  <java-symbol type="string" name="config_activityRecognitionHardwarePackageName" />
  <java-symbol type="string" name="config_fusedLocationProviderPackageName" />
  <java-symbol type="string" name="config_gnssLocationProviderPackageName" />
  <java-symbol type="string" name="config_geocoderProviderPackageName" />
  <java-symbol type="string" name="config_geofenceProviderPackageName" />
  <java-symbol type="string" name="config_networkLocationProviderPackageName" />
  <java-symbol type="string" name="config_wimaxManagerClassname" />
  <java-symbol type="string" name="config_wimaxNativeLibLocation" />
  <java-symbol type="string" name="config_wimaxServiceClassname" />
  <java-symbol type="string" name="config_wimaxServiceJarLocation" />
  <java-symbol type="string" name="config_wimaxStateTrackerClassname" />
  <java-symbol type="string" name="data_usage_limit_body" />
  <java-symbol type="string" name="data_usage_limit_snoozed_body" />
  <java-symbol type="string" name="data_usage_mobile_limit_snoozed_title" />
  <java-symbol type="string" name="data_usage_mobile_limit_title" />
  <java-symbol type="string" name="data_usage_restricted_body" />
  <java-symbol type="string" name="data_usage_restricted_title" />
  <java-symbol type="string" name="data_usage_warning_body" />
  <java-symbol type="string" name="data_usage_warning_title" />
  <java-symbol type="string" name="data_usage_wifi_limit_snoozed_title" />
  <java-symbol type="string" name="data_usage_wifi_limit_title" />
  <java-symbol type="string" name="data_usage_rapid_title" />
  <java-symbol type="string" name="data_usage_rapid_body" />
  <java-symbol type="string" name="data_usage_rapid_app_body" />
  <java-symbol type="string" name="default_wallpaper_component" />
  <java-symbol type="array" name="default_wallpaper_component_per_device_color" />
  <java-symbol type="string" name="device_storage_monitor_notification_channel" />
  <java-symbol type="string" name="dlg_ok" />
  <java-symbol type="string" name="dump_heap_notification" />
  <java-symbol type="string" name="dump_heap_ready_notification" />
  <java-symbol type="string" name="dump_heap_notification_detail" />
  <java-symbol type="string" name="dump_heap_text" />
  <java-symbol type="string" name="dump_heap_ready_text" />
  <java-symbol type="string" name="dump_heap_system_text" />
  <java-symbol type="string" name="dump_heap_title" />
  <java-symbol type="string" name="factorytest_failed" />
  <java-symbol type="string" name="factorytest_no_action" />
  <java-symbol type="string" name="factorytest_not_system" />
  <java-symbol type="string" name="factorytest_reboot" />
  <java-symbol type="string" name="hardware" />
  <java-symbol type="string" name="heavy_weight_notification" />
  <java-symbol type="string" name="heavy_weight_notification_detail" />
  <java-symbol type="string" name="image_wallpaper_component" />
  <java-symbol type="string" name="input_method_binding_label" />
  <java-symbol type="string" name="launch_warning_original" />
  <java-symbol type="string" name="launch_warning_replace" />
  <java-symbol type="string" name="launch_warning_title" />
  <java-symbol type="string" name="low_internal_storage_view_text" />
  <java-symbol type="string" name="low_internal_storage_view_text_no_boot" />
  <java-symbol type="string" name="low_internal_storage_view_title" />
  <java-symbol type="string" name="mmcc_authentication_reject" />
  <java-symbol type="string" name="mmcc_imsi_unknown_in_hlr" />
  <java-symbol type="string" name="mmcc_illegal_ms" />
  <java-symbol type="string" name="mmcc_illegal_me" />
  <java-symbol type="string" name="mmcc_authentication_reject_msim_template" />
  <java-symbol type="string" name="mmcc_imsi_unknown_in_hlr_msim_template" />
  <java-symbol type="string" name="mmcc_illegal_ms_msim_template" />
  <java-symbol type="string" name="mmcc_illegal_me_msim_template" />
  <java-symbol type="string" name="notification_listener_binding_label" />
  <java-symbol type="string" name="vr_listener_binding_label" />
  <java-symbol type="string" name="condition_provider_service_binding_label" />
  <java-symbol type="string" name="notification_ranker_binding_label" />
  <java-symbol type="string" name="report" />
  <java-symbol type="string" name="select_input_method" />
  <java-symbol type="string" name="select_keyboard_layout_notification_title" />
  <java-symbol type="string" name="select_multiple_keyboards_layout_notification_title" />
  <java-symbol type="string" name="select_keyboard_layout_notification_message" />
  <java-symbol type="string" name="smv_application" />
  <java-symbol type="string" name="smv_process" />
  <java-symbol type="string" name="adb_debugging_notification_channel_tv" />
  <java-symbol type="string" name="usb_accessory_notification_title" />
  <java-symbol type="string" name="usb_mtp_notification_title" />
  <java-symbol type="string" name="usb_charging_notification_title" />
  <java-symbol type="string" name="usb_notification_message" />
  <java-symbol type="string" name="usb_power_notification_message" />
  <java-symbol type="string" name="usb_ptp_notification_title" />
  <java-symbol type="string" name="usb_midi_notification_title" />
  <java-symbol type="string" name="usb_uvc_notification_title" />
  <java-symbol type="string" name="usb_tether_notification_title" />
  <java-symbol type="string" name="usb_supplying_notification_title" />
  <java-symbol type="string" name="usb_unsupported_audio_accessory_title" />
  <java-symbol type="string" name="usb_unsupported_audio_accessory_message" />
  <java-symbol type="string" name="usb_contaminant_detected_title" />
  <java-symbol type="string" name="usb_contaminant_detected_message" />
  <java-symbol type="string" name="usb_contaminant_not_detected_title" />
  <java-symbol type="string" name="usb_contaminant_not_detected_message" />
  <java-symbol type="string" name="config_UsbDeviceConnectionHandling_component" />
  <java-symbol type="string" name="vpn_text" />
  <java-symbol type="string" name="vpn_text_long" />
  <java-symbol type="string" name="vpn_title" />
  <java-symbol type="string" name="vpn_title_long" />
  <java-symbol type="string" name="vpn_lockdown_connecting" />
  <java-symbol type="string" name="vpn_lockdown_connected" />
  <java-symbol type="string" name="vpn_lockdown_disconnected" />
  <java-symbol type="string" name="vpn_lockdown_error" />
  <java-symbol type="string" name="vpn_lockdown_config" />
  <java-symbol type="string" name="wallpaper_binding_label" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.AppError" />
  <java-symbol type="style" name="Theme.Leanback.Dialog.Alert" />
  <java-symbol type="style" name="Theme.Toast" />
  <java-symbol type="xml" name="storage_list" />
  <java-symbol type="bool" name="config_dreamsSupported" />
  <java-symbol type="bool" name="config_dreamsEnabledByDefault" />
  <java-symbol type="bool" name="config_dreamsEnabledOnBattery" />
  <java-symbol type="bool" name="config_dreamsActivatedOnDockByDefault" />
  <java-symbol type="bool" name="config_dreamsActivatedOnSleepByDefault" />
  <java-symbol type="integer" name="config_dreamsBatteryLevelMinimumWhenPowered" />
  <java-symbol type="integer" name="config_dreamsBatteryLevelMinimumWhenNotPowered" />
  <java-symbol type="integer" name="config_dreamsBatteryLevelDrainCutoff" />
  <java-symbol type="string" name="config_dreamsDefaultComponent" />
  <java-symbol type="bool" name="config_dreamsDisabledByAmbientModeSuppressionConfig" />
  <java-symbol type="bool" name="config_dreamsOnlyEnabledForDockUser" />
  <java-symbol type="integer" name="config_dreamOpenAnimationDuration" />
  <java-symbol type="integer" name="config_dreamCloseAnimationDuration" />
  <java-symbol type="array" name="config_supportedDreamComplications" />
  <java-symbol type="array" name="config_disabledDreamComponents" />
  <java-symbol type="bool" name="config_dismissDreamOnActivityStart" />
  <java-symbol type="bool" name="config_resetScreenTimeoutOnUnexpectedDreamExit" />
  <java-symbol type="integer" name="config_dreamOverlayReconnectTimeoutMs" />
  <java-symbol type="integer" name="config_dreamOverlayMaxReconnectAttempts" />
  <java-symbol type="integer" name="config_minDreamOverlayDurationMs" />
  <java-symbol type="array" name="config_loggable_dream_prefixes" />
  <java-symbol type="string" name="config_dozeComponent" />
  <java-symbol type="string" name="enable_explore_by_touch_warning_title" />
  <java-symbol type="string" name="enable_explore_by_touch_warning_message" />
  <java-symbol type="bool" name="config_powerDecoupleAutoSuspendModeFromDisplay" />
  <java-symbol type="bool" name="config_powerDecoupleInteractiveModeFromDisplay" />
  <java-symbol type="integer" name="config_minimumScreenOffTimeout" />
  <java-symbol type="integer" name="config_maximumScreenDimDuration" />
  <java-symbol type="fraction" name="config_maximumScreenDimRatio" />
  <java-symbol type="integer" name="config_attentiveTimeout" />
  <java-symbol type="integer" name="config_attentiveWarningDuration" />
  <java-symbol type="string" name="config_customAdbPublicKeyConfirmationComponent" />
  <java-symbol type="string" name="config_customAdbPublicKeyConfirmationSecondaryUserComponent" />
  <java-symbol type="string" name="config_customAdbWifiNetworkConfirmationComponent" />
  <java-symbol type="string" name="config_customAdbWifiNetworkConfirmationSecondaryUserComponent" />
  <java-symbol type="string" name="config_customVpnConfirmDialogComponent" />
  <java-symbol type="string" name="config_customVpnAlwaysOnDisconnectedDialogComponent" />
  <java-symbol type="string" name="config_platformVpnConfirmDialogComponent" />
  <java-symbol type="string" name="config_carrierAppInstallDialogComponent" />
  <java-symbol type="string" name="config_credentialManagerDialogComponent" />
  <java-symbol type="string" name="config_credentialManagerReceiverComponent" />
  <java-symbol type="string" name="config_defaultNetworkScorerPackageName" />
  <java-symbol type="string" name="config_persistentDataPackageName" />
  <java-symbol type="string" name="config_deviceConfiguratorPackageName" />
  <java-symbol type="array" name="config_autoTimeSourcesPriority" />
  <java-symbol type="bool" name="config_enableGnssTimeUpdateService" />
  <java-symbol type="bool" name="config_enableGeolocationTimeZoneDetection" />
  <java-symbol type="bool" name="config_enablePrimaryLocationTimeZoneProvider" />
  <java-symbol type="string" name="config_primaryLocationTimeZoneProviderPackageName" />
  <java-symbol type="bool" name="config_enableSecondaryLocationTimeZoneProvider" />
  <java-symbol type="string" name="config_secondaryLocationTimeZoneProviderPackageName" />
  <java-symbol type="bool" name="config_supportTelephonyTimeZoneFallback" />
  <java-symbol type="bool" name="config_autoResetAirplaneMode" />
  <java-symbol type="string" name="config_notificationAccessConfirmationActivity" />
  <java-symbol type="bool" name="config_preventImeStartupUnlessTextEditor" />
  <java-symbol type="array" name="config_nonPreemptibleInputMethods" />
  <java-symbol type="bool" name="config_enhancedConfirmationModeEnabled" />
  <java-symbol type="bool" name="config_persistBrightnessNitsForDefaultDisplay" />
  <java-symbol type="bool" name="config_isPreApprovalRequestAvailable" />

  <java-symbol type="layout" name="resolver_list" />
  <java-symbol type="id" name="resolver_list" />
  <java-symbol type="id" name="button_once" />
  <java-symbol type="id" name="button_always" />
  <java-symbol type="integer" name="config_globalActionsKeyTimeout" />
  <java-symbol type="integer" name="config_screenshotChordKeyTimeout" />
  <java-symbol type="integer" name="config_maxResolverActivityColumns" />
  <java-symbol type="array" name="config_notificationSignalExtractors" />
  <java-symbol type="bool" name="config_notificationReviewPermissions" />

  <java-symbol type="layout" name="notification_material_action" />
  <java-symbol type="layout" name="notification_material_action_list" />
  <java-symbol type="layout" name="notification_material_action_tombstone" />
  <java-symbol type="layout" name="notification_template_material_base" />
  <java-symbol type="layout" name="notification_template_material_heads_up_base" />
  <java-symbol type="layout" name="notification_template_material_big_base" />
  <java-symbol type="layout" name="notification_template_material_big_picture" />
  <java-symbol type="layout" name="notification_template_material_inbox" />
  <java-symbol type="layout" name="notification_template_material_media" />
  <java-symbol type="layout" name="notification_template_material_big_media" />
  <java-symbol type="layout" name="notification_template_material_big_text" />
  <java-symbol type="layout" name="notification_template_header" />
  <java-symbol type="layout" name="notification_material_media_action" />
  <java-symbol type="color" name="notification_progress_background_color" />
  <java-symbol type="id" name="media_actions" />

  <java-symbol type="dimen" name="config_mediaMetadataBitmapMaxSize" />
  <java-symbol type="array" name="config_fontManagerServiceCerts" />

    <!-- From SystemUI -->
  <java-symbol type="anim" name="push_down_in" />
  <java-symbol type="anim" name="push_down_out" />
  <java-symbol type="anim" name="push_up_in" />
  <java-symbol type="anim" name="push_up_out" />
  <java-symbol type="anim" name="lock_screen_behind_enter" />
  <java-symbol type="anim" name="lock_screen_behind_enter_wallpaper" />
  <java-symbol type="anim" name="lock_screen_behind_enter_fade_in" />
  <java-symbol type="anim" name="lock_screen_behind_enter_subtle" />
  <java-symbol type="anim" name="lock_screen_wallpaper_exit" />
  <java-symbol type="anim" name="launch_task_behind_source" />
  <java-symbol type="anim" name="wallpaper_open_exit" />

  <java-symbol type="bool" name="config_alwaysUseCdmaRssi" />
  <java-symbol type="dimen" name="status_bar_icon_size" />
  <java-symbol type="dimen" name="status_bar_icon_size_sp" />
  <java-symbol type="dimen" name="status_bar_system_icon_size" />
  <java-symbol type="dimen" name="status_bar_system_icon_intrinsic_size" />
  <java-symbol type="drawable" name="list_selector_pressed_holo_dark" />
  <java-symbol type="drawable" name="scrubber_control_disabled_holo" />
  <java-symbol type="drawable" name="scrubber_control_selector_holo" />
  <java-symbol type="drawable" name="scrubber_progress_horizontal_holo_dark" />
  <java-symbol type="drawable" name="progress_small_material" />
  <java-symbol type="drawable" name="ic_chevron_end" />
  <java-symbol type="string" name="chooseUsbActivity" />
  <java-symbol type="string" name="ext_media_badremoval_notification_message" />
  <java-symbol type="string" name="ext_media_badremoval_notification_title" />
  <java-symbol type="string" name="ext_media_checking_notification_message" />
  <java-symbol type="string" name="ext_media_checking_notification_title" />
  <java-symbol type="string" name="ext_media_nomedia_notification_message" />
  <java-symbol type="string" name="ext_media_nomedia_notification_title" />
  <java-symbol type="string" name="ext_media_unmountable_notification_message" />
  <java-symbol type="string" name="ext_media_unmountable_notification_title" />
  <java-symbol type="string" name="ext_media_unmounting_notification_message" />
  <java-symbol type="string" name="ext_media_unmounting_notification_title" />
  <java-symbol type="string" name="ext_media_new_notification_message" />
  <java-symbol type="string" name="ext_media_ready_notification_message" />
  <java-symbol type="string" name="ext_media_init_action" />
  <java-symbol type="string" name="ext_media_unmount_action" />
  <java-symbol type="string" name="ext_media_browse_action" />
  <java-symbol type="string" name="ext_media_seamless_action" />
  <java-symbol type="string" name="ext_media_missing_title" />
  <java-symbol type="string" name="ext_media_missing_message" />
  <java-symbol type="string" name="ext_media_move_specific_title" />
  <java-symbol type="string" name="ext_media_move_title" />
  <java-symbol type="string" name="ext_media_move_success_title" />
  <java-symbol type="string" name="ext_media_move_success_message" />
  <java-symbol type="string" name="ext_media_move_failure_title" />
  <java-symbol type="string" name="ext_media_move_failure_message" />
  <java-symbol type="string" name="notification_feedback_indicator_alerted" />
  <java-symbol type="string" name="notification_feedback_indicator_silenced" />
  <java-symbol type="string" name="notification_feedback_indicator_promoted" />
  <java-symbol type="string" name="notification_feedback_indicator_demoted" />
  <java-symbol type="style" name="Animation.RecentApplications" />
  <java-symbol type="integer" name="dock_enter_exit_duration" />
  <java-symbol type="bool" name="config_battery_percentage_setting_available" />
  <java-symbol type="string" name="nas_upgrade_notification_title" />
  <java-symbol type="string" name="nas_upgrade_notification_content" />
  <java-symbol type="string" name="nas_upgrade_notification_enable_action" />
  <java-symbol type="string" name="nas_upgrade_notification_disable_action" />
  <java-symbol type="string" name="nas_upgrade_notification_learn_more_action" />
  <java-symbol type="string" name="nas_upgrade_notification_learn_more_content" />
  <java-symbol type="bool" name="config_settingsHelpLinksEnabled" />
  <java-symbol type="integer" name="config_activityDefaultDur" />
  <java-symbol type="integer" name="config_activityShortDur" />
  <java-symbol type="dimen" name="popup_enter_animation_from_y_delta" />
  <java-symbol type="dimen" name="popup_exit_animation_to_y_delta" />

  <!-- ImfTest -->
  <java-symbol type="layout" name="auto_complete_list" />

  <!-- From SettingsProvider -->
  <java-symbol type="raw" name="fallbackring" />

  <!-- From Settings -->
  <java-symbol type="array" name="config_mobile_hotspot_provision_app" />
  <java-symbol type="string" name="config_mobile_hotspot_provision_app_no_ui" />
  <java-symbol type="string" name="config_mobile_hotspot_provision_response" />
  <java-symbol type="integer" name="config_mobile_hotspot_provision_check_period" />
  <java-symbol type="string" name="config_wifi_tether_enable" />
  <java-symbol type="bool" name="config_intrusiveNotificationLed" />
  <java-symbol type="bool" name="config_notificationBadging" />
  <java-symbol type="bool" name="config_callNotificationActionColorsRequireColorized" />
  <java-symbol type="dimen" name="preference_fragment_padding_bottom" />
  <java-symbol type="dimen" name="preference_fragment_padding_side" />
  <java-symbol type="drawable" name="expander_ic_maximized" />
  <java-symbol type="drawable" name="expander_ic_minimized" />
  <java-symbol type="drawable" name="ic_menu_archive" />
  <java-symbol type="drawable" name="ic_menu_goto" />
  <java-symbol type="drawable" name="ic_settings_language" />
  <java-symbol type="drawable" name="title_bar_medium" />
  <java-symbol type="id" name="body" />
  <java-symbol type="string" name="fast_scroll_alphabet" />
  <java-symbol type="string" name="ssl_certificate" />

  <!-- From Phone -->
  <java-symbol type="bool" name="config_built_in_sip_phone" />
  <java-symbol type="id" name="maximize_window" />
  <java-symbol type="id" name="close_window" />
  <java-symbol type="layout" name="decor_caption" />
  <java-symbol type="drawable" name="decor_caption_title_focused" />
  <java-symbol type="drawable" name="decor_close_button_dark" />
  <java-symbol type="drawable" name="decor_close_button_light" />
  <java-symbol type="drawable" name="decor_maximize_button_dark" />
  <java-symbol type="drawable" name="decor_maximize_button_light" />
  <java-symbol type="color" name="decor_button_dark_color" />
  <java-symbol type="color" name="decor_button_light_color" />
  <java-symbol type="array" name="unloggable_phone_numbers" />

  <!-- From TelephonyProvider -->
  <java-symbol type="xml" name="apns" />

  <!-- From ContactsProvider -->
  <java-symbol type="array" name="common_nicknames" />
  <java-symbol type="drawable" name="call_contact" />
  <java-symbol type="drawable" name="create_contact" />
  <java-symbol type="string" name="common_name_prefixes" />
  <java-symbol type="string" name="common_last_name_prefixes" />
  <java-symbol type="string" name="common_name_suffixes" />
  <java-symbol type="string" name="common_name_conjunctions" />
  <java-symbol type="string" name="dial_number_using" />
  <java-symbol type="string" name="create_contact_using" />

  <!-- From DownloadProvider -->
  <java-symbol type="integer" name="config_MaxConcurrentDownloadsAllowed" />
  <java-symbol type="integer" name="config_downloadDataDirSize" />
  <java-symbol type="integer" name="config_downloadDataDirLowSpaceThreshold" />

  <!-- From Contacts -->
  <java-symbol type="drawable" name="quickcontact_badge_overlay_dark" />

  <!-- From Browser -->
  <java-symbol type="drawable" name="ic_menu_moreoverflow_normal_holo_dark" />
  <java-symbol type="id" name="placeholder" />
  <java-symbol type="string" name="ssl_certificate_is_valid" />

  <!-- From Mms -->
  <java-symbol type="drawable" name="ic_menu_play_clip" />

  <!-- From Stk -->
  <java-symbol type="bool" name="config_sf_slowBlur" />
  <java-symbol type="drawable" name="ic_volume" />
  <java-symbol type="drawable" name="stat_notify_sim_toolkit" />
  <java-symbol type="bool" name="config_stkNoAlphaUsrCnf" />
  <java-symbol type="bool" name="config_stk_sms_send_support" />

  <!-- From maps library -->
  <java-symbol type="array" name="maps_starting_lat_lng" />
  <java-symbol type="array" name="maps_starting_zoom" />
  <java-symbol type="attr" name="mapViewStyle" />
  <java-symbol type="attr" name="state_focused" />
  <java-symbol type="attr" name="state_selected" />
  <java-symbol type="attr" name="state_pressed" />
  <java-symbol type="drawable" name="compass_arrow" />
  <java-symbol type="drawable" name="compass_base" />
  <java-symbol type="drawable" name="ic_maps_indicator_current_position_anim" />
  <java-symbol type="drawable" name="loading_tile_android" />
  <java-symbol type="drawable" name="maps_google_logo" />
  <java-symbol type="drawable" name="no_tile_256" />
  <java-symbol type="drawable" name="reticle" />

  <!-- From PinyinIME(!!!) -->
  <java-symbol type="string" name="inputMethod" />

  <!-- Gestural Nav buttons within InputMethodService -->
  <java-symbol type="dimen" name="input_method_nav_key_button_ripple_max_width" />
  <java-symbol type="drawable" name="ic_ime_nav_back" />
  <java-symbol type="drawable" name="ic_ime_switcher" />
  <java-symbol type="id" name="input_method_nav_back" />
  <java-symbol type="id" name="input_method_nav_buttons" />
  <java-symbol type="id" name="input_method_nav_center_group" />
  <java-symbol type="id" name="input_method_nav_ends_group" />
  <java-symbol type="id" name="input_method_nav_home_handle" />
  <java-symbol type="id" name="input_method_nav_horizontal" />
  <java-symbol type="id" name="input_method_nav_ime_switcher" />
  <java-symbol type="id" name="input_method_nav_inflater" />
  <java-symbol type="layout" name="input_method_navigation_bar" />
  <java-symbol type="layout" name="input_method_navigation_layout" />
  <java-symbol type="layout" name="input_method_nav_back" />
  <java-symbol type="layout" name="input_method_nav_home_handle" />
  <java-symbol type="layout" name="input_method_nav_ime_switcher" />

  <!-- From Chromium-WebView -->
  <java-symbol type="attr" name="actionModeWebSearchDrawable" />
  <java-symbol type="string" name="websearch" />
  <java-symbol type="drawable" name="ic_media_video_poster" />
  <java-symbol type="xml" name="config_webview_packages" />

  <!-- From SubtitleView -->
  <java-symbol type="dimen" name="subtitle_corner_radius" />
  <java-symbol type="dimen" name="subtitle_shadow_radius" />
  <java-symbol type="dimen" name="subtitle_shadow_offset" />
  <java-symbol type="dimen" name="subtitle_outline_width" />

  <java-symbol type="attr" name="nestedScrollingEnabled" />

  <java-symbol type="layout" name="time_picker_material" />
  <java-symbol type="layout" name="time_picker_header_material" />
  <java-symbol type="layout" name="year_label_text_view" />
  <java-symbol type="layout" name="date_picker_material" />

  <java-symbol type="id" name="time_header" />
  <java-symbol type="id" name="hours" />
  <java-symbol type="id" name="minutes" />
  <java-symbol type="id" name="ampm_layout" />
  <java-symbol type="id" name="am_label" />
  <java-symbol type="id" name="pm_label" />
  <java-symbol type="id" name="radial_picker" />
  <java-symbol type="id" name="separator" />
  <java-symbol type="id" name="date_picker_header" />
  <java-symbol type="id" name="date_picker_header_year" />
  <java-symbol type="id" name="date_picker_header_date" />
  <java-symbol type="id" name="animator" />

  <java-symbol type="string" name="done_label" />
  <java-symbol type="string" name="hour_picker_description" />
  <java-symbol type="string" name="minute_picker_description" />
  <java-symbol type="string" name="select_hours" />
  <java-symbol type="string" name="select_minutes" />
  <java-symbol type="string" name="time_placeholder" />
  <java-symbol type="string" name="deleted_key" />
  <java-symbol type="string" name="sans_serif" />
  <java-symbol type="string" name="radial_numbers_typeface" />
  <java-symbol type="dimen" name="timepicker_selector_radius" />
  <java-symbol type="dimen" name="timepicker_selector_dot_radius" />
  <java-symbol type="dimen" name="timepicker_center_dot_radius" />
  <java-symbol type="dimen" name="timepicker_text_inset_normal" />
  <java-symbol type="dimen" name="timepicker_text_inset_inner" />
  <java-symbol type="dimen" name="timepicker_text_size_normal" />
  <java-symbol type="dimen" name="timepicker_text_size_inner" />
  <java-symbol type="string" name="battery_saver_description" />
  <java-symbol type="string" name="data_saver_description" />
  <java-symbol type="string" name="data_saver_enable_title" />
  <java-symbol type="string" name="data_saver_enable_button" />
  <java-symbol type="string" name="zen_mode_forever" />
  <java-symbol type="string" name="zen_mode_forever_dnd" />
  <java-symbol type="string" name="zen_mode_rule_name_combination" />
  <java-symbol type="string" name="zen_mode_duration_minutes" />
  <java-symbol type="string" name="zen_mode_duration_hours" />
  <java-symbol type="string" name="zen_mode_duration_minutes_summary" />
  <java-symbol type="string" name="zen_mode_duration_hours_summary" />
  <java-symbol type="string" name="zen_mode_duration_minutes_short" />
  <java-symbol type="string" name="zen_mode_duration_hours_short" />
  <java-symbol type="string" name="zen_mode_duration_minutes_summary_short" />
  <java-symbol type="string" name="zen_mode_duration_hours_summary_short" />
  <java-symbol type="string" name="zen_mode_until_next_day" />
  <java-symbol type="string" name="zen_mode_until" />
  <java-symbol type="string" name="zen_mode_feature_name" />
  <java-symbol type="string" name="zen_mode_downtime_feature_name" />
  <java-symbol type="string" name="zen_mode_default_weeknights_name" />
  <java-symbol type="string" name="zen_mode_default_weekends_name" />
  <java-symbol type="string" name="zen_mode_default_events_name" />
  <java-symbol type="string" name="zen_mode_default_every_night_name" />
  <java-symbol type="string" name="display_rotation_camera_compat_toast_after_rotation" />
  <java-symbol type="string" name="display_rotation_camera_compat_toast_in_multi_window" />
  <java-symbol type="array" name="config_system_condition_providers" />
  <java-symbol type="string" name="muted_by" />
  <java-symbol type="string" name="zen_mode_alarm" />

  <java-symbol type="string" name="select_day" />
  <java-symbol type="string" name="select_year" />

  <java-symbol type="string" name="date_picker_month_typeface" />
  <java-symbol type="string" name="date_picker_day_of_week_typeface" />
  <java-symbol type="string" name="date_picker_day_typeface" />
  <java-symbol type="dimen" name="date_picker_month_text_size" />
  <java-symbol type="dimen" name="date_picker_day_of_week_text_size" />
  <java-symbol type="dimen" name="date_picker_day_text_size" />
  <java-symbol type="dimen" name="date_picker_month_height" />
  <java-symbol type="dimen" name="date_picker_day_height" />
  <java-symbol type="dimen" name="date_picker_day_width" />
  <java-symbol type="dimen" name="date_picker_day_selector_radius" />
  <java-symbol type="id" name="date_picker_day_picker" />
  <java-symbol type="id" name="date_picker_year_picker" />

  <java-symbol type="dimen" name="datepicker_view_animator_height" />
  <java-symbol type="dimen" name="datepicker_year_label_height" />

  <java-symbol type="array" name="config_clockTickVibePattern" />

  <!-- From KeyguardServiceDelegate -->
  <java-symbol type="string" name="config_keyguardComponent" />

  <!-- Biometric messages -->
  <java-symbol type="string" name="biometric_app_setting_name" />
  <java-symbol type="string" name="biometric_or_screen_lock_app_setting_name" />
  <java-symbol type="string" name="biometric_dialog_default_title" />
  <java-symbol type="string" name="biometric_dialog_default_subtitle" />
  <java-symbol type="string" name="biometric_or_screen_lock_dialog_default_subtitle" />
  <java-symbol type="string" name="biometric_error_hw_unavailable" />
  <java-symbol type="string" name="biometric_error_user_canceled" />
  <java-symbol type="string" name="biometric_not_recognized" />
  <java-symbol type="string" name="biometric_face_not_recognized" />
  <java-symbol type="string" name="biometric_error_canceled" />
  <java-symbol type="string" name="biometric_error_device_not_secured" />
  <java-symbol type="string" name="biometric_error_generic" />

  <!-- Biometric FRR config -->
  <java-symbol type="fraction" name="config_biometricNotificationFrrThreshold" />
  <java-symbol type="bool" name="config_biometricFrrNotificationEnabled" />

  <!-- Biometric FRR notification messages -->
  <java-symbol type="string" name="device_unlock_notification_name" />
  <java-symbol type="string" name="alternative_unlock_setup_notification_title" />
  <java-symbol type="string" name="alternative_face_setup_notification_content" />
  <java-symbol type="string" name="alternative_fp_setup_notification_content" />

  <!-- Device credential strings for BiometricManager -->
  <java-symbol type="string" name="screen_lock_app_setting_name" />
  <java-symbol type="string" name="screen_lock_dialog_default_subtitle" />

  <!-- Fingerprint messages -->
  <java-symbol type="string" name="fingerprint_error_unable_to_process" />
  <java-symbol type="string" name="fingerprint_error_hw_not_available" />
  <java-symbol type="string" name="fingerprint_error_no_space" />
  <java-symbol type="string" name="fingerprint_error_timeout" />
  <java-symbol type="array" name="fingerprint_error_vendor" />
  <java-symbol type="string" name="fingerprint_error_vendor_unknown" />
  <java-symbol type="string" name="fingerprint_error_not_match" />
  <java-symbol type="string" name="fingerprint_udfps_error_not_match" />
  <java-symbol type="string" name="fingerprint_dialog_use_fingerprint_instead" />
  <java-symbol type="string" name="fingerprint_acquired_partial" />
  <java-symbol type="string" name="fingerprint_acquired_insufficient" />
  <java-symbol type="string" name="fingerprint_acquired_imager_dirty" />
  <java-symbol type="string" name="fingerprint_acquired_too_slow" />
  <java-symbol type="string" name="fingerprint_acquired_too_fast" />
  <java-symbol type="string" name="fingerprint_acquired_power_press" />
  <java-symbol type="string" name="fingerprint_acquired_too_bright" />
  <java-symbol type="array" name="fingerprint_acquired_vendor" />
  <java-symbol type="string" name="fingerprint_error_canceled" />
  <java-symbol type="string" name="fingerprint_error_user_canceled" />
  <java-symbol type="string" name="fingerprint_error_lockout" />
  <java-symbol type="string" name="fingerprint_error_lockout_permanent" />
  <java-symbol type="string" name="fingerprint_name_template" />
  <java-symbol type="string" name="fingerprint_app_setting_name" />
  <java-symbol type="string" name="fingerprint_or_screen_lock_app_setting_name" />
  <java-symbol type="string" name="fingerprint_dialog_default_subtitle" />
  <java-symbol type="string" name="fingerprint_or_screen_lock_dialog_default_subtitle" />
  <java-symbol type="string" name="fingerprint_authenticated" />
  <java-symbol type="string" name="fingerprint_error_no_fingerprints" />
  <java-symbol type="string" name="fingerprint_error_hw_not_present" />
  <java-symbol type="string" name="fingerprint_error_security_update_required" />
  <java-symbol type="string" name="fingerprint_error_bad_calibration" />
  <java-symbol type="string" name="fingerprint_acquired_immobile" />
  <java-symbol type="string" name="fingerprint_recalibrate_notification_name" />
  <java-symbol type="string" name="fingerprint_recalibrate_notification_title" />
  <java-symbol type="string" name="fingerprint_recalibrate_notification_content" />
  <java-symbol type="string" name="fingerprint_error_power_pressed" />

  <!-- Fingerprint config -->
  <java-symbol type="integer" name="config_fingerprintMaxTemplatesPerUser"/>
  <java-symbol type="bool" name="config_fingerprintSupportsGestures"/>
  <java-symbol type="integer" name="config_sidefpsBpPowerPressWindow"/>
  <java-symbol type="integer" name="config_sidefpsKeyguardPowerPressWindow"/>
  <java-symbol type="integer" name="config_sidefpsPostAuthDowntime"/>
  <java-symbol type="integer" name="config_sideFpsToastTimeout"/>
  <java-symbol type="integer" name="config_sidefpsSkipWaitForPowerAcquireMessage"/>
  <java-symbol type="integer" name="config_sidefpsSkipWaitForPowerVendorAcquireMessage"/>

  <!-- Clickable toast used during sidefps enrollment -->
  <java-symbol type="layout" name="side_fps_toast" />
  <java-symbol type="id" name="turn_off_screen" />

  <!-- Face authentication messages -->
  <java-symbol type="string" name="face_recalibrate_notification_name" />
  <java-symbol type="string" name="face_recalibrate_notification_title" />
  <java-symbol type="string" name="face_recalibrate_notification_content" />
  <java-symbol type="string" name="face_sensor_privacy_enabled" />
  <java-symbol type="string" name="face_error_unable_to_process" />
  <java-symbol type="string" name="face_error_hw_not_available" />
  <java-symbol type="string" name="face_error_no_space" />
  <java-symbol type="string" name="face_error_timeout" />
  <java-symbol type="array" name="face_error_vendor" />
  <java-symbol type="string" name="face_error_vendor_unknown" />
  <java-symbol type="string" name="face_error_canceled" />
  <java-symbol type="string" name="face_error_user_canceled" />
  <java-symbol type="string" name="face_error_lockout" />
  <java-symbol type="string" name="face_error_lockout_permanent" />
  <java-symbol type="string" name="face_error_not_enrolled" />
  <java-symbol type="string" name="face_error_hw_not_present" />
  <java-symbol type="string" name="face_acquired_insufficient" />
  <java-symbol type="string" name="face_acquired_too_bright" />
  <java-symbol type="string" name="face_acquired_too_dark" />
  <java-symbol type="string" name="face_acquired_too_close" />
  <java-symbol type="string" name="face_acquired_too_far" />
  <java-symbol type="string" name="face_acquired_too_high" />
  <java-symbol type="string" name="face_acquired_too_low" />
  <java-symbol type="string" name="face_acquired_too_right" />
  <java-symbol type="string" name="face_acquired_too_left" />
  <java-symbol type="string" name="face_acquired_poor_gaze" />
  <java-symbol type="string" name="face_acquired_not_detected" />
  <java-symbol type="string" name="face_acquired_too_much_motion" />
  <java-symbol type="string" name="face_acquired_recalibrate" />
  <java-symbol type="string" name="face_acquired_too_different" />
  <java-symbol type="string" name="face_acquired_too_similar" />
  <java-symbol type="string" name="face_acquired_pan_too_extreme" />
  <java-symbol type="string" name="face_acquired_tilt_too_extreme" />
  <java-symbol type="string" name="face_acquired_roll_too_extreme" />
  <java-symbol type="string" name="face_acquired_obscured" />
  <java-symbol type="string" name="face_acquired_sensor_dirty" />
  <java-symbol type="string" name="face_acquired_dark_glasses_detected" />
  <java-symbol type="string" name="face_acquired_mouth_covering_detected" />
  <java-symbol type="string" name="face_acquired_recalibrate_alt" />
  <java-symbol type="string" name="face_acquired_dark_glasses_detected_alt" />
  <java-symbol type="string" name="face_acquired_mouth_covering_detected_alt" />
  <java-symbol type="array" name="face_acquired_vendor" />
  <java-symbol type="string" name="face_name_template" />
  <java-symbol type="string" name="face_app_setting_name" />
  <java-symbol type="string" name="face_or_screen_lock_app_setting_name" />
  <java-symbol type="string" name="face_dialog_default_subtitle" />
  <java-symbol type="string" name="face_or_screen_lock_dialog_default_subtitle" />
  <java-symbol type="string" name="face_authenticated_no_confirmation_required" />
  <java-symbol type="string" name="face_authenticated_confirmation_required" />
  <java-symbol type="string" name="face_error_security_update_required" />

  <java-symbol type="string" name="config_biometric_prompt_ui_package" />
  <java-symbol type="array" name="config_biometric_sensors" />
  <java-symbol type="bool" name="allow_test_udfps" />
  <java-symbol type="array" name="config_udfps_sensor_props" />
  <java-symbol type="array" name="config_udfps_touch_detection_options" />
  <java-symbol type="integer" name="config_selected_udfps_touch_detection" />
  <java-symbol type="array" name="config_sfps_sensor_props" />
  <java-symbol type="bool" name="config_is_powerbutton_fps" />
  <java-symbol type="array" name="config_udfps_enroll_stage_thresholds" />
  <java-symbol type="array" name="config_sfps_enroll_stage_thresholds" />
  <java-symbol type="array" name="config_face_acquire_enroll_ignorelist" />
  <java-symbol type="array" name="config_face_acquire_vendor_enroll_ignorelist" />
  <java-symbol type="array" name="config_face_acquire_keyguard_ignorelist" />
  <java-symbol type="array" name="config_face_acquire_vendor_keyguard_ignorelist" />
  <java-symbol type="array" name="config_face_acquire_biometricprompt_ignorelist" />
  <java-symbol type="array" name="config_face_acquire_vendor_biometricprompt_ignorelist" />
  <java-symbol type="bool" name="config_faceAuthSupportsSelfIllumination" />
  <java-symbol type="bool" name="config_faceAuthDismissesKeyguard" />
  <java-symbol type="bool" name="config_performantAuthDefault" />

  <!-- Face config -->
  <java-symbol type="integer" name="config_faceMaxTemplatesPerUser" />

  <!-- From various Material changes -->
  <java-symbol type="attr" name="titleTextAppearance" />
  <java-symbol type="attr" name="subtitleTextAppearance" />
  <java-symbol type="attr" name="windowActionBarFullscreenDecorLayout" />
  <java-symbol type="drawable" name="ic_lock_bugreport" />
  <java-symbol type="id" name="icon_frame" />
  <java-symbol type="style" name="Animation.VolumePanel" />
  <java-symbol type="transition" name="no_transition" />
  <java-symbol type="color" name="timepicker_default_text_color_material" />
  <java-symbol type="color" name="timepicker_default_ampm_unselected_background_color_material" />
  <java-symbol type="color" name="timepicker_default_ampm_selected_background_color_material" />
  <java-symbol type="color" name="timepicker_default_selector_color_material" />
  <java-symbol type="color" name="timepicker_default_numbers_background_color_material" />
  <java-symbol type="style" name="TextAppearance.Material.TimePicker.TimeLabel" />
  <java-symbol type="attr" name="seekBarPreferenceStyle" />
  <java-symbol type="style" name="Theme.DeviceDefault.Resolver" />
  <java-symbol type="style" name="Theme.DeviceDefault.Chooser" />
  <java-symbol type="style" name="Theme.DeviceDefault.System" />
  <java-symbol type="attr" name="preferenceActivityStyle" />
  <java-symbol type="attr" name="preferenceFragmentStyle" />
  <java-symbol type="bool" name="skipHoldBeforeMerge" />
  <java-symbol type="bool" name="imsServiceAllowTurnOff" />
  <java-symbol type="bool" name="config_device_volte_available" />
  <java-symbol type="bool" name="config_carrier_volte_available" />
  <java-symbol type="bool" name="config_carrier_volte_tty_supported" />
  <java-symbol type="bool" name="config_device_vt_available" />
  <java-symbol type="bool" name="config_device_respects_hold_carrier_config" />
  <java-symbol type="bool" name="config_carrier_vt_available" />
  <java-symbol type="bool" name="config_device_wfc_ims_available" />
  <java-symbol type="bool" name="config_carrier_wfc_ims_available" />
  <java-symbol type="bool" name="config_use_voip_mode_for_ims" />
  <java-symbol type="bool" name="config_allow_ussd_over_ims" />
  <java-symbol type="attr" name="touchscreenBlocksFocus" />
  <java-symbol type="layout" name="resolver_list_with_default" />
  <java-symbol type="layout" name="miniresolver" />
  <java-symbol type="layout" name="resolver_profile_tab_button" />
  <java-symbol type="string" name="activity_resolver_use_always" />
  <java-symbol type="string" name="whichApplicationNamed" />
  <java-symbol type="string" name="whichApplicationLabel" />
  <java-symbol type="string" name="whichViewApplication" />
  <java-symbol type="string" name="whichViewApplicationNamed" />
  <java-symbol type="string" name="whichViewApplicationLabel" />
  <java-symbol type="string" name="whichOpenHostLinksWith" />
  <java-symbol type="string" name="whichOpenHostLinksWithApp" />
  <java-symbol type="string" name="whichOpenLinksWith" />
  <java-symbol type="string" name="whichOpenLinksWithApp" />
  <java-symbol type="string" name="whichGiveAccessToApplicationLabel" />
  <java-symbol type="string" name="whichEditApplication" />
  <java-symbol type="string" name="whichEditApplicationNamed" />
  <java-symbol type="string" name="whichEditApplicationLabel" />
  <java-symbol type="string" name="whichSendApplication" />
  <java-symbol type="string" name="whichSendApplicationNamed" />
  <java-symbol type="string" name="whichSendApplicationLabel" />
  <java-symbol type="string" name="whichSendToApplication" />
  <java-symbol type="string" name="whichSendToApplicationNamed" />
  <java-symbol type="string" name="whichSendToApplicationLabel" />
  <java-symbol type="string" name="whichImageCaptureApplication" />
  <java-symbol type="string" name="whichImageCaptureApplicationNamed" />
  <java-symbol type="string" name="whichImageCaptureApplicationLabel" />
  <java-symbol type="attr" name="lightY" />
  <java-symbol type="attr" name="lightZ" />
  <java-symbol type="attr" name="lightRadius" />
  <java-symbol type="attr" name="ambientShadowAlpha" />
  <java-symbol type="attr" name="spotShadowAlpha" />
  <java-symbol type="bool" name="config_sms_decode_gsm_8bit_data" />
  <java-symbol type="integer" name="default_reserved_data_coding_scheme" />
  <java-symbol type="dimen" name="text_size_small_material" />
  <java-symbol type="attr" name="checkMarkGravity" />
  <java-symbol type="layout" name="select_dialog_singlechoice_material" />
  <java-symbol type="layout" name="select_dialog_multichoice_material" />
  <java-symbol type="array" name="no_ems_support_sim_operators" />
  <java-symbol type="color" name="system_notification_accent_color" />
  <java-symbol type="dimen" name="text_handle_min_size" />
  <java-symbol type="id" name="transitionTransform" />
  <java-symbol type="id" name="parentMatrix" />
  <java-symbol type="bool" name="config_auto_attach_data_on_creation" />
  <java-symbol type="attr" name="closeItemLayout" />
  <java-symbol type="layout" name="resolver_different_item_header" />
  <java-symbol type="integer" name="config_cdma_3waycall_flash_delay"/>
  <java-symbol type="array" name="config_default_vm_number" />
  <java-symbol type="attr" name="windowBackgroundFallback" />
  <java-symbol type="id" name="textSpacerNoButtons" />
  <java-symbol type="array" name="dial_string_replace" />
  <java-symbol type="bool" name="config_restart_radio_on_pdp_fail_regular_deactivation" />
  <java-symbol type="array" name="networks_not_clear_data" />
  <java-symbol type="bool" name="config_switch_phone_on_voice_reg_state_change" />
  <java-symbol type="string" name="whichHomeApplicationNamed" />
  <java-symbol type="string" name="whichHomeApplicationLabel" />
  <java-symbol type="bool" name="config_smma_notification_supported_over_ims" />
  <java-symbol type="bool" name="config_sms_force_7bit_encoding" />
  <java-symbol type="bool" name="config_defaultWindowFeatureOptionsPanel" />
  <java-symbol type="bool" name="config_defaultWindowFeatureContextMenu" />
  <java-symbol type="bool" name="config_overrideRemoteViewsActivityTransition" />
  <java-symbol type="attr" name="colorProgressBackgroundNormal" />
  <java-symbol type="bool" name="config_allow_pin_storage_for_unattended_reboot" />

  <java-symbol type="layout" name="simple_account_item" />
  <java-symbol type="string" name="prohibit_manual_network_selection_in_gobal_mode" />
  <java-symbol type="id" name="profile_button" />

  <java-symbol type="array" name="config_vvmSmsFilterRegexes" />

  <!-- Cascading submenus -->
  <java-symbol type="dimen" name="cascading_menus_min_smallest_width" />

  <java-symbol type="string" name="android_system_label" />
  <java-symbol type="string" name="system_error_wipe_data" />
  <java-symbol type="string" name="system_error_manufacturer" />
  <java-symbol type="dimen" name="fast_scroller_minimum_touch_target" />
  <java-symbol type="array" name="config_cdma_international_roaming_indicators" />
  <java-symbol type="string" name="kg_text_message_separator" />

  <java-symbol type="bool" name="config_use_sim_language_file" />
  <java-symbol type="bool" name="config_LTE_eri_for_network_name" />
  <java-symbol type="bool" name="config_defaultInTouchMode" />

  <java-symbol type="string" name="usb_midi_peripheral_name" />
  <java-symbol type="string" name="usb_midi_peripheral_manufacturer_name" />
  <java-symbol type="string" name="usb_midi_peripheral_product_name" />

  <java-symbol type="id" name="spacer" />

  <java-symbol type="xml" name="bookmarks" />

  <java-symbol type="integer" name="config_defaultNightMode" />

  <java-symbol type="integer" name="config_jobSchedulerInactivityIdleThreshold" />
  <java-symbol type="integer" name="config_jobSchedulerIdleWindowSlop" />
  <java-symbol type="bool" name="config_jobSchedulerRestrictBackgroundUser" />
  <java-symbol type="integer" name="config_jobSchedulerUserGracePeriod" />

  <java-symbol type="style" name="Animation.ImmersiveModeConfirmation" />

  <java-symbol type="integer" name="config_screen_magnification_multi_tap_adjustment" />
  <java-symbol type="dimen" name="config_screen_magnification_scaling_threshold" />
  <java-symbol type="dimen" name="timepicker_selector_stroke"/>

  <java-symbol type="style" name="TextAppearance.Material.Widget.Calendar.Month" />
  <java-symbol type="style" name="TextAppearance.Material.Widget.Calendar.DayOfWeek" />
  <java-symbol type="style" name="TextAppearance.Material.Widget.Calendar.Day" />
  <java-symbol type="style" name="TextAppearance.Material.DatePicker.List.YearLabel" />
  <java-symbol type="style" name="TextAppearance.Material.DatePicker.List.YearLabel.Activated" />
  <java-symbol type="dimen" name="day_picker_padding_top"/>
  <java-symbol type="dimen" name="date_picker_day_of_week_height"/>

  <java-symbol type="string" name="storage_internal" />
  <java-symbol type="string" name="storage_sd_card" />
  <java-symbol type="string" name="storage_sd_card_label" />
  <java-symbol type="string" name="storage_usb_drive" />
  <java-symbol type="string" name="storage_usb_drive_label" />
  <java-symbol type="string" name="storage_usb" />

  <java-symbol type="drawable" name="ic_eject_24dp" />
  <java-symbol type="drawable" name="ic_folder_24dp" />
  <java-symbol type="drawable" name="ic_sd_card_48dp" />
  <java-symbol type="drawable" name="ic_settings_24dp" />
  <java-symbol type="drawable" name="ic_storage_48dp" />
  <java-symbol type="drawable" name="ic_usb_48dp" />
  <java-symbol type="drawable" name="ic_zen_24dp" />
  <java-symbol type="drawable" name="ic_dnd_block_notifications" />

  <!-- Floating toolbar -->
  <java-symbol type="id" name="floating_toolbar_menu_item_image" />
  <java-symbol type="id" name="floating_toolbar_menu_item_text" />
  <java-symbol type="id" name="overflow" />
  <java-symbol type="layout" name="floating_popup_container" />
  <java-symbol type="layout" name="floating_popup_menu_button" />
  <java-symbol type="layout" name="floating_popup_open_overflow_button" />
  <java-symbol type="layout" name="floating_popup_close_overflow_button" />
  <java-symbol type="layout" name="floating_popup_overflow_button" />
  <java-symbol type="string" name="floating_toolbar_open_overflow_description" />
  <java-symbol type="string" name="floating_toolbar_close_overflow_description" />
  <java-symbol type="dimen" name="floating_toolbar_height" />
  <java-symbol type="dimen" name="floating_toolbar_menu_button_side_padding" />
  <java-symbol type="dimen" name="floating_toolbar_overflow_side_padding" />
  <java-symbol type="dimen" name="floating_toolbar_text_size" />
  <java-symbol type="dimen" name="floating_toolbar_menu_button_minimum_width" />
  <java-symbol type="dimen" name="floating_toolbar_preferred_width" />
  <java-symbol type="dimen" name="floating_toolbar_minimum_overflow_height" />
  <java-symbol type="dimen" name="floating_toolbar_maximum_overflow_height" />
  <java-symbol type="dimen" name="floating_toolbar_horizontal_margin" />
  <java-symbol type="dimen" name="floating_toolbar_vertical_margin" />
  <java-symbol type="dimen" name="floating_toolbar_icon_text_spacing" />
  <java-symbol type="dimen" name="content_rect_bottom_clip_allowance" />
  <java-symbol type="drawable" name="ft_avd_tooverflow" />
  <java-symbol type="drawable" name="ft_avd_toarrow" />
  <java-symbol type="drawable" name="ft_avd_toarrow_animation" />
  <java-symbol type="drawable" name="ft_avd_tooverflow_animation" />
  <java-symbol type="attr" name="floatingToolbarDividerColor" />

  <!-- Magnifier -->
  <java-symbol type="dimen" name="default_magnifier_width" />
  <java-symbol type="dimen" name="default_magnifier_height" />
  <java-symbol type="dimen" name="default_magnifier_elevation" />
  <java-symbol type="dimen" name="default_magnifier_corner_radius" />
  <java-symbol type="dimen" name="default_magnifier_zoom" />
  <java-symbol type="dimen" name="default_magnifier_vertical_offset" />
  <java-symbol type="dimen" name="default_magnifier_horizontal_offset" />
  <java-symbol type="color" name="default_magnifier_color_overlay" />
  <java-symbol type="attr" name="magnifierWidth" />
  <java-symbol type="attr" name="magnifierHeight" />
  <java-symbol type="attr" name="magnifierElevation" />
  <java-symbol type="attr" name="magnifierZoom" />
  <java-symbol type="attr" name="magnifierVerticalOffset" />
  <java-symbol type="attr" name="magnifierHorizontalOffset" />
  <java-symbol type="attr" name="magnifierColorOverlay" />
  <java-symbol type="attr" name="magnifierStyle" />

  <java-symbol type="string" name="date_picker_prev_month_button" />
  <java-symbol type="string" name="date_picker_next_month_button" />
  <java-symbol type="layout" name="date_picker_month_item_material" />
  <java-symbol type="id" name="month_view" />
  <java-symbol type="integer" name="config_zen_repeat_callers_threshold" />
  <java-symbol type="dimen" name="chooser_width" />
  <java-symbol type="dimen" name="chooser_corner_radius" />
  <java-symbol type="string" name="chooser_no_direct_share_targets" />
  <java-symbol type="drawable" name="chooser_row_layer_list" />
  <java-symbol type="dimen" name="chooser_view_spacing" />
  <java-symbol type="dimen" name="chooser_edge_margin_thin" />
  <java-symbol type="dimen" name="chooser_edge_margin_normal" />
  <java-symbol type="dimen" name="chooser_preview_image_font_size"/>
  <java-symbol type="dimen" name="chooser_preview_width" />
  <java-symbol type="dimen" name="chooser_preview_image_border"/>
  <java-symbol type="dimen" name="chooser_max_collapsed_height" />
  <java-symbol type="layout" name="chooser_grid" />
  <java-symbol type="id" name="chooser_header" />
  <java-symbol type="dimen" name="chooser_header_scroll_elevation" />
  <java-symbol type="layout" name="chooser_grid_preview_text" />
  <java-symbol type="layout" name="chooser_grid_preview_image" />
  <java-symbol type="layout" name="chooser_grid_preview_file" />
  <java-symbol type="id" name="chooser_row_text_option" />
  <java-symbol type="dimen" name="chooser_row_text_option_translate" />
  <java-symbol type="dimen" name="chooser_preview_image_max_dimen"/>
  <java-symbol type="drawable" name="ic_chooser_group_arrow"/>
  <java-symbol type="drawable" name="chooser_group_background"/>
  <java-symbol type="drawable" name="ic_chooser_pin"/>
  <java-symbol type="drawable" name="ic_chooser_pin_dialog"/>
  <java-symbol type="drawable" name="chooser_pinned_background"/>
  <java-symbol type="integer" name="config_maxShortcutTargetsPerApp" />
  <java-symbol type="layout" name="resolve_grid_item" />
  <java-symbol type="id" name="day_picker_view_pager" />
  <java-symbol type="layout" name="day_picker_content_material" />
  <java-symbol type="drawable" name="scroll_indicator_material" />

  <java-symbol type="layout" name="chooser_row" />
  <java-symbol type="layout" name="chooser_profile_row" />
  <java-symbol type="color" name="chooser_row_divider" />
  <java-symbol type="layout" name="chooser_row_direct_share" />
  <java-symbol type="bool" name="config_supportDoubleTapWake" />
  <java-symbol type="drawable" name="ic_perm_device_info" />
  <java-symbol type="string" name="config_radio_access_family" />
  <java-symbol type="string" name="notification_inbox_ellipsis" />
  <java-symbol type="bool" name="config_mainBuiltInDisplayIsRound" />

  <java-symbol type="id" name="actions_container" />
  <java-symbol type="id" name="actions_container_layout" />
  <java-symbol type="id" name="smart_reply_container" />
  <java-symbol type="id" name="remote_input_tag" />
  <java-symbol type="id" name="pending_intent_tag" />
  <java-symbol type="id" name="remote_checked_change_listener_tag" />
  <java-symbol type="id" name="notification_action_index_tag" />

  <java-symbol type="attr" name="seekBarDialogPreferenceStyle" />
  <java-symbol type="string" name="ext_media_status_removed" />
  <java-symbol type="string" name="ext_media_status_unmounted" />
  <java-symbol type="string" name="ext_media_status_checking" />
  <java-symbol type="string" name="ext_media_status_mounted" />
  <java-symbol type="string" name="ext_media_status_mounted_ro" />
  <java-symbol type="string" name="ext_media_status_bad_removal" />
  <java-symbol type="string" name="ext_media_status_unmountable" />
  <java-symbol type="string" name="ext_media_status_unsupported" />
  <java-symbol type="string" name="ext_media_status_ejecting" />
  <java-symbol type="string" name="ext_media_status_formatting" />
  <java-symbol type="string" name="ext_media_status_missing" />
  <java-symbol type="string" name="ext_media_unsupported_notification_message" />
  <java-symbol type="string" name="ext_media_unsupported_notification_title" />
  <java-symbol type="drawable" name="ic_dialog_alert_material" />


  <java-symbol type="string" name="lockscreen_access_pattern_area" />

  <java-symbol type="bool" name="config_eap_sim_based_auth_supported" />

  <java-symbol type="array" name="config_cell_retries_per_error_code" />
  <java-symbol type="drawable" name="ic_more_items" />

  <!-- Gesture -->
  <java-symbol type="integer" name="config_cameraLaunchGestureSensorType" />
  <java-symbol type="string" name="config_cameraLaunchGestureSensorStringType" />
  <java-symbol type="bool" name="config_cameraDoubleTapPowerGestureEnabled" />
  <java-symbol type="integer" name="config_cameraLiftTriggerSensorType" />
  <java-symbol type="string" name="config_cameraLiftTriggerSensorStringType" />
  <java-symbol type="bool" name="config_emergencyGestureEnabled" />
  <java-symbol type="bool" name="config_defaultEmergencyGestureEnabled" />
  <java-symbol type="bool" name="config_defaultEmergencyGestureSoundEnabled" />
  <java-symbol type="bool" name="config_volumeHushGestureEnabled" />

  <java-symbol type="drawable" name="platlogo_m" />

  <java-symbol type="string" name="config_iccHotswapPromptForRestartDialogComponent" />

  <java-symbol type="string" name="config_packagedKeyboardName" />
  <java-symbol type="bool" name="config_forceWindowDrawsStatusBarBackground" />
  <java-symbol type="integer" name="config_navBarOpacityMode" />
  <java-symbol type="integer" name="config_navBarInteractionMode" />
  <java-symbol type="bool" name="config_navBarCanMove" />
  <java-symbol type="bool" name="config_navBarTapThrough" />
  <java-symbol type="bool" name="config_imeDrawsImeNavBar" />
  <java-symbol type="bool" name="config_navBarAlwaysShowOnSideEdgeGesture" />
  <java-symbol type="bool" name="config_navBarNeedsScrim" />
  <java-symbol type="bool" name="config_allowSeamlessRotationDespiteNavBarMoving" />
  <java-symbol type="dimen" name="config_backGestureInset" />
  <java-symbol type="array" name="config_backGestureInsetScales" />
  <java-symbol type="color" name="system_bar_background_semi_transparent" />
  <java-symbol type="bool" name="config_showGesturalNavigationHints" />
  <java-symbol type="bool" name="config_navBarDefaultTransparent" />
  <java-symbol type="color" name="navigation_bar_default"/>
  <java-symbol type="color" name="navigation_bar_compatible"/>

  <!-- EditText suggestion popup. -->
  <java-symbol type="id" name="suggestionWindowContainer" />
  <java-symbol type="id" name="suggestionContainer" />
  <java-symbol type="id" name="addToDictionaryButton" />
  <java-symbol type="id" name="deleteButton" />
  <!-- TextView -->
  <java-symbol type="bool" name="config_textShareSupported" />
  <java-symbol type="string" name="failed_to_copy_to_clipboard" />

  <java-symbol type="id" name="notification_material_reply_container" />
  <java-symbol type="id" name="notification_material_reply_text_1" />
  <java-symbol type="id" name="notification_material_reply_text_1_container" />
  <java-symbol type="id" name="notification_material_reply_text_2" />
  <java-symbol type="id" name="notification_material_reply_text_3" />
  <java-symbol type="id" name="notification_material_reply_progress" />

  <java-symbol type="string" name="notification_hidden_text" />
  <java-symbol type="id" name="app_name_text" />
  <java-symbol type="id" name="app_name_divider" />
  <java-symbol type="id" name="header_text" />
  <java-symbol type="id" name="header_text_secondary" />
  <java-symbol type="id" name="expand_button" />
  <java-symbol type="id" name="expand_button_pill" />
  <java-symbol type="id" name="expand_button_pill_colorized_layer" />
  <java-symbol type="id" name="expand_button_number" />
  <java-symbol type="id" name="expand_button_icon" />
  <java-symbol type="id" name="alternate_expand_target" />
  <java-symbol type="id" name="notification_header" />
  <java-symbol type="id" name="notification_top_line" />
  <java-symbol type="id" name="time_divider" />
  <java-symbol type="id" name="header_text_divider" />
  <java-symbol type="id" name="header_text_secondary_divider" />
  <java-symbol type="drawable" name="ic_expand_notification" />
  <java-symbol type="drawable" name="ic_collapse_notification" />
  <java-symbol type="drawable" name="ic_expand_bundle" />
  <java-symbol type="drawable" name="ic_collapse_bundle" />
  <java-symbol type="dimen" name="notification_header_shrink_min_width" />
  <java-symbol type="dimen" name="notification_header_shrink_hide_width" />
  <java-symbol type="dimen" name="notification_content_margin_start" />
  <java-symbol type="dimen" name="notification_content_margin_end" />
  <java-symbol type="dimen" name="notification_heading_margin_end" />
  <java-symbol type="dimen" name="notification_content_margin_top" />
  <java-symbol type="dimen" name="notification_content_margin" />
  <java-symbol type="dimen" name="notification_header_background_height" />
  <java-symbol type="dimen" name="notification_header_touchable_height" />
  <java-symbol type="dimen" name="notification_header_expand_icon_size" />
  <java-symbol type="dimen" name="notification_header_icon_size" />
  <java-symbol type="dimen" name="notification_header_app_name_margin_start" />
  <java-symbol type="dimen" name="notification_header_separating_margin" />
  <java-symbol type="dimen" name="notification_headerless_margin_oneline" />
  <java-symbol type="dimen" name="notification_headerless_margin_twoline" />
  <java-symbol type="string" name="default_notification_channel_label" />
  <java-symbol type="string" name="importance_from_user" />
  <java-symbol type="string" name="importance_from_person" />

  <java-symbol type="layout" name="work_widget_mask_view" />
  <java-symbol type="id" name="work_widget_app_icon" />
  <java-symbol type="id" name="work_widget_badge_icon" />

  <java-symbol type="id" name="aerr_report" />
  <java-symbol type="id" name="aerr_restart" />
  <java-symbol type="id" name="aerr_close" />
  <java-symbol type="id" name="aerr_app_info" />
  <java-symbol type="id" name="aerr_mute" />

  <java-symbol type="string" name="status_bar_rotate" />
  <java-symbol type="string" name="status_bar_headset" />
  <java-symbol type="string" name="status_bar_data_saver" />
  <java-symbol type="string" name="status_bar_managed_profile" />
  <java-symbol type="string" name="status_bar_ime" />
  <java-symbol type="string" name="status_bar_sync_failing" />
  <java-symbol type="string" name="status_bar_sync_active" />
  <java-symbol type="string" name="status_bar_cast" />
  <java-symbol type="string" name="status_bar_connected_display" />
  <java-symbol type="string" name="status_bar_hotspot" />
  <java-symbol type="string" name="status_bar_location" />
  <java-symbol type="string" name="status_bar_bluetooth" />
  <java-symbol type="string" name="status_bar_nfc" />
  <java-symbol type="string" name="status_bar_tty" />
  <java-symbol type="string" name="status_bar_speakerphone" />
  <java-symbol type="string" name="status_bar_zen" />
  <java-symbol type="string" name="status_bar_mute" />
  <java-symbol type="string" name="status_bar_volume" />
  <java-symbol type="string" name="status_bar_wifi" />
  <java-symbol type="string" name="status_bar_cdma_eri" />
  <java-symbol type="string" name="status_bar_data_connection" />
  <java-symbol type="string" name="status_bar_phone_evdo_signal" />
  <java-symbol type="string" name="status_bar_phone_signal" />
  <java-symbol type="string" name="status_bar_battery" />
  <java-symbol type="string" name="status_bar_alarm_clock" />
  <java-symbol type="string" name="status_bar_secure" />
  <java-symbol type="string" name="status_bar_clock" />
  <java-symbol type="string" name="status_bar_airplane" />
  <java-symbol type="string" name="status_bar_no_calling" />
  <java-symbol type="string" name="status_bar_call_strength" />
  <java-symbol type="string" name="status_bar_mobile" />
  <java-symbol type="string" name="status_bar_ethernet" />
  <java-symbol type="string" name="status_bar_vpn" />
  <java-symbol type="string" name="status_bar_microphone" />
  <java-symbol type="string" name="status_bar_camera" />
  <java-symbol type="string" name="status_bar_sensors_off" />
  <java-symbol type="string" name="status_bar_screen_record" />

  <!-- Locale picker -->
  <java-symbol type="id" name="locale_search_menu" />
  <java-symbol type="layout" name="language_picker_item" />
  <java-symbol type="layout" name="language_picker_bilingual_item" />
  <java-symbol type="color" name="language_picker_item_text_color" />
  <java-symbol type="color" name="language_picker_item_text_color_selected" />
  <java-symbol type="color" name="language_picker_item_text_color_secondary_selected" />
  <java-symbol type="drawable" name="language_picker_item_text_color_selector" />
  <java-symbol type="drawable" name="language_picker_item_text_color2_selector" />
  <java-symbol type="drawable" name="language_picker_item_bg_selected" />
  <java-symbol type="layout" name="language_picker_section_header" />
  <java-symbol type="layout" name="language_picker_bilingual_section_header" />
  <java-symbol type="menu" name="language_selection_list" />
  <java-symbol type="string" name="country_selection_title" />
  <java-symbol type="string" name="language_picker_section_all" />
  <java-symbol type="string" name="region_picker_section_all" />
  <java-symbol type="string" name="language_picker_section_suggested" />
  <java-symbol type="string" name="language_picker_regions_section_suggested" />
  <java-symbol type="string" name="language_picker_section_suggested_bilingual" />
  <java-symbol type="string" name="region_picker_section_suggested_bilingual" />
  <java-symbol type="string" name="language_selection_title" />
  <java-symbol type="string" name="search_language_hint" />

  <!--  Work profile unlaunchable app alert dialog-->
  <java-symbol type="style" name="AlertDialogWithEmergencyButton"/>
  <java-symbol type="string" name="work_mode_emergency_call_button" />
  <java-symbol type="string" name="work_mode_off_title" />
  <java-symbol type="string" name="work_mode_turn_on" />

  <java-symbol type="string" name="deprecated_target_sdk_message" />
  <java-symbol type="string" name="deprecated_target_sdk_app_store" />

  <java-symbol type="string" name="deprecated_abi_message" />

  <!-- New SMS notification while phone is locked. -->
  <java-symbol type="string" name="new_sms_notification_title" />
  <java-symbol type="string" name="new_sms_notification_content" />

  <java-symbol type="dimen" name="media_notification_expanded_image_margin_bottom" />

  <java-symbol type="bool" name="config_strongAuthRequiredOnBoot" />

  <java-symbol type="layout" name="app_anr_dialog" />
  <java-symbol type="layout" name="notification_template_material_messaging" />
  <java-symbol type="layout" name="notification_template_material_big_messaging" />

  <java-symbol type="id" name="aerr_wait" />

  <java-symbol type="string" name="duration_minutes_shortest" />
  <java-symbol type="string" name="duration_hours_shortest" />
  <java-symbol type="string" name="duration_days_shortest" />
  <java-symbol type="string" name="duration_years_shortest" />
  <java-symbol type="string" name="duration_minutes_shortest_future" />
  <java-symbol type="string" name="duration_hours_shortest_future" />
  <java-symbol type="string" name="duration_days_shortest_future" />
  <java-symbol type="string" name="duration_years_shortest_future" />

  <java-symbol type="string" name="duration_minutes_relative" />
  <java-symbol type="string" name="duration_hours_relative" />
  <java-symbol type="string" name="duration_days_relative" />
  <java-symbol type="string" name="duration_years_relative" />
  <java-symbol type="string" name="duration_minutes_relative_future" />
  <java-symbol type="string" name="duration_hours_relative_future" />
  <java-symbol type="string" name="duration_days_relative_future" />
  <java-symbol type="string" name="duration_years_relative_future" />

  <java-symbol type="string" name="now_string_shortest" />

  <!-- Encryption notification while accounts are locked by credential encryption -->
  <java-symbol type="string" name="profile_encrypted_title" />
  <java-symbol type="string" name="profile_encrypted_detail" />
  <java-symbol type="string" name="profile_encrypted_message" />
  <java-symbol type="drawable" name="ic_user_secure" />

  <java-symbol type="string" name="android_upgrading_notification_title" />

  <java-symbol type="string" name="usb_mtp_launch_notification_title" />
  <java-symbol type="string" name="usb_mtp_launch_notification_description" />

  <java-symbol type="color" name="notification_action_list" />

  <!-- Resolver target actions -->
  <java-symbol type="array" name="resolver_target_actions_pin" />
  <java-symbol type="array" name="resolver_target_actions_unpin" />
  <java-symbol type="string" name="pin_specific_target" />
  <java-symbol type="string" name="unpin_specific_target" />

  <java-symbol type="array" name="non_removable_euicc_slots" />

  <java-symbol type="string" name="install_carrier_app_notification_title" />
  <java-symbol type="string" name="install_carrier_app_notification_text" />
  <java-symbol type="string" name="install_carrier_app_notification_text_app_name" />
  <java-symbol type="string" name="install_carrier_app_notification_button" />
  <java-symbol type="string" name="carrier_app_notification_title" />
  <java-symbol type="string" name="carrier_app_notification_text" />
  <java-symbol type="string" name="negative_duration" />

  <java-symbol type="dimen" name="notification_messaging_spacing" />
  <java-symbol type="dimen" name="notification_messaging_spacing_conversation_group" />

  <java-symbol type="dimen" name="notification_text_margin_top" />
  <java-symbol type="dimen" name="notification_inbox_item_top_padding" />

  <!-- WallpaperManager config -->
  <java-symbol type="string" name="config_wallpaperCropperPackage" />
  <java-symbol type="string" name="expand_action_accessibility" />

  <java-symbol type="id" name="textSpacerNoTitle" />
  <java-symbol type="id" name="titleDividerNoCustom" />

  <java-symbol type="id" name="notification_messaging" />

  <java-symbol type="bool" name="config_sustainedPerformanceModeSupported" />

  <!-- Wearable input extract edit view -->
  <java-symbol type="drawable" name="ic_input_extract_action_go" />
  <java-symbol type="drawable" name="ic_input_extract_action_search" />
  <java-symbol type="drawable" name="ic_input_extract_action_send" />
  <java-symbol type="drawable" name="ic_input_extract_action_next" />
  <java-symbol type="drawable" name="ic_input_extract_action_done" />
  <java-symbol type="drawable" name="ic_input_extract_action_previous" />
  <java-symbol type="drawable" name="ic_input_extract_action_return" />

  <java-symbol type="fraction" name="input_extract_layout_height" />
  <java-symbol type="fraction" name="input_extract_layout_padding_left" />
  <java-symbol type="fraction" name="input_extract_layout_padding_left_no_action" />
  <java-symbol type="fraction" name="input_extract_layout_padding_right" />
  <java-symbol type="fraction" name="input_extract_text_margin_bottom" />
  <java-symbol type="fraction" name="input_extract_action_margin_bottom" />

  <java-symbol type="dimen" name="input_extract_action_button_width" />
  <java-symbol type="dimen" name="input_extract_action_button_height" />

  <java-symbol type="dimen" name="notification_action_list_height" />
  <java-symbol type="dimen" name="notification_action_emphasized_height" />

  <!-- TV Remote Service package -->
  <java-symbol type="string" name="config_tvRemoteServicePackage" />

  <!-- Notifications: MessagingStyle -->
  <java-symbol type="string" name="notification_messaging_title_template" />

  <!-- Notifications: CallStyle -->
  <java-symbol type="layout" name="notification_template_material_call" />
  <java-symbol type="layout" name="notification_template_material_big_call" />
  <java-symbol type="string" name="call_notification_answer_action" />
  <java-symbol type="string" name="call_notification_answer_video_action" />
  <java-symbol type="string" name="call_notification_decline_action" />
  <java-symbol type="string" name="call_notification_hang_up_action" />
  <java-symbol type="string" name="call_notification_incoming_text" />
  <java-symbol type="string" name="call_notification_ongoing_text" />
  <java-symbol type="string" name="call_notification_screening_text" />
  <java-symbol type="color" name="call_notification_decline_color"/>
  <java-symbol type="color" name="call_notification_answer_color"/>
  <java-symbol type="dimen" name="call_notification_collapsible_indent"/>
  <java-symbol type="dimen" name="call_notification_system_action_min_width"/>
  <java-symbol type="drawable" name="ic_call_answer" />
  <java-symbol type="drawable" name="ic_call_answer_video" />
  <java-symbol type="drawable" name="ic_call_decline" />
  <java-symbol type="id" name="verification_divider" />
  <java-symbol type="id" name="verification_icon" />
  <java-symbol type="id" name="verification_text" />
  <java-symbol type="string" name="notification_verified_content_description" />

  <!-- Notification handler / dashboard package -->
  <java-symbol type="string" name="config_notificationHandlerPackage" />

  <java-symbol type="bool" name="config_supportPreRebootSecurityLogs" />

  <java-symbol type="id" name="notification_action_list_margin_target" />
  <java-symbol type="dimen" name="notification_actions_padding_start"/>
  <java-symbol type="dimen" name="notification_actions_collapsed_priority_width"/>
  <!--prefer to use disabled content and surface alpha values for disabled actions-->
  <java-symbol type="dimen" name="notification_action_disabled_alpha" />
  <java-symbol type="dimen" name="notification_action_disabled_content_alpha" />
  <java-symbol type="dimen" name="notification_action_disabled_container_alpha" />
  <java-symbol type="id" name="tag_margin_end_when_icon_visible" />
  <java-symbol type="id" name="tag_margin_end_when_icon_gone" />
  <java-symbol type="id" name="tag_uses_right_icon_drawable" />
  <java-symbol type="id" name="tag_keep_when_showing_left_icon" />

  <!-- Override Wake Key Behavior When Screen is Off -->
  <java-symbol type="bool" name="config_wakeOnDpadKeyPress" />
  <java-symbol type="bool" name="config_wakeOnAssistKeyPress" />
  <java-symbol type="bool" name="config_wakeOnBackKeyPress" />

  <!-- Pinner Service -->
  <java-symbol type="array" name="config_defaultPinnerServiceFiles" />
  <java-symbol type="bool" name="config_pinnerCameraApp" />
  <java-symbol type="bool" name="config_pinnerHomeApp" />
  <java-symbol type="bool" name="config_pinnerAssistantApp" />

  <java-symbol type="string" name="config_doubleTouchGestureEnableFile" />

  <java-symbol type="string" name="suspended_widget_accessibility" />

  <java-symbol type="string" name="app_suspended_title" />
  <java-symbol type="string" name="app_suspended_more_details" />
  <java-symbol type="string" name="app_suspended_unsuspend_message" />
  <java-symbol type="string" name="app_suspended_default_message" />

  <java-symbol type="string" name="app_blocked_title" />
  <java-symbol type="string" name="app_blocked_message" />

  <java-symbol type="string" name="app_streaming_blocked_title" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_permission_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_camera_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_fingerprint_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_microphone_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_playstore_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_title_for_settings_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_message" />
  <java-symbol type="string" name="app_streaming_blocked_message_for_fingerprint_dialog" />
  <java-symbol type="string" name="app_streaming_blocked_message_for_settings_dialog" />

  <!-- Used internally for assistant to launch activity transitions -->
  <java-symbol type="id" name="cross_task_transition" />

  <java-symbol type="bool" name="config_useRoundIcon" />

  <!-- For System navigation keys -->
  <java-symbol type="bool" name="config_supportSystemNavigationKeys" />

  <java-symbol type="layout" name="unsupported_display_size_dialog_content" />
  <java-symbol type="string" name="unsupported_display_size_message" />

  <java-symbol type="layout" name="notification_material_action_emphasized" />
  <java-symbol type="layout" name="notification_material_action_emphasized_tombstone" />

  <!-- Package name for the device provisioning package -->
  <java-symbol type="string" name="config_deviceProvisioningPackage" />

  <!-- Colon separated list of package names that should be granted DND access -->
  <java-symbol type="string" name="config_defaultDndAccessPackages" />

  <!-- For NetworkPolicyManagerService -->
  <java-symbol type="string" name="config_networkOverLimitComponent" />
  <java-symbol type="string" name="config_dataUsageSummaryComponent" />

  <java-symbol type="string" name="lockscreen_storage_locked" />

  <java-symbol type="string" name="global_action_emergency" />
  <java-symbol type="string" name="config_emergency_call_number" />
  <java-symbol type="string" name="config_emergency_dialer_package" />
  <java-symbol type="array" name="config_emergency_iso_country_codes" />

  <java-symbol type="string" name="config_dozeDoubleTapSensorType" />
  <java-symbol type="string" name="config_dozeTapSensorType" />
  <java-symbol type="array" name="config_dozeTapSensorPostureMapping" />
  <java-symbol type="bool" name="config_dozePulsePickup" />
  <java-symbol type="bool" name="config_pulseOnNotificationsAvailable" />

  <!-- Used for MimeIconUtils. -->
  <java-symbol type="drawable" name="ic_doc_apk" />
  <java-symbol type="drawable" name="ic_doc_audio" />
  <java-symbol type="drawable" name="ic_doc_certificate" />
  <java-symbol type="drawable" name="ic_doc_codes" />
  <java-symbol type="drawable" name="ic_doc_compressed" />
  <java-symbol type="drawable" name="ic_doc_contact" />
  <java-symbol type="drawable" name="ic_doc_event" />
  <java-symbol type="drawable" name="ic_doc_font" />
  <java-symbol type="drawable" name="ic_doc_image" />
  <java-symbol type="drawable" name="ic_doc_pdf" />
  <java-symbol type="drawable" name="ic_doc_presentation" />
  <java-symbol type="drawable" name="ic_doc_spreadsheet" />
  <java-symbol type="drawable" name="ic_doc_document" />
  <java-symbol type="drawable" name="ic_doc_video" />
  <java-symbol type="drawable" name="ic_doc_word" />
  <java-symbol type="drawable" name="ic_doc_excel" />
  <java-symbol type="drawable" name="ic_doc_powerpoint" />
  <java-symbol type="drawable" name="ic_doc_folder" />
  <java-symbol type="drawable" name="ic_doc_audio" />
  <java-symbol type="drawable" name="ic_doc_image" />
  <java-symbol type="drawable" name="ic_doc_text" />
  <java-symbol type="drawable" name="ic_doc_video" />
  <java-symbol type="drawable" name="ic_doc_generic" />

  <java-symbol type="bool" name="config_setColorTransformAccelerated" />
  <java-symbol type="bool" name="config_setColorTransformAcceleratedPerLayer" />
  <java-symbol type="bool" name="config_nightDisplayAvailable" />
  <java-symbol type="bool" name="config_allowDisablingAssistDisclosure" />
  <java-symbol type="integer" name="config_defaultNightDisplayAutoMode" />
  <java-symbol type="integer" name="config_defaultNightDisplayCustomStartTime" />
  <java-symbol type="integer" name="config_defaultNightDisplayCustomEndTime" />
  <java-symbol type="integer" name="config_nightDisplayColorTemperatureDefault" />
  <java-symbol type="integer" name="config_nightDisplayColorTemperatureMin" />
  <java-symbol type="integer" name="config_nightDisplayColorTemperatureMax" />
  <java-symbol type="array" name="config_nightDisplayColorTemperatureCoefficients" />
  <java-symbol type="array" name="config_nightDisplayColorTemperatureCoefficientsNative" />
  <java-symbol type="bool" name="config_reduceBrightColorsAvailable" />
  <java-symbol type="array" name="config_reduceBrightColorsCoefficients" />
  <java-symbol type="array" name="config_reduceBrightColorsCoefficientsNonlinear" />
  <java-symbol type="integer" name="config_reduceBrightColorsStrengthDefault" />
  <java-symbol type="integer" name="config_reduceBrightColorsStrengthMin" />
  <java-symbol type="integer" name="config_reduceBrightColorsStrengthMax" />
  <java-symbol type="array" name="config_availableColorModes" />
  <java-symbol type="array" name="config_mappedColorModes" />
  <java-symbol type="string" name="config_vendorColorModesRestoreHint" />
  <java-symbol type="integer" name="config_accessibilityColorMode" />
  <java-symbol type="array" name="config_displayCompositionColorModes" />
  <java-symbol type="array" name="config_displayCompositionColorSpaces" />
  <java-symbol type="bool" name="config_displayWhiteBalanceAvailable" />
  <java-symbol type="bool" name="config_displayWhiteBalanceEnabledDefault" />
  <java-symbol type="integer" name="config_displayWhiteBalanceColorTemperatureMin" />
  <java-symbol type="integer" name="config_displayWhiteBalanceColorTemperatureMax" />
  <java-symbol type="integer" name="config_displayWhiteBalanceColorTemperatureDefault" />
  <java-symbol type="array" name="config_displayWhiteBalanceDisplayPrimaries" />
  <java-symbol type="array" name="config_displayWhiteBalanceDisplayNominalWhite" />
  <java-symbol type="integer" name="config_displayWhiteBalanceDisplayNominalWhiteCct" />
  <java-symbol type="array" name="config_displayWhiteBalanceDisplayRangeMinimums" />
  <java-symbol type="array" name="config_displayWhiteBalanceDisplaySteps" />
  <java-symbol type="bool" name="config_displayWhiteBalanceLightModeAllowed" />
  <java-symbol type="integer" name="config_displayWhiteBalanceTransitionTime" />

  <!-- Device states where the sensor based rotation values should be reversed around the Z axis
       for the default display.
       TODO(b/265312193): Remove this workaround when this bug is fixed.-->
  <java-symbol type="array" name="config_deviceStatesToReverseDefaultDisplayRotationAroundZAxis" />

  <!-- Boolean indicating whether secondary built-in displays should have their orientation
       match the active default display. This config assumes that the secondary display only
       requires swapping ROTATION_90 and ROTATION_270.
       TODO(b/265991392): This should eventually be configured and parsed in
        display_settings.xml -->
  <java-symbol type="bool" name="config_matchSecondaryInternalDisplaysOrientationToReverseDefaultDisplay" />

  <!-- Default user restrictions for the SYSTEM user -->
  <java-symbol type="array" name="config_defaultFirstUserRestrictions" />

  <java-symbol type="bool" name="config_permissionsIndividuallyControlled" />
  <java-symbol type="bool" name="config_wirelessConsentRequired" />

  <!-- Global actions icons -->
  <java-symbol type="drawable" name="ic_restart" />
  <java-symbol type="drawable" name="ic_screenshot" />
  <java-symbol type="drawable" name="ic_faster_emergency" />
  <java-symbol type="drawable" name="ic_media_seamless" />
  <java-symbol type="drawable" name="emergency_icon" />

  <java-symbol type="array" name="config_convert_to_emergency_number_map" />

  <java-symbol type="array" name="config_priorityOnlyDndExemptPackages" />

  <!-- Screen-size-dependent modes for picker dialogs. -->
  <java-symbol type="integer" name="time_picker_mode" />
  <java-symbol type="integer" name="date_picker_mode" />

  <java-symbol type="dimen" name="config_appTransitionAnimationDurationScaleDefault" />

  <!-- Network Recommendation -->
  <java-symbol type="string" name="config_defaultNetworkRecommendationProviderPackage" />

  <!-- Search Selector -->
  <java-symbol type="string" name="config_defaultSearchSelectorPackageName" />

  <!-- Captive Portal Login -->
  <java-symbol type="string" name="config_defaultCaptivePortalLoginPackageName" />

  <!-- Dock Manager -->
  <java-symbol type="string" name="config_defaultDockManagerPackageName" />

  <!-- Optional IPsec algorithms -->
  <java-symbol type="array" name="config_optionalIpSecAlgorithms" />

  <!-- Whether allow 3rd party apps on internal storage. -->
  <java-symbol type="bool" name="config_allow3rdPartyAppOnInternal" />

  <java-symbol type="bool" name="use_lock_pattern_drawable" />
  <java-symbol type="drawable" name="lockscreen_notselected" />
  <java-symbol type="drawable" name="lockscreen_selected" />

  <java-symbol type="string" name="notification_header_divider_symbol_with_spaces" />

  <java-symbol type="color" name="notification_primary_text_color_light" />
  <java-symbol type="color" name="notification_primary_text_color_dark" />
  <java-symbol type="color" name="notification_secondary_text_color_light" />
  <java-symbol type="color" name="notification_secondary_text_color_dark" />
  <java-symbol type="color" name="notification_default_color_light" />
  <java-symbol type="color" name="notification_default_color_dark" />
  <java-symbol type="dimen" name="notification_secondary_text_disabled_alpha" />

  <java-symbol type="string" name="app_category_game" />
  <java-symbol type="string" name="app_category_audio" />
  <java-symbol type="string" name="app_category_video" />
  <java-symbol type="string" name="app_category_image" />
  <java-symbol type="string" name="app_category_social" />
  <java-symbol type="string" name="app_category_news" />
  <java-symbol type="string" name="app_category_maps" />
  <java-symbol type="string" name="app_category_productivity" />
  <java-symbol type="string" name="app_category_accessibility" />

  <java-symbol type="raw" name="fallback_categories" />

  <java-symbol type="string" name="config_icon_mask" />
  <java-symbol type="string" name="config_batterymeterPerimeterPath" />
  <java-symbol type="string" name="config_batterymeterErrorPerimeterPath" />
  <java-symbol type="string" name="config_batterymeterFillMask" />
  <java-symbol type="string" name="config_batterymeterBoltPath" />
  <java-symbol type="string" name="config_batterymeterPowersavePath" />
  <java-symbol type="bool" name="config_batterymeterDualTone" />
  <java-symbol type="string" name="config_signalAttributionPath" />
  <java-symbol type="dimen" name="config_signalCutoutWidthFraction" />
  <java-symbol type="dimen" name="config_signalCutoutHeightFraction" />

  <java-symbol type="bool" name="config_debugEnableAutomaticSystemServerHeapDumps" />
  <java-symbol type="integer" name="config_debugSystemServerPssThresholdBytes" />

  <!-- Accessibility Shortcut -->
  <java-symbol type="string" name="accessibility_shortcut_single_service_warning_title" />
  <java-symbol type="string" name="accessibility_shortcut_single_service_warning" />
  <java-symbol type="string" name="accessibility_shortcut_multiple_service_warning_title" />
  <java-symbol type="string" name="accessibility_shortcut_multiple_service_warning" />
  <java-symbol type="string" name="accessibility_shortcut_multiple_service_list" />
  <java-symbol type="string" name="accessibility_shortcut_on" />
  <java-symbol type="string" name="accessibility_shortcut_off" />
  <java-symbol type="string" name="accessibility_shortcut_enabling_service" />
  <java-symbol type="string" name="accessibility_shortcut_disabling_service" />
  <java-symbol type="string" name="color_inversion_feature_name" />
  <java-symbol type="string" name="color_correction_feature_name" />
  <java-symbol type="string" name="reduce_bright_colors_feature_name" />
  <java-symbol type="string" name="config_defaultAccessibilityService" />
  <java-symbol type="string" name="config_defaultAccessibilityNotificationSound" />
  <java-symbol type="string" name="accessibility_shortcut_spoken_feedback" />

  <java-symbol type="string" name="accessibility_select_shortcut_menu_title" />
  <java-symbol type="string" name="accessibility_edit_shortcut_menu_button_title" />
  <java-symbol type="string" name="accessibility_edit_shortcut_menu_volume_title" />
  <java-symbol type="string" name="accessibility_uncheck_legacy_item_warning" />

  <java-symbol type="layout" name="accessibility_enable_service_warning" />
  <java-symbol type="id" name="accessibility_permissionDialog_icon" />
  <java-symbol type="id" name="accessibility_permissionDialog_title" />
  <java-symbol type="id" name="accessibility_permission_enable_allow_button" />
  <java-symbol type="id" name="accessibility_permission_enable_deny_button" />
  <java-symbol type="string" name="accessibility_enable_service_title" />

  <java-symbol type="layout" name="accessibility_shortcut_chooser_item" />
  <java-symbol type="id" name="accessibility_shortcut_target_checkbox" />
  <java-symbol type="id" name="accessibility_shortcut_target_icon" />
  <java-symbol type="id" name="accessibility_shortcut_target_label" />
  <java-symbol type="id" name="accessibility_shortcut_target_status" />
  <java-symbol type="string" name="accessibility_shortcut_menu_item_status_on" />
  <java-symbol type="string" name="accessibility_shortcut_menu_item_status_off" />

  <!-- Accessibility Button -->
  <java-symbol type="layout" name="accessibility_button_chooser" />
  <java-symbol type="layout" name="accessibility_button_chooser_item" />
  <java-symbol type="id" name="accessibility_button_target_icon" />
  <java-symbol type="id" name="accessibility_button_target_label" />
  <java-symbol type="id" name="accessibility_button_chooser_grid" />
  <java-symbol type="id" name="accessibility_button_prompt" />
  <java-symbol type="id" name="accessibility_button_prompt_prologue" />
  <java-symbol type="string" name="accessibility_gesture_prompt_text" />
  <java-symbol type="string" name="accessibility_gesture_3finger_prompt_text" />
  <java-symbol type="string" name="accessibility_gesture_instructional_text" />
  <java-symbol type="string" name="accessibility_gesture_3finger_instructional_text" />

  <java-symbol type="string" name="accessibility_magnification_chooser_text" />
  <java-symbol type="string" name="edit_accessibility_shortcut_menu_button" />
  <java-symbol type="string" name="done_accessibility_shortcut_menu_button" />

  <java-symbol type="drawable" name="ic_accessibility_color_inversion" />
  <java-symbol type="drawable" name="ic_accessibility_color_correction" />
  <java-symbol type="drawable" name="ic_accessibility_hearing_aid" />
  <java-symbol type="drawable" name="ic_accessibility_magnification" />
  <java-symbol type="drawable" name="ic_accessibility_reduce_bright_colors" />
  <java-symbol type="drawable" name="ic_accessibility_one_handed" />

  <java-symbol type="string" name="hearing_aids_feature_name" />
  <java-symbol type="string" name="reduce_bright_colors_feature_name" />
  <java-symbol type="string" name="one_handed_mode_feature_name" />

  <!-- com.android.internal.widget.RecyclerView -->
  <java-symbol type="id" name="item_touch_helper_previous_elevation"/>
  <java-symbol type="dimen" name="item_touch_helper_max_drag_scroll_per_frame"/>
  <java-symbol type="dimen" name="item_touch_helper_swipe_escape_velocity"/>
  <java-symbol type="dimen" name="item_touch_helper_swipe_escape_max_velocity"/>

  <!-- com.android.server.autofill -->
  <java-symbol type="layout" name="autofill_save"/>
  <java-symbol type="layout" name="autofill_dataset_picker"/>
  <java-symbol type="layout" name="autofill_dataset_picker_fullscreen"/>
  <java-symbol type="layout" name="autofill_dataset_picker_header_footer"/>
  <java-symbol type="layout" name="autofill_fill_dialog"/>
  <java-symbol type="id" name="autofill_save_icon"/>
  <java-symbol type="id" name="autofill" />
  <java-symbol type="id" name="autofill_dataset_footer"/>
  <java-symbol type="id" name="autofill_dataset_header"/>
  <java-symbol type="id" name="autofill_dataset_icon" />
  <java-symbol type="id" name="autofill_dataset_list"/>
  <java-symbol type="id" name="autofill_dataset_picker"/>
  <java-symbol type="id" name="autofill_dataset_title" />
  <java-symbol type="id" name="autofill_save_custom_subtitle" />
  <java-symbol type="id" name="autofill_save_icon" />
  <java-symbol type="id" name="autofill_save_no" />
  <java-symbol type="id" name="autofill_save_title" />
  <java-symbol type="id" name="autofill_save_yes" />
  <java-symbol type="id" name="autofill_service_icon" />
  <java-symbol type="id" name="autofill_dialog_picker"/>
  <java-symbol type="id" name="autofill_dialog_header"/>
  <java-symbol type="id" name="autofill_dialog_container"/>
  <java-symbol type="id" name="autofill_dialog_list"/>
  <java-symbol type="id" name="autofill_dialog_no" />
  <java-symbol type="id" name="autofill_dialog_yes" />
  <java-symbol type="string" name="autofill_error_cannot_autofill" />
  <java-symbol type="string" name="autofill_picker_no_suggestions" />
  <java-symbol type="string" name="autofill_picker_some_suggestions" />
  <java-symbol type="string" name="autofill" />
  <java-symbol type="string" name="autofill_picker_accessibility_title " />
  <java-symbol type="string" name="autofill_update_title" />
  <java-symbol type="string" name="autofill_update_title_with_type" />
  <java-symbol type="string" name="autofill_update_title_with_2types" />
  <java-symbol type="string" name="autofill_update_title_with_3types" />
  <java-symbol type="string" name="autofill_update_yes" />
  <java-symbol type="string" name="autofill_continue_yes" />
  <java-symbol type="string" name="autofill_save_accessibility_title " />
  <java-symbol type="string" name="autofill_save_title" />
  <java-symbol type="string" name="autofill_save_title_with_type" />
  <java-symbol type="string" name="autofill_save_title_with_2types" />
  <java-symbol type="string" name="autofill_save_title_with_3types" />
  <java-symbol type="string" name="autofill_save_yes" />
  <java-symbol type="string" name="autofill_save_no" />
  <java-symbol type="string" name="autofill_save_notnow" />
  <java-symbol type="string" name="autofill_save_never" />
  <java-symbol type="string" name="autofill_save_type_password" />
  <java-symbol type="string" name="autofill_save_type_address" />
  <java-symbol type="string" name="autofill_save_type_credit_card" />
  <java-symbol type="string" name="autofill_save_type_debit_card" />
  <java-symbol type="string" name="autofill_save_type_payment_card" />
  <java-symbol type="string" name="autofill_save_type_generic_card" />
  <java-symbol type="string" name="autofill_save_type_username" />
  <java-symbol type="string" name="autofill_save_type_email_address" />
  <java-symbol type="drawable" name="autofill_dataset_picker_background" />
  <java-symbol type="style" name="AutofillDatasetPicker" />
  <java-symbol type="style" name="AutofillHalfScreenAnimation" />
  <java-symbol type="style" name="AutofillSaveAnimation" />
  <java-symbol type="dimen" name="autofill_dataset_picker_max_width"/>
  <java-symbol type="dimen" name="autofill_dataset_picker_max_height"/>
  <java-symbol type="dimen" name="autofill_save_custom_subtitle_max_height"/>
  <java-symbol type="integer" name="autofill_max_visible_datasets" />
  <java-symbol type="dimen" name="autofill_dialog_max_width" />
  <java-symbol type="dimen" name="autofill_dialog_offset"/>
  <java-symbol type="dimen" name="autofill_save_outer_margin"/>

  <java-symbol type="bool" name="autofill_dialog_horizontal_space_included"/>

  <java-symbol type="style" name="Theme.DeviceDefault.Autofill" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Autofill" />
  <java-symbol type="style" name="Theme.DeviceDefault.Autofill.Save" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Autofill.Save" />

  <java-symbol type="dimen" name="notification_small_icon_size"/>
  <java-symbol type="dimen" name="notification_big_picture_max_height"/>
  <java-symbol type="dimen" name="notification_big_picture_max_width"/>
  <java-symbol type="dimen" name="notification_right_icon_size"/>
  <java-symbol type="dimen" name="notification_right_icon_content_margin"/>
  <java-symbol type="dimen" name="notification_actions_icon_drawable_size"/>
  <java-symbol type="dimen" name="notification_custom_view_max_image_height"/>
  <java-symbol type="dimen" name="notification_custom_view_max_image_width"/>
  <java-symbol type="dimen" name="notification_person_icon_max_size" />

  <java-symbol type="dimen" name="notification_small_icon_size_low_ram"/>
  <java-symbol type="dimen" name="notification_big_picture_max_height_low_ram"/>
  <java-symbol type="dimen" name="notification_big_picture_max_width_low_ram"/>
  <java-symbol type="dimen" name="notification_right_icon_size_low_ram"/>
  <java-symbol type="dimen" name="notification_grayscale_icon_max_size"/>
  <java-symbol type="dimen" name="notification_custom_view_max_image_height_low_ram"/>
  <java-symbol type="dimen" name="notification_custom_view_max_image_width_low_ram"/>
  <java-symbol type="dimen" name="notification_person_icon_max_size_low_ram" />

  <!-- Accessibility fingerprint gestures -->
  <java-symbol type="string" name="capability_title_canCaptureFingerprintGestures" />
  <java-symbol type="string" name="capability_desc_canCaptureFingerprintGestures" />

  <!-- android.service.trust -->
  <java-symbol type="bool" name="config_allowEscrowTokenForTrustAgent"/>
  <java-symbol type="string" name="config_defaultTrustAgent" />

  <java-symbol type="id" name="toggle_mode"/>
  <java-symbol type="id" name="input_mode"/>
  <java-symbol type="id" name="input_header"/>
  <java-symbol type="id" name="input_separator"/>
  <java-symbol type="id" name="input_hour"/>
  <java-symbol type="id" name="input_minute"/>
  <java-symbol type="id" name="am_pm_spinner"/>
  <java-symbol type="id" name="label_hour"/>
  <java-symbol type="id" name="label_minute"/>
  <java-symbol type="id" name="label_error"/>
  <java-symbol type="layout" name="time_picker_text_input_material"/>
  <java-symbol type="drawable" name="btn_keyboard_key_material"/>
  <java-symbol type="drawable" name="btn_clock_material"/>
  <java-symbol type="string" name="time_picker_text_input_mode_description"/>
  <java-symbol type="string" name="time_picker_radial_mode_description"/>

  <!-- resolver activity -->
  <java-symbol type="drawable" name="resolver_icon_placeholder" />

  <!-- Alert windows notification -->
  <java-symbol type="string" name="alert_windows_notification_channel_group_name" />
  <java-symbol type="string" name="alert_windows_notification_channel_name" />
  <java-symbol type="string" name="alert_windows_notification_title" />
  <java-symbol type="string" name="alert_windows_notification_message" />
  <java-symbol type="string" name="alert_windows_notification_turn_off_action" />
  <java-symbol type="drawable" name="alert_window_layer" />
  <java-symbol type="style" name="Widget.LockPatternView" />
  <java-symbol type="attr" name="lockPatternStyle" />

  <java-symbol type="string" name="expand_button_content_description_collapsed" />
  <java-symbol type="string" name="expand_button_content_description_expanded" />

  <!-- Colon separated list of package names that should be granted Notification Listener access -->
  <java-symbol type="string" name="config_defaultListenerAccessPackages" />

  <!-- maximum width of the display -->
  <java-symbol type="integer" name="config_maxUiWidth" />

  <!-- system notification channels -->
  <java-symbol type="string" name="notification_channel_physical_keyboard" />
  <java-symbol type="string" name="notification_channel_security" />
  <java-symbol type="string" name="notification_channel_car_mode" />
  <java-symbol type="string" name="notification_channel_account" />
  <java-symbol type="string" name="notification_channel_developer" />
  <java-symbol type="string" name="notification_channel_developer_important" />
  <java-symbol type="string" name="notification_channel_updates" />
  <java-symbol type="string" name="notification_channel_network_status" />
  <java-symbol type="string" name="notification_channel_network_alerts" />
  <java-symbol type="string" name="notification_channel_network_available" />
  <java-symbol type="array" name="config_defaultCloudSearchServices" />
  <java-symbol type="string" name="notification_channel_vpn" />
  <java-symbol type="string" name="notification_channel_device_admin" />
  <java-symbol type="string" name="notification_channel_alerts" />
  <java-symbol type="string" name="notification_channel_retail_mode" />
  <java-symbol type="string" name="notification_channel_usb" />
  <java-symbol type="string" name="notification_channel_heavy_weight_app" />
  <java-symbol type="string" name="notification_channel_system_changes" />
  <java-symbol type="string" name="notification_channel_do_not_disturb" />
  <java-symbol type="string" name="notification_channel_accessibility_magnification" />
  <java-symbol type="string" name="notification_channel_accessibility_security_policy" />
  <java-symbol type="string" name="config_defaultAutofillService" />
  <java-symbol type="string" name="config_defaultFieldClassificationService" />
  <java-symbol type="string" name="config_defaultOnDeviceSpeechRecognitionService" />
  <java-symbol type="string" name="config_defaultTextClassifierPackage" />
  <java-symbol type="string" name="config_defaultWellbeingPackage" />
  <java-symbol type="string" name="config_defaultContentCaptureService" />
  <java-symbol type="string" name="config_defaultContentProtectionService" />
  <java-symbol type="string" name="config_defaultAugmentedAutofillService" />
  <java-symbol type="string" name="config_defaultTranslationService" />
  <java-symbol type="string" name="config_defaultAppPredictionService" />
  <java-symbol type="string" name="config_defaultContentSuggestionsService" />
  <java-symbol type="string" name="config_defaultCredentialManagerHybridService" />
  <java-symbol type="array" name="config_enabledCredentialProviderService" />
  <java-symbol type="array" name="config_primaryCredentialProviderService" />
  <java-symbol type="string" name="config_defaultSearchUiService" />
  <java-symbol type="string" name="config_defaultSmartspaceService" />
  <java-symbol type="string" name="config_defaultWallpaperEffectsGenerationService" />
  <java-symbol type="string" name="config_defaultMusicRecognitionService" />
  <java-symbol type="string" name="config_defaultAttentionService" />
  <java-symbol type="string" name="config_defaultRotationResolverService" />
  <java-symbol type="string" name="config_defaultSystemCaptionsManagerService" />
  <java-symbol type="string" name="config_defaultAmbientContextDetectionService" />
  <java-symbol type="string" name="config_defaultAmbientContextConsentComponent" />
  <java-symbol type="string" name="config_defaultWearableSensingConsentComponent" />
  <java-symbol type="string" name="config_wearableAmbientContextPackageNameExtraKey" />
  <java-symbol type="string" name="config_wearableAmbientContextEventArrayExtraKey" />
  <java-symbol type="array" name="config_defaultAmbientContextServices" />
  <java-symbol type="string" name="config_ambientContextPackageNameExtraKey" />
  <java-symbol type="string" name="config_ambientContextEventArrayExtraKey" />
  <java-symbol type="string" name="config_defaultWearableSensingService" />
  <java-symbol type="string" name="config_retailDemoPackage" />
  <java-symbol type="string" name="config_retailDemoPackageSignature" />

  <java-symbol type="bool" name="config_systemCaptionsServiceCallsEnabled" />

  <java-symbol type="string" name="notification_channel_foreground_service" />
  <java-symbol type="string" name="foreground_service_app_in_background" />
  <java-symbol type="string" name="foreground_service_apps_in_background" />
  <java-symbol type="string" name="foreground_service_tap_for_details" />
  <java-symbol type="string" name="foreground_service_multiple_separator" />

  <java-symbol type="bool" name="config_enableCredentialFactoryResetProtection" />

  <!-- ETWS primary messages -->
  <java-symbol type="string" name="etws_primary_default_message_earthquake" />
  <java-symbol type="string" name="etws_primary_default_message_tsunami" />
  <java-symbol type="string" name="etws_primary_default_message_earthquake_and_tsunami" />
  <java-symbol type="string" name="etws_primary_default_message_test" />
  <java-symbol type="string" name="etws_primary_default_message_others" />

  <java-symbol type="bool" name="config_quickSettingsSupported" />

  <java-symbol type="style" name="Theme.DeviceDefault.SystemUI" />

  <java-symbol type="integer" name="default_data_warning_level_mb" />
  <java-symbol type="bool" name="config_useVideoPauseWorkaround" />
  <java-symbol type="bool" name="config_sendPackageName" />
  <java-symbol type="string" name="config_helpPackageNameKey" />
  <java-symbol type="string" name="config_helpPackageNameValue" />
  <java-symbol type="string" name="config_helpIntentExtraKey" />
  <java-symbol type="string" name="config_helpIntentNameKey" />
  <java-symbol type="string" name="config_feedbackIntentExtraKey" />
  <java-symbol type="string" name="config_feedbackIntentNameKey" />

  <java-symbol type="array" name="config_hideWhenDisabled_packageNames" />

  <java-symbol type="string" name="config_dozeLongPressSensorType" />
  <java-symbol type="string" name="config_dozeUdfpsLongPressSensorType" />
  <java-symbol type="bool" name="config_dozeWakeLockScreenSensorAvailable" />
  <java-symbol type="integer" name="config_dozeWakeLockScreenDebounce" />
  <java-symbol type="string" name="config_quickPickupSensorType" />

  <java-symbol type="array" name="config_allowedGlobalInstantAppSettings" />
  <java-symbol type="array" name="config_allowedSystemInstantAppSettings" />
  <java-symbol type="array" name="config_allowedSecureInstantAppSettings" />

  <java-symbol type="bool" name="config_handleVolumeKeysInWindowManager" />
  <java-symbol type="bool" name="config_handleVolumeAliasesUsingVolumeGroups" />
  <java-symbol type="dimen" name="config_inCallNotificationVolume" />
  <java-symbol type="string" name="config_inCallNotificationSound" />
  <java-symbol type="string" name="config_cameraShutterSound" />
  <java-symbol type="integer" name="config_autoGroupAtCount" />
  <java-symbol type="bool" name="config_dozeAlwaysOnDisplayAvailable" />
  <java-symbol type="bool" name="config_dozePickupGestureEnabled" />
  <java-symbol type="bool" name="config_dozeAlwaysOnEnabled" />
  <java-symbol type="bool" name="config_dozeSupportsAodWallpaper" />
  <java-symbol type="bool" name="config_displayBlanksAfterDoze" />
  <java-symbol type="bool" name="config_displayBrightnessBucketsInDoze" />
  <java-symbol type="bool" name="config_displayColorFadeDisabled" />
  <java-symbol type="integer" name="config_storageManagerDaystoRetainDefault" />
  <java-symbol type="string" name="config_headlineFontFamily" />
  <java-symbol type="string" name="config_headlineFontFamilyMedium" />

  <java-symbol type="drawable" name="stat_sys_vitals" />

  <java-symbol type="color" name="text_color_primary" />
  <java-symbol type="color" name="material_grey_300" />
  <java-symbol type="dimen" name="emphasized_button_stroke_width" />
  <java-symbol type="dimen" name="button_inset_vertical_material" />

  <java-symbol type="array" name="config_batteryPackageTypeSystem" />
  <java-symbol type="array" name="config_batteryPackageTypeService" />

  <java-symbol type="string" name="popup_window_default_title" />
  <java-symbol type="bool" name="config_showAreaUpdateInfoSettings" />
  <java-symbol type="layout" name="shutdown_dialog" />
  <java-symbol type="bool" name="config_showSysuiShutdown" />
  <java-symbol type="drawable" name="chooser_file_generic" />

  <java-symbol type="layout" name="notification_template_messaging_text_message" />
  <java-symbol type="layout" name="notification_template_messaging_image_message" />
  <java-symbol type="layout" name="notification_template_messaging_group" />
  <java-symbol type="id" name="message_text" />
  <java-symbol type="id" name="message_name" />
  <java-symbol type="id" name="message_icon" />
  <java-symbol type="id" name="group_message_container" />
  <java-symbol type="id" name="tag_top_animator" />
  <java-symbol type="id" name="tag_top_override" />
  <java-symbol type="id" name="tag_layout_top" />
  <java-symbol type="id" name="tag_is_first_layout" />
  <java-symbol type="id" name="tag_alpha_animator" />
  <java-symbol type="id" name="clip_children_set_tag" />
  <java-symbol type="id" name="clip_to_padding_tag" />
  <java-symbol type="id" name="clip_children_tag" />
  <java-symbol type="id" name="bubble_button" />
  <java-symbol type="id" name="snooze_button" />
  <java-symbol type="dimen" name="text_size_body_2_material" />
  <java-symbol type="dimen" name="notification_icon_circle_size" />
  <java-symbol type="dimen" name="messaging_avatar_size" />
  <java-symbol type="dimen" name="messaging_group_sending_progress_size" />
  <java-symbol type="dimen" name="messaging_image_rounding" />
  <java-symbol type="dimen" name="messaging_image_min_size" />
  <java-symbol type="dimen" name="messaging_image_max_height" />
  <java-symbol type="dimen" name="messaging_image_extra_spacing" />
  <java-symbol type="id" name="messaging_group_icon_container" />
  <java-symbol type="id" name="messaging_group_sending_progress" />
  <java-symbol type="id" name="messaging_group_sending_progress_container" />
  <java-symbol type="bool" name="config_supportsSeamlessRefreshRateSwitching" />

  <java-symbol type="integer" name="config_stableDeviceDisplayWidth" />
  <java-symbol type="integer" name="config_stableDeviceDisplayHeight" />
  <java-symbol type="array" name="config_display_no_service_when_sim_unready" />

  <java-symbol type="layout" name="slice_grid" />
  <java-symbol type="layout" name="slice_message_local" />
  <java-symbol type="layout" name="slice_message" />
  <java-symbol type="layout" name="slice_title" />
  <java-symbol type="layout" name="slice_secondary_text" />
  <java-symbol type="layout" name="slice_remote_input" />
  <java-symbol type="layout" name="slice_small_template" />
  <java-symbol type="id" name="remote_input_progress" />
  <java-symbol type="id" name="remote_input_send" />
  <java-symbol type="id" name="remote_input" />
  <java-symbol type="dimen" name="slice_shortcut_size" />
  <java-symbol type="dimen" name="slice_icon_size" />
  <java-symbol type="dimen" name="slice_padding" />
  <java-symbol type="string" name="slice_more_content" />

  <java-symbol type="string" name="shortcut_restored_on_lower_version" />
  <java-symbol type="string" name="shortcut_restore_not_supported" />
  <java-symbol type="string" name="shortcut_restore_signature_mismatch" />
  <java-symbol type="string" name="shortcut_restore_unknown_issue" />

  <java-symbol type="bool" name="config_swipe_up_gesture_setting_available" />

  <!-- From media projection -->
  <java-symbol type="string" name="config_mediaProjectionPermissionDialogComponent" />
  <java-symbol type="string" name="config_batterySaverDeviceSpecificConfig" />

  <!-- Compile SDK check -->
  <java-symbol type="layout" name="unsupported_compile_sdk_dialog_content" />
  <java-symbol type="string" name="unsupported_compile_sdk_message" />
  <java-symbol type="string" name="unsupported_compile_sdk_check_update" />

  <java-symbol type="string" name="keyguard_accessibility_pattern_unlock" />
  <java-symbol type="string" name="keyguard_accessibility_pin_unlock" />
  <java-symbol type="string" name="keyguard_accessibility_sim_pin_unlock" />
  <java-symbol type="string" name="keyguard_accessibility_sim_puk_unlock" />
  <java-symbol type="string" name="keyguard_accessibility_password_unlock" />

  <java-symbol type="dimen" name="status_bar_height_portrait" />
  <java-symbol type="dimen" name="status_bar_height_landscape" />

  <java-symbol type="string" name="global_action_logout" />
  <java-symbol type="string" name="config_mainBuiltInDisplayCutout" />
  <java-symbol type="string" name="config_mainBuiltInDisplayCutoutRectApproximation" />
  <java-symbol type="drawable" name="messaging_user" />
  <java-symbol type="bool" name="config_fillMainBuiltInDisplayCutout" />
  <java-symbol type="drawable" name="ic_logout" />

  <java-symbol type="bool" name="config_enableIdleScreenBrightnessMode" />
  <java-symbol type="array" name="config_autoBrightnessDisplayValuesNits" />
  <java-symbol type="array" name="config_autoBrightnessDisplayValuesNitsIdle" />
  <java-symbol type="array" name="config_screenBrightnessBacklight" />
  <java-symbol type="array" name="config_screenBrightnessNits" />

  <java-symbol type="string" name="shortcut_disabled_reason_unknown" />

  <java-symbol type="string" name="harmful_app_warning_uninstall" />
  <java-symbol type="string" name="harmful_app_warning_open_anyway" />
  <java-symbol type="string" name="harmful_app_warning_title" />
  <java-symbol type="layout" name="harmful_app_warning_dialog" />

  <java-symbol type="string" name="config_defaultAssistantAccessComponent" />

  <java-symbol type="string" name="slices_permission_request" />

  <java-symbol type="string" name="screenshot_edit" />

  <java-symbol type="bool" name="config_keepRestrictedProfilesInBackground" />

  <java-symbol type="array" name="config_ringtoneEffectUris" />

  <!-- For Wear devices -->
  <java-symbol type="array" name="config_wearActivityModeRadios" />

  <java-symbol type="string" name="zen_upgrade_notification_title" />
  <java-symbol type="string" name="zen_upgrade_notification_content" />
  <java-symbol type="string" name="zen_upgrade_notification_visd_title" />
  <java-symbol type="string" name="zen_upgrade_notification_visd_content" />

  <java-symbol type="string" name="review_notification_settings_title" />
  <java-symbol type="string" name="review_notification_settings_text" />
  <java-symbol type="string" name="review_notification_settings_remind_me_action" />
  <java-symbol type="string" name="review_notification_settings_dismiss" />

  <java-symbol type="string" name="config_managed_provisioning_package" />

  <java-symbol type="string" name="notification_app_name_system" />
  <java-symbol type="string" name="notification_app_name_settings" />

  <java-symbol type="integer" name="config_lowBatteryAutoTriggerDefaultLevel" />
  <java-symbol type="bool" name="config_batterySaverStickyBehaviourDisabled" />
  <java-symbol type="integer" name="config_dynamicPowerSavingsDefaultDisableThreshold" />
  <java-symbol type="string" name="config_batterySaverScheduleProvider" />
  <java-symbol type="string" name="config_powerSaveModeChangedListenerPackage" />

  <!-- For car devices -->
  <java-symbol type="string" name="car_loading_profile" />
  <java-symbol type="color" name="car_body1_light" />
  <java-symbol type="color" name="car_user_switcher_user_image_bgcolor" />
  <java-symbol type="color" name="car_user_switcher_user_image_fgcolor" />
  <java-symbol type="color" name="car_card_dark" />
  <java-symbol type="dimen" name="car_body1_size" />
  <java-symbol type="dimen" name="car_padding_4" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.Alert.UserSwitchingDialog" />

  <java-symbol type="string" name="battery_saver_description_with_learn_more" />
  <java-symbol type="string" name="confirm_battery_saver" />

  <java-symbol type="attr" name="opticalInsetLeft" />
  <java-symbol type="attr" name="opticalInsetTop" />
  <java-symbol type="attr" name="opticalInsetRight" />
  <java-symbol type="attr" name="opticalInsetBottom" />

  <java-symbol type="drawable" name="ic_lock_lockdown" />
  <java-symbol type="drawable" name="ic_arrow_forward" />
  <java-symbol type="drawable" name="ic_permission" />

  <java-symbol type="integer" name="config_defaultAlarmVibrationIntensity" />
  <java-symbol type="integer" name="config_defaultHapticFeedbackIntensity" />
  <java-symbol type="integer" name="config_defaultMediaVibrationIntensity" />
  <java-symbol type="integer" name="config_defaultNotificationVibrationIntensity" />
  <java-symbol type="integer" name="config_defaultRingVibrationIntensity" />

  <java-symbol type="bool" name="config_maskMainBuiltInDisplayCutout" />

  <java-symbol type="string" name="config_customCountryDetector" />

  <java-symbol type="integer" name="config_maxScanTasksForHomeVisibility" />

  <!-- For Foldables -->
  <java-symbol type="array" name="config_openDeviceStates" />
  <java-symbol type="array" name="config_foldedDeviceStates" />
  <java-symbol type="array" name="config_halfFoldedDeviceStates" />
  <java-symbol type="array" name="config_rearDisplayDeviceStates" />
  <java-symbol type="bool" name="config_windowManagerHalfFoldAutoRotateOverride" />
  <java-symbol type="bool" name="config_windowManagerPauseRotationWhenUnfolding" />
  <java-symbol type="integer" name="config_pauseRotationWhenUnfolding_hingeEventTimeout" />
  <java-symbol type="integer" name="config_pauseRotationWhenUnfolding_maxHingeAngle" />
  <java-symbol type="integer" name="config_pauseRotationWhenUnfolding_displaySwitchTimeout" />
  <java-symbol type="array" name="config_deviceStatesOnWhichToWakeUp" />
  <java-symbol type="array" name="config_deviceStatesOnWhichToSleep" />
  <java-symbol type="string" name="config_foldedArea" />
  <java-symbol type="bool" name="config_supportsConcurrentInternalDisplays" />
  <java-symbol type="bool" name="config_unfoldTransitionEnabled" />
  <java-symbol type="bool" name="config_unfoldTransitionHingeAngle" />
  <java-symbol type="integer" name="config_unfoldTransitionHalfFoldedTimeout" />
  <java-symbol type="array" name="config_perDeviceStateRotationLockDefaults" />


  <java-symbol type="array" name="config_disableApksUnlessMatchedSku_apk_list" />
  <java-symbol type="array" name="config_disableApkUnlessMatchedSku_skus_list" />

  <java-symbol type="string" name="config_misprovisionedDeviceModel" />
  <java-symbol type="string" name="config_misprovisionedBrandValue" />

  <java-symbol type="integer" name="db_wal_truncate_size" />

  <!-- For Bluetooth AbsoluteVolume -->
  <java-symbol type="fraction" name="config_prescaleAbsoluteVolume_index1" />
  <java-symbol type="fraction" name="config_prescaleAbsoluteVolume_index2" />
  <java-symbol type="fraction" name="config_prescaleAbsoluteVolume_index3" />

  <java-symbol type="bool" name="config_useSmsAppService" />

  <java-symbol type="id" name="transition_overlay_view_tag" />
  <java-symbol type="id" name="notification_custom_view_index_tag" />

  <java-symbol type="dimen" name="rounded_corner_radius" />
  <java-symbol type="dimen" name="rounded_corner_radius_top" />
  <java-symbol type="dimen" name="rounded_corner_radius_bottom" />
  <java-symbol type="dimen" name="rounded_corner_radius_adjustment" />
  <java-symbol type="dimen" name="rounded_corner_radius_top_adjustment" />
  <java-symbol type="dimen" name="rounded_corner_radius_bottom_adjustment" />
  <java-symbol type="bool" name="config_supportsRoundedCornersOnWindows" />

  <java-symbol type="string" name="config_defaultModuleMetadataProvider" />

  <!-- For Secondary Launcher -->
  <java-symbol type="string" name="config_secondaryHomePackage" />
  <java-symbol type="bool" name="config_useSystemProvidedLauncherForSecondary" />

  <java-symbol type="string" name="battery_saver_notification_channel_name" />
  <java-symbol type="string" name="battery_saver_off_notification_title" />
  <java-symbol type="string" name="battery_saver_charged_notification_summary" />
  <java-symbol type="string" name="dynamic_mode_notification_channel_name" />
  <java-symbol type="string" name="dynamic_mode_notification_title" />
  <java-symbol type="string" name="dynamic_mode_notification_summary" />
  <java-symbol type="drawable" name="ic_battery" />

  <java-symbol type="bool" name="config_skipSensorAvailable" />
  <java-symbol type="bool" name="config_silenceSensorAvailable" />

  <java-symbol type="bool" name="config_zramWriteback" />

  <!-- For CBRS -->
  <java-symbol type="bool" name="config_cbrs_supported" />

  <java-symbol type="bool" name="config_awareSettingAvailable" />

  <!-- For Attention Service -->
  <java-symbol type="integer" name="config_attentionMaximumExtension" />

  <java-symbol type="string" name="config_incidentReportApproverPackage" />
  <java-symbol type="array" name="config_restrictedImagesServices" />

  <!-- Display White-Balance -->
  <java-symbol type="integer" name="config_displayWhiteBalanceBrightnessSensorRate" />
  <java-symbol type="integer" name="config_displayWhiteBalanceBrightnessFilterHorizon" />
  <java-symbol type="dimen" name="config_displayWhiteBalanceBrightnessFilterIntercept" />
  <java-symbol type="string" name="config_displayWhiteBalanceColorTemperatureSensorName" />
  <java-symbol type="integer" name="config_displayWhiteBalanceColorTemperatureSensorRate" />
  <java-symbol type="integer" name="config_displayWhiteBalanceColorTemperatureFilterHorizon" />
  <java-symbol type="dimen" name="config_displayWhiteBalanceColorTemperatureFilterIntercept" />
  <java-symbol type="integer" name="config_displayWhiteBalanceIncreaseDebounce" />
  <java-symbol type="integer" name="config_displayWhiteBalanceDecreaseDebounce" />
  <java-symbol type="array" name="config_displayWhiteBalanceBaseThresholds" />
  <java-symbol type="array" name="config_displayWhiteBalanceIncreaseThresholds" />
  <java-symbol type="array" name="config_displayWhiteBalanceDecreaseThresholds" />
  <java-symbol type="array" name="config_displayWhiteBalanceLowLightAmbientBrightnesses" />
  <java-symbol type="array" name="config_displayWhiteBalanceLowLightAmbientBiases" />
  <java-symbol type="dimen" name="config_displayWhiteBalanceLowLightAmbientColorTemperature" />
  <java-symbol type="array" name="config_displayWhiteBalanceHighLightAmbientBrightnesses" />
  <java-symbol type="array" name="config_displayWhiteBalanceHighLightAmbientBiases" />
  <java-symbol type="dimen" name="config_displayWhiteBalanceHighLightAmbientColorTemperature" />
  <java-symbol type="array" name="config_displayWhiteBalanceAmbientColorTemperatures" />
  <java-symbol type="array" name="config_displayWhiteBalanceDisplayColorTemperatures" />
  <java-symbol type="array" name="config_displayWhiteBalanceStrongAmbientColorTemperatures" />
  <java-symbol type="array" name="config_displayWhiteBalanceStrongDisplayColorTemperatures" />
  <java-symbol type="drawable" name="ic_action_open" />
  <java-symbol type="drawable" name="ic_menu_copy_material" />

  <!-- MIME types -->
  <java-symbol type="string" name="mime_type_folder" />
  <java-symbol type="string" name="mime_type_apk" />
  <java-symbol type="string" name="mime_type_generic" />
  <java-symbol type="string" name="mime_type_generic_ext" />
  <java-symbol type="string" name="mime_type_audio" />
  <java-symbol type="string" name="mime_type_audio_ext" />
  <java-symbol type="string" name="mime_type_video" />
  <java-symbol type="string" name="mime_type_video_ext" />
  <java-symbol type="string" name="mime_type_image" />
  <java-symbol type="string" name="mime_type_image_ext" />
  <java-symbol type="string" name="mime_type_compressed" />
  <java-symbol type="string" name="mime_type_compressed_ext" />
  <java-symbol type="string" name="mime_type_document" />
  <java-symbol type="string" name="mime_type_document_ext" />
  <java-symbol type="string" name="mime_type_spreadsheet" />
  <java-symbol type="string" name="mime_type_spreadsheet_ext" />
  <java-symbol type="string" name="mime_type_presentation" />
  <java-symbol type="string" name="mime_type_presentation_ext" />

  <!-- For Bluetooth service -->
  <java-symbol type="string" name="bluetooth_airplane_mode_toast" />

  <!-- For high refresh rate displays -->
  <java-symbol type="integer" name="config_defaultRefreshRate" />
  <java-symbol type="integer" name="config_defaultPeakRefreshRate" />
  <java-symbol type="integer" name="config_defaultRefreshRateInZone" />
  <java-symbol type="array" name="config_brightnessThresholdsOfPeakRefreshRate" />
  <java-symbol type="array" name="config_ambientThresholdsOfPeakRefreshRate" />
  <java-symbol type="integer" name="config_defaultRefreshRateInHbmSunlight" />
  <java-symbol type="integer" name="config_defaultRefreshRateInHbmHdr" />

  <!-- For fixed refresh rate displays in high brightness-->
  <java-symbol type="integer" name="config_fixedRefreshRateInHighZone" />
  <java-symbol type="array" name="config_highDisplayBrightnessThresholdsOfFixedRefreshRate" />
  <java-symbol type="array" name="config_highAmbientBrightnessThresholdsOfFixedRefreshRate" />

  <!-- For Auto-Brightness -->
  <java-symbol type="string" name="config_displayLightSensorType" />

  <java-symbol type="dimen" name="notification_min_height" />

  <java-symbol type="drawable" name="iconfactory_adaptive_icon_drawable_wrapper"/>
  <java-symbol type="attr" name="iconfactoryIconSize"/>
  <java-symbol type="attr" name="iconfactoryBadgeSize"/>
  <java-symbol type="dimen" name="resolver_icon_size"/>
  <java-symbol type="dimen" name="resolver_badge_size"/>
  <java-symbol type="dimen" name="resolver_button_bar_spacing"/>
  <java-symbol type="dimen" name="resolver_icon_margin"/>
  <java-symbol type="dimen" name="resolver_small_margin"/>
  <java-symbol type="dimen" name="resolver_edge_margin"/>
  <java-symbol type="dimen" name="resolver_elevation"/>
  <java-symbol type="dimen" name="chooser_icon_size"/>
  <java-symbol type="dimen" name="chooser_badge_size"/>

  <!-- For DropBox -->
  <java-symbol type="integer" name="config_dropboxLowPriorityBroadcastRateLimitPeriod" />
  <java-symbol type="array" name="config_dropboxLowPriorityTags" />

  <!-- For Privacy Type -->
  <java-symbol type="drawable" name="perm_group_camera" />
  <java-symbol type="drawable" name="perm_group_location" />
  <java-symbol type="drawable" name="perm_group_microphone" />

  <java-symbol type="drawable" name="chooser_direct_share_icon_placeholder" />
  <java-symbol type="color" name="chooser_gradient_background" />
  <java-symbol type="color" name="chooser_gradient_highlight" />
  <java-symbol type="drawable" name="chooser_direct_share_label_placeholder" />
  <java-symbol type="dimen" name="chooser_direct_share_label_placeholder_max_width" />
  <java-symbol type="dimen" name="seekbar_thumb_exclusion_max_size" />
  <java-symbol type="layout" name="chooser_az_label_row" />
  <java-symbol type="string" name="chooser_all_apps_button_label" />
  <java-symbol type="anim" name="resolver_launch_anim" />
  <java-symbol type="style" name="Animation.DeviceDefault.Activity.Resolver" />

  <java-symbol type="color" name="decor_view_status_guard_light" />

  <java-symbol type="string" name="config_defaultSupervisionProfileOwnerComponent" />

  <java-symbol type="drawable" name="android_logotype" />
  <java-symbol type="layout" name="platlogo_layout" />
  <java-symbol type="drawable" name="ic_number11" />

  <java-symbol type="integer" name="config_notificationWarnRemoteViewSizeBytes" />
  <java-symbol type="integer" name="config_notificationStripRemoteViewSizeBytes" />

  <java-symbol type="string" name="config_factoryResetPackage" />
  <java-symbol type="array" name="config_highRefreshRateBlacklist" />
  <java-symbol type="array" name="config_forceSlowJpegModeList" />

  <java-symbol type="array" name="config_smallAreaDetectionAllowlist" />

  <java-symbol type="layout" name="chooser_dialog" />
  <java-symbol type="layout" name="chooser_dialog_item" />
  <java-symbol type="drawable" name="chooser_dialog_background" />
  <java-symbol type="id" name="chooser_copy_button" />
  <java-symbol type="id" name="chooser_nearby_button" />
  <java-symbol type="id" name="chooser_edit_button" />
  <java-symbol type="layout" name="chooser_action_button" />
  <java-symbol type="dimen" name="chooser_action_button_icon_size" />
  <java-symbol type="string" name="config_defaultNearbySharingComponent" />
  <java-symbol type="string" name="config_defaultNearbyFastPairSettingsDevicesComponent" />
  <java-symbol type="bool" name="config_disable_all_cb_messages" />
  <java-symbol type="drawable" name="ic_close" />

  <java-symbol type="bool" name="config_hideNavBarForKeyboard" />

  <java-symbol type="bool" name="config_showBuiltinWirelessChargingAnim" />

  <!-- For bug report handler -->
  <java-symbol type="bool" name="config_bugReportHandlerEnabled" />
  <java-symbol type="string" name="config_defaultBugReportHandlerApp" />

  <!-- For profcollect report uploader -->
  <java-symbol type="bool" name="config_profcollectReportUploaderEnabled" />
  <java-symbol type="string" name="config_defaultProfcollectReportUploaderApp" />
  <java-symbol type="string" name="config_defaultProfcollectReportUploaderAction" />

  <java-symbol type="string" name="usb_device_resolve_prompt_warn" />

  <!-- For Accessibility system actions -->
  <java-symbol type="string" name="accessibility_system_action_back_label" />
  <java-symbol type="string" name="accessibility_system_action_home_label" />
  <java-symbol type="string" name="accessibility_system_action_lock_screen_label" />
  <java-symbol type="string" name="accessibility_system_action_notifications_label" />
  <java-symbol type="string" name="accessibility_system_action_power_dialog_label" />
  <java-symbol type="string" name="accessibility_system_action_quick_settings_label" />
  <java-symbol type="string" name="accessibility_system_action_recents_label" />
  <java-symbol type="string" name="accessibility_system_action_screenshot_label" />
  <java-symbol type="string" name="accessibility_system_action_headset_hook_label" />
  <java-symbol type="string" name="accessibility_system_action_on_screen_a11y_shortcut_label" />
  <java-symbol type="string" name="accessibility_system_action_on_screen_a11y_shortcut_chooser_label" />
  <java-symbol type="string" name="accessibility_system_action_hardware_a11y_shortcut_label" />
  <java-symbol type="string" name="accessibility_system_action_dismiss_notification_shade" />
  <java-symbol type="string" name="accessibility_system_action_dpad_up_label" />
  <java-symbol type="string" name="accessibility_system_action_dpad_down_label" />
  <java-symbol type="string" name="accessibility_system_action_dpad_left_label" />
  <java-symbol type="string" name="accessibility_system_action_dpad_right_label" />
  <java-symbol type="string" name="accessibility_system_action_dpad_center_label" />

  <java-symbol type="string" name="accessibility_freeform_caption" />

  <!-- For Wide Color Gamut -->
  <java-symbol type="bool" name="config_enableWcgMode" />

  <!-- For contacts provider. -->
  <java-symbol type="string" name="config_rawContactsLocalAccountName" />
  <java-symbol type="string" name="config_rawContactsLocalAccountType" />

  <!-- For App Standby -->
  <java-symbol type="string" name="as_app_forced_to_restricted_bucket" />

  <!-- For Waterfall Display -->
  <java-symbol type="dimen" name="waterfall_display_left_edge_size" />
  <java-symbol type="dimen" name="waterfall_display_top_edge_size" />
  <java-symbol type="dimen" name="waterfall_display_right_edge_size" />
  <java-symbol type="dimen" name="waterfall_display_bottom_edge_size" />

  <!-- For device policy -->
  <java-symbol type="array" name="config_packagesExemptFromSuspension" />

  <!-- Accessibility take screenshot -->
  <java-symbol type="string" name="capability_desc_canTakeScreenshot" />
  <java-symbol type="string" name="capability_title_canTakeScreenshot" />

  <java-symbol type="string" name="dream_preview_title" />

  <java-symbol type="string" name="config_servicesExtensionPackage" />

  <!-- For app process exit info tracking -->
  <java-symbol type="integer" name="config_app_exit_info_history_list_size" />

  <java-symbol type="array" name="config_defaultImperceptibleKillingExemptionPkgs" />
  <java-symbol type="array" name="config_defaultImperceptibleKillingExemptionProcStates" />

  <java-symbol type="color" name="conversation_important_highlight" />
  <java-symbol type="dimen" name="importance_ring_stroke_width" />
  <java-symbol type="dimen" name="importance_ring_anim_max_stroke_width" />
  <java-symbol type="dimen" name="importance_ring_size" />
  <java-symbol type="dimen" name="conversation_icon_size_badged" />
  <java-symbol type="dimen" name="conversation_icon_circle_start" />
  <java-symbol type="dimen" name="notification_icon_circle_start" />

  <java-symbol type="attr" name="notificationHeaderTextAppearance" />
  <java-symbol type="string" name="conversation_single_line_name_display" />
  <java-symbol type="string" name="conversation_single_line_image_placeholder" />
  <java-symbol type="string" name="conversation_title_fallback_one_to_one" />
  <java-symbol type="string" name="conversation_title_fallback_group_chat" />
  <java-symbol type="id" name="conversation_icon" />
  <java-symbol type="id" name="conversation_icon_badge" />
  <java-symbol type="id" name="conversation_icon_badge_ring" />
  <java-symbol type="id" name="conversation_icon_badge_bg" />
  <java-symbol type="id" name="expand_button_container" />
  <java-symbol type="id" name="expand_button_a11y_container" />
  <java-symbol type="id" name="expand_button_touch_container" />
  <java-symbol type="id" name="messaging_group_content_container" />
  <java-symbol type="id" name="expand_button_and_content_container" />
  <java-symbol type="id" name="conversation_header" />
  <java-symbol type="id" name="conversation_face_pile_bottom_background" />
  <java-symbol type="id" name="conversation_face_pile_bottom" />
  <java-symbol type="id" name="conversation_face_pile_top" />
  <java-symbol type="id" name="conversation_face_pile" />
  <java-symbol type="id" name="conversation_text" />
  <java-symbol type="id" name="message_icon_container" />
  <java-symbol type="id" name="conversation_image_message_container" />
  <java-symbol type="id" name="conversation_icon_container" />
  <java-symbol type="dimen" name="messaging_group_singleline_sender_padding_end" />
  <java-symbol type="dimen" name="conversation_badge_protrusion" />
  <java-symbol type="dimen" name="conversation_avatar_size" />
  <java-symbol type="dimen" name="conversation_avatar_size_group_expanded" />
  <java-symbol type="dimen" name="conversation_face_pile_avatar_size" />
  <java-symbol type="dimen" name="conversation_face_pile_avatar_size_group_expanded" />
  <java-symbol type="dimen" name="conversation_face_pile_protection_width" />
  <java-symbol type="dimen" name="conversation_face_pile_protection_width_expanded" />
  <java-symbol type="dimen" name="conversation_badge_protrusion_group_expanded" />
  <java-symbol type="dimen" name="conversation_badge_protrusion_group_expanded_face_pile" />
  <java-symbol type="dimen" name="conversation_content_start" />
  <java-symbol type="dimen" name="expanded_group_conversation_message_padding" />
  <java-symbol type="dimen" name="messaging_layout_icon_padding_start" />
  <java-symbol type="dimen" name="conversation_header_expanded_padding_end" />
  <java-symbol type="dimen" name="conversation_icon_container_top_padding" />
  <java-symbol type="dimen" name="conversation_icon_container_top_padding_small_avatar" />
  <java-symbol type="layout" name="notification_template_material_conversation" />
  <java-symbol type="dimen" name="button_padding_horizontal_material" />
  <java-symbol type="dimen" name="button_inset_horizontal_material" />
  <java-symbol type="layout" name="conversation_face_pile_layout" />
  <java-symbol type="string" name="unread_convo_overflow" />
  <java-symbol type="drawable" name="conversation_badge_background" />
  <java-symbol type="drawable" name="conversation_badge_ring" />
  <java-symbol type="color" name="conversation_important_highlight" />
  <java-symbol type="dimen" name="importance_ring_stroke_width" />
  <java-symbol type="dimen" name="importance_ring_anim_max_stroke_width" />
  <java-symbol type="dimen" name="importance_ring_size" />
  <java-symbol type="dimen" name="conversation_icon_size_badged" />

  <!-- Intent resolver and share sheet -->
  <java-symbol type="string" name="resolver_personal_tab" />
  <java-symbol type="string" name="resolver_personal_tab_accessibility" />
  <java-symbol type="string" name="resolver_work_tab" />
  <java-symbol type="string" name="resolver_work_tab_accessibility" />
  <java-symbol type="id" name="stub" />
  <java-symbol type="id" name="resolver_empty_state" />
  <java-symbol type="id" name="resolver_empty_state_icon" />
  <java-symbol type="id" name="resolver_empty_state_title" />
  <java-symbol type="id" name="resolver_empty_state_subtitle" />
  <java-symbol type="id" name="resolver_empty_state_button" />
  <java-symbol type="id" name="resolver_empty_state_progress" />
  <java-symbol type="id" name="resolver_button_bar_divider" />
  <java-symbol type="id" name="resolver_empty_state_container" />
  <java-symbol type="id" name="button_bar_container" />
  <java-symbol type="id" name="title_container" />
  <java-symbol type="string" name="resolver_cross_profile_blocked" />
  <java-symbol type="string" name="resolver_cant_share_with_work_apps_explanation" />
  <java-symbol type="string" name="resolver_cant_share_with_personal_apps_explanation" />
  <java-symbol type="string" name="resolver_cant_access_work_apps_explanation" />
  <java-symbol type="string" name="resolver_cant_access_personal_apps_explanation" />
  <java-symbol type="string" name="resolver_turn_on_work_apps" />
  <java-symbol type="string" name="resolver_no_work_apps_available" />
  <java-symbol type="string" name="resolver_no_personal_apps_available" />
  <java-symbol type="string" name="resolver_switch_on_work" />
  <java-symbol type="drawable" name="ic_screenshot_edit" />
  <java-symbol type="dimen" name="resolver_empty_state_height" />
  <java-symbol type="dimen" name="resolver_empty_state_height_with_tabs" />
  <java-symbol type="dimen" name="resolver_max_collapsed_height_with_tabs" />
  <java-symbol type="dimen" name="resolver_max_collapsed_height_with_default_with_tabs" />
  <java-symbol type="bool" name="resolver_landscape_phone" />
  <java-symbol type="dimen" name="resolver_tab_text_size" />
  <java-symbol type="dimen" name="resolver_title_padding_bottom" />
  <java-symbol type="dimen" name="resolver_empty_state_container_padding_top" />
  <java-symbol type="dimen" name="resolver_empty_state_container_padding_bottom" />
  <java-symbol type="dimen" name="resolver_profile_tab_margin" />

  <java-symbol type="string" name="config_deviceSpecificDisplayAreaPolicyProvider" />

  <!-- Whether to expand the lock screen user switcher by default -->
  <java-symbol type="bool" name="config_expandLockScreenUserSwitcher" />

  <java-symbol type="string" name="loading" />

  <java-symbol type="array" name="config_toastCrossUserPackages" />

  <java-symbol type="string" name="notification_history_title_placeholder" />

  <!-- The max scale for the wallpaper when it's zoomed in -->
  <java-symbol type="dimen" name="config_wallpaperMaxScale"/>

  <!-- Set to true to offset the wallpaper when using multiple displays so that it's centered
        at the same position than in the largest display. -->
  <java-symbol type="bool" name="config_offsetWallpaperToCenterOfLargestDisplay" />

  <!-- Set to true to enable the user switcher on the keyguard. -->
  <java-symbol type="bool" name="config_keyguardUserSwitcher" />

  <!-- If true, show multiuser switcher by default unless the user specifically disables it. -->
  <java-symbol type="bool" name="config_showUserSwitcherByDefault" />

  <!-- Set to true to make assistant show in front of the dream/screensaver. -->
  <java-symbol type="bool" name="config_assistantOnTopOfDream"/>

  <java-symbol type="string" name="config_overrideComponentUiPackage" />

  <java-symbol type="string" name="notification_channel_network_status" />
  <java-symbol type="string" name="notification_channel_network_alerts" />
  <java-symbol type="string" name="notification_channel_network_available" />

  <java-symbol type="color" name="personal_apps_suspension_notification_color" />

  <!-- For Pdn throttle feature -->
  <java-symbol type="bool" name="config_pdp_reject_enable_retry" />
  <java-symbol type="integer" name="config_pdp_reject_retry_delay_ms" />
  <java-symbol type="string" name="config_pdp_reject_dialog_title" />
  <java-symbol type="string" name="config_pdp_reject_user_authentication_failed" />
  <java-symbol type="string" name="config_pdp_reject_service_not_subscribed" />
  <java-symbol type="string" name="config_pdp_reject_multi_conn_to_same_pdn_not_allowed" />

  <java-symbol type="array" name="config_notificationMsgPkgsAllowedAsConvos" />

  <!-- To config device idle -->
  <java-symbol type="integer" name="device_idle_flex_time_short_ms" />
  <java-symbol type="integer" name="device_idle_light_after_inactive_to_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_to_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_to_init_flex_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_to_max_flex_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_factor" />
  <java-symbol type="integer" name="device_idle_light_max_idle_to_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_maintenance_min_budget_ms" />
  <java-symbol type="integer" name="device_idle_light_idle_maintenance_max_budget_ms" />
  <java-symbol type="integer" name="device_idle_min_light_maintenance_time_ms" />
  <java-symbol type="integer" name="device_idle_min_deep_maintenance_time_ms" />
  <java-symbol type="integer" name="device_idle_inactive_to_ms" />
  <java-symbol type="integer" name="device_idle_sensing_to_ms" />
  <java-symbol type="integer" name="device_idle_locating_to_ms" />
  <java-symbol type="integer" name="device_idle_location_accuracy" />
  <java-symbol type="integer" name="device_idle_motion_inactive_to_ms" />
  <java-symbol type="integer" name="device_idle_motion_inactive_to_flex_ms" />
  <java-symbol type="integer" name="device_idle_idle_after_inactive_to_ms" />
  <java-symbol type="integer" name="device_idle_idle_pending_to_ms" />
  <java-symbol type="integer" name="device_idle_max_idle_pending_to_ms" />
  <java-symbol type="integer" name="device_idle_idle_pending_factor" />
  <java-symbol type="integer" name="device_idle_quick_doze_delay_to_ms" />
  <java-symbol type="integer" name="device_idle_idle_to_ms" />
  <java-symbol type="integer" name="device_idle_max_idle_to_ms" />
  <java-symbol type="integer" name="device_idle_idle_factor" />
  <java-symbol type="integer" name="device_idle_min_time_to_alarm_ms" />
  <java-symbol type="integer" name="device_idle_max_temp_app_allowlist_duration_ms" />
  <java-symbol type="integer" name="device_idle_mms_temp_app_allowlist_duration_ms" />
  <java-symbol type="integer" name="device_idle_sms_temp_app_allowlist_duration_ms" />
  <java-symbol type="integer" name="device_idle_notification_allowlist_duration_ms" />
  <java-symbol type="bool" name="device_idle_wait_for_unlock" />
  <java-symbol type="integer" name="device_idle_pre_idle_factor_long" />
  <java-symbol type="integer" name="device_idle_pre_idle_factor_short" />
  <java-symbol type="bool" name="device_idle_use_window_alarms" />

  <!-- Binder heavy hitter watcher configs -->
  <java-symbol type="bool" name="config_defaultBinderHeavyHitterWatcherEnabled" />
  <java-symbol type="integer" name="config_defaultBinderHeavyHitterWatcherBatchSize" />
  <java-symbol type="dimen" name="config_defaultBinderHeavyHitterWatcherThreshold" />
  <java-symbol type="bool" name="config_defaultBinderHeavyHitterAutoSamplerEnabled" />
  <java-symbol type="integer" name="config_defaultBinderHeavyHitterAutoSamplerBatchSize" />
  <java-symbol type="dimen" name="config_defaultBinderHeavyHitterAutoSamplerThreshold" />

  <java-symbol type="dimen" name="default_background_blur_radius" />
  <java-symbol type="array" name="config_keep_warming_services" />
  <java-symbol type="string" name="config_display_features" />
  <java-symbol type="array" name="config_device_state_postures" />
  <java-symbol type="array" name="config_deviceTabletopRotations" />
  <java-symbol type="bool" name="config_isDisplayHingeAlwaysSeparating" />

  <java-symbol type="dimen" name="controls_thumbnail_image_max_height" />
  <java-symbol type="dimen" name="controls_thumbnail_image_max_width" />

  <java-symbol type="bool" name="config_letterboxIsDisplayRotationImmersiveAppCompatPolicyEnabled" />
  <java-symbol type="dimen" name="config_fixedOrientationLetterboxAspectRatio" />
  <java-symbol type="dimen" name="config_letterboxBackgroundWallpaperBlurRadius" />
  <java-symbol type="integer" name="config_letterboxActivityCornersRadius" />
  <java-symbol type="dimen" name="config_letterboxBackgroundWallaperDarkScrimAlpha" />
  <java-symbol type="integer" name="config_letterboxBackgroundType" />
  <java-symbol type="color" name="config_letterboxBackgroundColor" />
  <java-symbol type="dimen" name="config_letterboxHorizontalPositionMultiplier" />
  <java-symbol type="dimen" name="config_letterboxVerticalPositionMultiplier" />
  <java-symbol type="dimen" name="config_letterboxBookModePositionMultiplier" />
  <java-symbol type="dimen" name="config_letterboxTabletopModePositionMultiplier" />
  <java-symbol type="bool" name="config_letterboxIsHorizontalReachabilityEnabled" />
  <java-symbol type="bool" name="config_letterboxIsVerticalReachabilityEnabled" />
  <java-symbol type="bool" name="config_letterboxIsAutomaticReachabilityInBookModeEnabled" />
  <java-symbol type="integer" name="config_letterboxDefaultPositionForHorizontalReachability" />
  <java-symbol type="integer" name="config_letterboxDefaultPositionForVerticalReachability" />
  <java-symbol type="integer" name="config_letterboxDefaultPositionForBookModeReachability" />
  <java-symbol type="integer" name="config_letterboxDefaultPositionForTabletopModeReachability" />
  <java-symbol type="bool" name="config_letterboxIsPolicyForIgnoringRequestedOrientationEnabled" />
  <java-symbol type="bool" name="config_letterboxIsEducationEnabled" />
  <java-symbol type="dimen" name="config_letterboxDefaultMinAspectRatioForUnresizableApps" />
  <java-symbol type="bool" name="config_letterboxIsSplitScreenAspectRatioForUnresizableAppsEnabled" />
  <java-symbol type="bool" name="config_letterboxIsDisplayAspectRatioForFixedOrientationLetterboxEnabled" />
  <!-- Set to true to enable letterboxing on translucent activities. -->
  <java-symbol type="bool" name="config_letterboxIsEnabledForTranslucentActivities" />

  <!-- Whether per-app user aspect ratio override settings is enabled -->
  <java-symbol type="bool" name="config_appCompatUserAppAspectRatioSettingsIsEnabled" />
  <java-symbol type="bool" name="config_appCompatUserAppAspectRatioFullscreenIsEnabled" />

  <java-symbol type="bool" name="config_isCompatFakeFocusEnabled" />
  <java-symbol type="bool" name="config_isWindowManagerCameraCompatTreatmentEnabled" />
  <java-symbol type="bool" name="config_isWindowManagerCameraCompatSplitScreenAspectRatioEnabled" />
  <java-symbol type="bool" name="config_isCameraCompatControlForStretchedIssuesEnabled" />
  <java-symbol type="bool" name="config_skipActivityRelaunchWhenDocking" />

  <java-symbol type="bool" name="config_hideDisplayCutoutWithDisplayArea" />

  <!-- Window magnification prompt -->
  <java-symbol type="string" name="window_magnification_prompt_title" />
  <java-symbol type="string" name="window_magnification_prompt_content" />
  <java-symbol type="string" name="turn_on_magnification_settings_action" />
  <java-symbol type="string" name="dismiss_action" />

  <java-symbol type="bool" name="config_magnification_area" />
  <java-symbol type="bool" name="config_magnification_always_on_enabled" />

  <java-symbol type="bool" name="config_trackerAppNeedsPermissions"/>
  <!-- FullScreenMagnification thumbnail -->
  <java-symbol type="layout" name="thumbnail_background_view" />
  <java-symbol type="drawable" name="accessibility_magnification_thumbnail_background_bg" />
  <java-symbol type="drawable" name="accessibility_magnification_thumbnail_background_fg" />
  <java-symbol type="drawable" name="accessibility_magnification_thumbnail_bg" />
  <java-symbol type="color" name="accessibility_magnification_thumbnail_stroke_color" />
  <java-symbol type="color" name="accessibility_magnification_thumbnail_background_color" />
  <java-symbol type="color" name="accessibility_magnification_thumbnail_container_background_color" />
  <java-symbol type="color" name="accessibility_magnification_thumbnail_container_stroke_color" />
  <java-symbol type="dimen" name="accessibility_magnification_thumbnail_container_stroke_width" />
  <java-symbol type="dimen" name="accessibility_magnification_thumbnail_padding" />
  <java-symbol type="id" name="accessibility_magnification_thumbnail_view" />
  <!-- Package with global data query permissions for AppSearch -->
  <java-symbol type="string" name="config_globalAppSearchDataQuerierPackage" />

  <!-- Color used by the accessibility focus rectangle -->
  <java-symbol type="color" name="accessibility_focus_highlight_color" />
  <!-- Width of the outline stroke used by the accessibility focus rectangle -->
  <java-symbol type="dimen" name="accessibility_focus_highlight_stroke_width" />
  <java-symbol type="dimen" name="accessibility_window_magnifier_min_size" />

  <java-symbol type="bool" name="config_attachNavBarToAppDuringTransition" />

  <java-symbol type="bool" name="config_enableBackSound" />

  <java-symbol type="bool" name="config_forceOrientationListenerEnabledWhileDreaming" />

  <java-symbol type="drawable" name="ic_camera_blocked" />
  <java-symbol type="drawable" name="ic_mic_blocked" />
  <java-symbol type="drawable" name="ic_camera_allowed" />
  <java-symbol type="drawable" name="ic_mic_allowed" />
  <java-symbol type="string" name="sensor_privacy_start_use_mic_notification_content_title" />
  <java-symbol type="string" name="sensor_privacy_start_use_camera_notification_content_title" />
  <java-symbol type="string" name="sensor_privacy_start_use_notification_content_text" />
  <java-symbol type="string" name="sensor_privacy_start_use_dialog_turn_on_button" />
  <java-symbol type="string" name="sensor_privacy_notification_channel_label" />

  <java-symbol type="bool" name="config_telephony5gStandalone" />
  <java-symbol type="bool" name="config_telephony5gNonStandalone" />

  <java-symbol type="bool" name="config_voice_data_sms_auto_fallback" />

  <java-symbol type="attr" name="colorAccentPrimary" />
  <java-symbol type="attr" name="colorAccentSecondary" />
  <java-symbol type="attr" name="colorAccentTertiary" />
  <java-symbol type="attr" name="colorAccentPrimaryVariant" />
  <java-symbol type="attr" name="colorAccentSecondaryVariant" />
  <java-symbol type="attr" name="colorAccentTertiaryVariant" />
  <java-symbol type="attr" name="colorSurface" format="color" />
  <java-symbol type="attr" name="colorSurfaceHighlight" format="color" />
  <java-symbol type="attr" name="colorSurfaceVariant" format="color" />
  <java-symbol type="attr" name="colorSurfaceHeader" format="color" />
  <java-symbol type="attr" name="textColorOnAccent" format="color" />

  <!-- CEC Configuration -->
  <java-symbol type="bool" name="config_cecHdmiCecEnabled_userConfigurable" />
  <java-symbol type="bool" name="config_cecHdmiCecControlEnabled_allowed" />
  <java-symbol type="bool" name="config_cecHdmiCecControlEnabled_default" />
  <java-symbol type="bool" name="config_cecHdmiCecControlDisabled_allowed" />
  <java-symbol type="bool" name="config_cecHdmiCecControlDisabled_default" />

  <java-symbol type="bool" name="config_cecHdmiCecVersion_userConfigurable" />
  <java-symbol type="bool" name="config_cecHdmiCecVersion14b_allowed" />
  <java-symbol type="bool" name="config_cecHdmiCecVersion14b_default" />
  <java-symbol type="bool" name="config_cecHdmiCecVersion20_allowed" />
  <java-symbol type="bool" name="config_cecHdmiCecVersion20_default" />

  <java-symbol type="bool" name="config_cecRoutingControl_userConfigurable" />
  <java-symbol type="bool" name="config_cecRoutingControlEnabled_allowed" />
  <java-symbol type="bool" name="config_cecRoutingControlEnabled_default" />
  <java-symbol type="bool" name="config_cecRoutingControlDisabled_allowed" />
  <java-symbol type="bool" name="config_cecRoutingControlDisabled_default" />

  <java-symbol type="bool" name="config_cecSoundbarMode_userConfigurable" />
  <java-symbol type="bool" name="config_cecSoundbarModeEnabled_allowed" />
  <java-symbol type="bool" name="config_cecSoundbarModeEnabled_default" />
  <java-symbol type="bool" name="config_cecSoundbarModeDisabled_allowed" />
  <java-symbol type="bool" name="config_cecSoundbarModeDisabled_default" />

  <java-symbol type="bool" name="config_cecPowerControlMode_userConfigurable" />
  <java-symbol type="bool" name="config_cecPowerControlModeTv_allowed" />
  <java-symbol type="bool" name="config_cecPowerControlModeTv_default" />
  <java-symbol type="bool" name="config_cecPowerControlModeTvAndAudioSystem_allowed" />
  <java-symbol type="bool" name="config_cecPowerControlModeTvAndAudioSystem_default" />
  <java-symbol type="bool" name="config_cecPowerControlModeBroadcast_allowed" />
  <java-symbol type="bool" name="config_cecPowerControlModeBroadcast_default" />
  <java-symbol type="bool" name="config_cecPowerControlModeNone_allowed" />
  <java-symbol type="bool" name="config_cecPowerControlModeNone_default" />

  <java-symbol type="bool" name="config_cecPowerStateChangeOnActiveSourceLost_userConfigurable" />
  <java-symbol type="bool" name="config_cecPowerStateChangeOnActiveSourceLostNone_allowed" />
  <java-symbol type="bool" name="config_cecPowerStateChangeOnActiveSourceLostNone_default" />
  <java-symbol type="bool" name="config_cecPowerStateChangeOnActiveSourceLostStandbyNow_allowed" />
  <java-symbol type="bool" name="config_cecPowerStateChangeOnActiveSourceLostStandbyNow_default" />

  <java-symbol type="bool" name="config_cecSystemAudioControl_userConfigurable" />
  <java-symbol type="bool" name="config_cecSystemAudioControlEnabled_allowed" />
  <java-symbol type="bool" name="config_cecSystemAudioControlEnabled_default" />
  <java-symbol type="bool" name="config_cecSystemAudioControlDisabled_allowed" />
  <java-symbol type="bool" name="config_cecSystemAudioControlDisabled_default" />

  <java-symbol type="bool" name="config_cecSystemAudioModeMuting_userConfigurable" />
  <java-symbol type="bool" name="config_cecSystemAudioModeMutingEnabled_allowed" />
  <java-symbol type="bool" name="config_cecSystemAudioModeMutingEnabled_default" />
  <java-symbol type="bool" name="config_cecSystemAudioModeMutingDisabled_allowed" />
  <java-symbol type="bool" name="config_cecSystemAudioModeMutingDisabled_default" />

  <java-symbol type="bool" name="config_cecVolumeControlMode_userConfigurable" />
  <java-symbol type="bool" name="config_cecVolumeControlModeEnabled_allowed" />
  <java-symbol type="bool" name="config_cecVolumeControlModeEnabled_default" />
  <java-symbol type="bool" name="config_cecVolumeControlModeDisabled_allowed" />
  <java-symbol type="bool" name="config_cecVolumeControlModeDisabled_default" />

  <java-symbol type="bool" name="config_cecTvWakeOnOneTouchPlay_userConfigurable" />
  <java-symbol type="bool" name="config_cecTvWakeOnOneTouchPlayEnabled_allowed" />
  <java-symbol type="bool" name="config_cecTvWakeOnOneTouchPlayEnabled_default" />
  <java-symbol type="bool" name="config_cecTvWakeOnOneTouchPlayDisabled_allowed" />
  <java-symbol type="bool" name="config_cecTvWakeOnOneTouchPlayDisabled_default" />

  <java-symbol type="bool" name="config_cecTvSendStandbyOnSleep_userConfigurable" />
  <java-symbol type="bool" name="config_cecTvSendStandbyOnSleepEnabled_allowed" />
  <java-symbol type="bool" name="config_cecTvSendStandbyOnSleepEnabled_default" />
  <java-symbol type="bool" name="config_cecTvSendStandbyOnSleepDisabled_allowed" />
  <java-symbol type="bool" name="config_cecTvSendStandbyOnSleepDisabled_default" />

  <java-symbol type="bool" name="config_cecSetMenuLanguage_userConfigurable" />
  <java-symbol type="bool" name="config_cecSetMenuLanguageEnabled_allowed" />
  <java-symbol type="bool" name="config_cecSetMenuLanguageEnabled_default" />
  <java-symbol type="bool" name="config_cecSetMenuLanguageDisabled_allowed" />
  <java-symbol type="bool" name="config_cecSetMenuLanguageDisabled_default" />

  <java-symbol type="bool" name="config_cecRcProfileTv_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileTvNone_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileTvNone_default" />
  <java-symbol type="bool" name="config_cecRcProfileTvOne_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileTvOne_default" />
  <java-symbol type="bool" name="config_cecRcProfileTvTwo_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileTvTwo_default" />
  <java-symbol type="bool" name="config_cecRcProfileTvThree_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileTvThree_default" />
  <java-symbol type="bool" name="config_cecRcProfileTvFour_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileTvFour_default" />

  <java-symbol type="bool" name="config_cecRcProfileSourceRootMenu_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileSourceRootMenuHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceRootMenuHandled_default" />
  <java-symbol type="bool" name="config_cecRcProfileSourceRootMenuNotHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceRootMenuNotHandled_default" />

  <java-symbol type="bool" name="config_cecRcProfileSourceSetupMenu_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileSourceSetupMenuHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceSetupMenuHandled_default" />
  <java-symbol type="bool" name="config_cecRcProfileSourceSetupMenuNotHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceSetupMenuNotHandled_default" />

  <java-symbol type="bool" name="config_cecRcProfileSourceContentsMenu_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileSourceContentsMenuHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceContentsMenuHandled_default" />
  <java-symbol type="bool" name="config_cecRcProfileSourceContentsMenuNotHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceContentsMenuNotHandled_default" />

  <java-symbol type="bool" name="config_cecRcProfileSourceTopMenu_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileSourceTopMenuHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceTopMenuHandled_default" />
  <java-symbol type="bool" name="config_cecRcProfileSourceTopMenuNotHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceTopMenuNotHandled_default" />

  <java-symbol type="bool" name="config_cecRcProfileSourceMediaContextSensitiveMenu_userConfigurable" />
  <java-symbol type="bool" name="config_cecRcProfileSourceMediaContextSensitiveMenuHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceMediaContextSensitiveMenuHandled_default" />
  <java-symbol type="bool" name="config_cecRcProfileSourceMediaContextSensitiveMenuNotHandled_allowed" />
  <java-symbol type="bool" name="config_cecRcProfileSourceMediaContextSensitiveMenuNotHandled_default" />

  <!-- Which Short Audio Descriptors a TV should query via CEC -->
  <java-symbol type="bool" name="config_cecQuerySadLpcm_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadLpcmEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadLpcmEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadLpcmDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadLpcmDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadDd_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadDdEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDdEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadDdDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDdDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadMpeg1_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg1Enabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg1Enabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg1Disabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg1Disabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadMp3_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadMp3Enabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMp3Enabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadMp3Disabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMp3Disabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadMpeg2_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg2Enabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg2Enabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg2Disabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMpeg2Disabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadAac_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadAacEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadAacEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadAacDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadAacDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadDts_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadDtsEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDtsEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadDtsDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDtsDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadAtrac_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadAtracEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadAtracEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadAtracDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadAtracDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadOnebitaudio_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadOnebitaudioEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadOnebitaudioEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadOnebitaudioDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadOnebitaudioDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadDdp_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadDdpEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDdpEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadDdpDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDdpDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadDtshd_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadDtshdEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDtshdEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadDtshdDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDtshdDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadTruehd_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadTruehdEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadTruehdEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadTruehdDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadTruehdDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadDst_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadDstEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDstEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadDstDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadDstDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadWmapro_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadWmaproEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadWmaproEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadWmaproDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadWmaproDisabled_default" />

  <java-symbol type="bool" name="config_cecQuerySadMax_userConfigurable" />
  <java-symbol type="bool" name="config_cecQuerySadMaxEnabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMaxEnabled_default" />
  <java-symbol type="bool" name="config_cecQuerySadMaxDisabled_allowed" />
  <java-symbol type="bool" name="config_cecQuerySadMaxDisabled_default" />

  <!-- eARC Configuration -->
  <java-symbol type="bool" name="config_earcEnabled_userConfigurable" />
  <java-symbol type="bool" name="config_earcFeatureEnabled_allowed" />
  <java-symbol type="bool" name="config_earcFeatureEnabled_default" />
  <java-symbol type="bool" name="config_earcFeatureDisabled_allowed" />
  <java-symbol type="bool" name="config_earcFeatureDisabled_default" />

  <!-- Ids for RemoteViews -->
  <java-symbol type="id" name="remote_views_next_child" />
  <java-symbol type="id" name="remote_views_stable_id" />
  <java-symbol type="id" name="remote_views_override_id" />

  <!-- View and control prompt -->
  <java-symbol type="drawable" name="ic_accessibility_24dp" />
  <java-symbol type="string" name="view_and_control_notification_title" />
  <java-symbol type="string" name="view_and_control_notification_content" />
  <!-- Translation -->
  <java-symbol type="string" name="ui_translation_accessibility_translated_text" />
  <java-symbol type="string" name="ui_translation_accessibility_translation_finished" />

  <java-symbol type="layout" name="notification_expand_button"/>

  <java-symbol type="bool" name="config_supportsMicToggle" />
  <java-symbol type="bool" name="config_supportsCamToggle" />
  <java-symbol type="bool" name="config_supportsHardwareMicToggle" />
  <java-symbol type="bool" name="config_supportsHardwareCamToggle" />
  <java-symbol type="bool" name="config_launchCameraOnCameraLensCoverToggle" />
  <java-symbol type="bool" name="config_sensorPrivacyRequiresAuthentication" />

  <java-symbol type="dimen" name="starting_surface_icon_size" />
  <java-symbol type="dimen" name="starting_surface_default_icon_size" />

  <java-symbol type="bool" name="config_assistLongPressHomeEnabledDefault" />
  <java-symbol type="bool" name="config_assistTouchGestureEnabledDefault" />

  <java-symbol type="bool" name="config_searchPressHoldNavHandleEnabledDefault" />
  <java-symbol type="bool" name="config_searchLongPressHomeEnabledDefault" />

  <java-symbol type="integer" name="config_hotwordDetectedResultMaxBundleSize" />

  <java-symbol type="dimen" name="config_wallpaperDimAmount" />

  <java-symbol type="bool" name="config_volumeAdjustmentForRemoteGroupSessions" />

  <java-symbol type="integer" name="config_mediaOutputSwitchDialogVersion" />

  <!-- List of shared library packages that should be loaded by the classloader after the
       code and resources provided by applications. -->
  <java-symbol type="array" name="config_sharedLibrariesLoadedAfterApp" />

  <java-symbol type="integer" name="config_customizedMaxCachedProcesses" />

  <java-symbol type="color" name="overview_background"/>

  <java-symbol type="bool" name="config_disableTaskSnapshots" />

  <java-symbol type="string" name="config_secondaryBuiltInDisplayCutout" />
  <java-symbol type="string" name="config_secondaryBuiltInDisplayCutoutRectApproximation" />
  <java-symbol type="bool" name="config_fillSecondaryBuiltInDisplayCutout" />
  <java-symbol type="bool" name="config_maskSecondaryBuiltInDisplayCutout" />
  <java-symbol type="array" name="config_displayUniqueIdArray" />
  <java-symbol type="array" name="config_displayCutoutPathArray" />
  <java-symbol type="array" name="config_displayCutoutApproximationRectArray" />
  <java-symbol type="array" name="config_fillBuiltInDisplayCutoutArray" />
  <java-symbol type="array" name="config_maskBuiltInDisplayCutoutArray" />
  <java-symbol type="dimen" name="secondary_waterfall_display_left_edge_size" />
  <java-symbol type="dimen" name="secondary_waterfall_display_top_edge_size" />
  <java-symbol type="dimen" name="secondary_waterfall_display_right_edge_size" />
  <java-symbol type="dimen" name="secondary_waterfall_display_bottom_edge_size" />
  <java-symbol type="array" name="config_mainBuiltInDisplayWaterfallCutout" />
  <java-symbol type="array" name="config_secondaryBuiltInDisplayWaterfallCutout" />
  <java-symbol type="array" name="config_waterfallCutoutArray" />

  <java-symbol type="fraction" name="global_actions_vertical_padding_percentage" />
  <java-symbol type="fraction" name="global_actions_horizontal_padding_percentage" />
  <java-symbol type="drawable" name="global_actions_item_red_background" />

  <java-symbol type="string" name="config_wearSysUiPackage"/>
  <java-symbol type="string" name="config_wearSysUiMainActivity"/>
  <java-symbol type="string" name="config_wearMediaControlsPackage"/>
  <java-symbol type="string" name="config_wearMediaSessionsPackage"/>
  <java-symbol type="string" name="config_defaultQrCodeComponent"/>

  <java-symbol type="dimen" name="secondary_rounded_corner_radius" />
  <java-symbol type="dimen" name="secondary_rounded_corner_radius_top" />
  <java-symbol type="dimen" name="secondary_rounded_corner_radius_bottom" />
  <java-symbol type="dimen" name="secondary_rounded_corner_radius_adjustment" />
  <java-symbol type="dimen" name="secondary_rounded_corner_radius_top_adjustment" />
  <java-symbol type="dimen" name="secondary_rounded_corner_radius_bottom_adjustment" />
  <java-symbol type="array" name="config_roundedCornerRadiusArray" />
  <java-symbol type="array" name="config_roundedCornerTopRadiusArray" />
  <java-symbol type="array" name="config_roundedCornerBottomRadiusArray" />
  <java-symbol type="array" name="config_roundedCornerRadiusAdjustmentArray" />
  <java-symbol type="array" name="config_roundedCornerTopRadiusAdjustmentArray" />
  <java-symbol type="array" name="config_roundedCornerBottomRadiusAdjustmentArray" />
  <java-symbol type="bool" name="config_secondaryBuiltInDisplayIsRound" />
  <java-symbol type="array" name="config_builtInDisplayIsRoundArray" />
  <java-symbol type="array" name="config_gnssParameters" />

  <java-symbol type="integer" name="config_mashPressVibrateTimeOnPowerButton" />

  <java-symbol type="string" name="config_systemGameService" />

  <java-symbol type="string" name="config_supervisedUserCreationPackage"/>

  <java-symbol type="bool" name="config_enableStylusPointerIcon" />

  <java-symbol type="bool" name="config_enableSafetyCenter" />

  <java-symbol type="bool" name="config_safetyProtectionEnabled" />

  <java-symbol type="bool" name="config_repairModeSupported" />

  <java-symbol type="bool" name="config_variableRefreshRateTypingSupported" />

  <java-symbol type="string" name="config_devicePolicyManagementUpdater" />

  <java-symbol type="string" name="config_deviceSpecificDeviceStatePolicyProvider" />

  <java-symbol type="array" name="config_dockExtconStateMapping" />

  <java-symbol type="string" name="notification_channel_abusive_bg_apps"/>
  <java-symbol type="string" name="notification_title_abusive_bg_apps"/>
  <java-symbol type="string" name="notification_title_long_running_fgs"/>
  <java-symbol type="string" name="notification_content_abusive_bg_apps"/>
  <java-symbol type="string" name="notification_content_long_running_fgs"/>
  <java-symbol type="string" name="notification_action_check_bg_apps"/>

  <java-symbol type="bool" name="config_lowPowerStandbySupported" />
  <java-symbol type="bool" name="config_lowPowerStandbyEnabledByDefault" />
  <java-symbol type="integer" name="config_lowPowerStandbyNonInteractiveTimeout" />

  <!-- For VirtualDeviceManager -->
  <java-symbol type="string" name="vdm_camera_access_denied" />
  <java-symbol type="string" name="vdm_secure_window" />

  <java-symbol type="integer" name="config_cameraPrivacyLightAlsAveragingIntervalMillis"/>
  <java-symbol type="array" name="config_cameraPrivacyLightAlsLuxThresholds"/>
  <java-symbol type="array" name="config_cameraPrivacyLightColors"/>

  <java-symbol type="bool" name="config_bg_current_drain_monitor_enabled" />
  <java-symbol type="array" name="config_bg_current_drain_threshold_to_restricted_bucket" />
  <java-symbol type="array" name="config_bg_current_drain_threshold_to_bg_restricted" />
  <java-symbol type="integer" name="config_bg_current_drain_window" />
  <java-symbol type="integer" name="config_bg_current_drain_types_to_restricted_bucket" />
  <java-symbol type="integer" name="config_bg_current_drain_types_to_bg_restricted" />
  <java-symbol type="integer" name="config_bg_current_drain_power_components" />
  <java-symbol type="bool" name="config_bg_current_drain_event_duration_based_threshold_enabled" />
  <java-symbol type="array" name="config_bg_current_drain_high_threshold_to_restricted_bucket" />
  <java-symbol type="array" name="config_bg_current_drain_high_threshold_to_bg_restricted" />
  <java-symbol type="integer" name="config_bg_current_drain_media_playback_min_duration" />
  <java-symbol type="integer" name="config_bg_current_drain_location_min_duration" />
  <java-symbol type="bool" name="config_bg_current_drain_auto_restrict_abusive_apps" />
  <java-symbol type="bool" name="config_bg_prompt_fgs_with_noti_to_bg_restricted" />
  <java-symbol type="bool" name="config_bg_prompt_abusive_apps_to_bg_restricted" />
  <java-symbol type="integer" name="config_bg_current_drain_exempted_types" />
  <java-symbol type="bool" name="config_bg_current_drain_high_threshold_by_bg_location" />
  <java-symbol type="drawable" name="ic_swap_horiz" />
  <java-symbol type="array" name="config_deviceStatesAvailableForAppRequests" />
  <java-symbol type="array" name="config_serviceStateLocationAllowedPackages" />
  <java-symbol type="integer" name="config_deviceStateRearDisplay"/>
  <java-symbol type="array" name="device_state_notification_state_identifiers"/>
  <java-symbol type="array" name="device_state_notification_names"/>
  <java-symbol type="array" name="device_state_notification_active_titles"/>
  <java-symbol type="array" name="device_state_notification_active_contents"/>
  <java-symbol type="array" name="device_state_notification_thermal_titles"/>
  <java-symbol type="array" name="device_state_notification_thermal_contents"/>
  <java-symbol type="array" name="device_state_notification_power_save_titles"/>
  <java-symbol type="array" name="device_state_notification_power_save_contents"/>
  <java-symbol type="string" name="concurrent_display_notification_name"/>
  <java-symbol type="string" name="concurrent_display_notification_active_title"/>
  <java-symbol type="string" name="concurrent_display_notification_active_content"/>
  <java-symbol type="string" name="concurrent_display_notification_thermal_title"/>
  <java-symbol type="string" name="concurrent_display_notification_thermal_content"/>
  <java-symbol type="string" name="concurrent_display_notification_power_save_title"/>
  <java-symbol type="string" name="concurrent_display_notification_power_save_content"/>
  <java-symbol type="string" name="device_state_notification_turn_off_button"/>
  <java-symbol type="string" name="device_state_notification_settings_button"/>
  <java-symbol type="integer" name="config_deviceStateConcurrentRearDisplay" />
  <java-symbol type="string" name="config_rearDisplayPhysicalAddress" />

  <!-- For app language picker -->
  <java-symbol type="string" name="system_locale_title" />
  <java-symbol type="layout" name="app_language_picker_system_default" />
  <java-symbol type="layout" name="app_language_picker_system_current" />
  <java-symbol type="layout" name="app_language_picker_current_locale_item" />
  <java-symbol type="id" name="system_locale_subtitle" />
  <java-symbol type="id" name="language_picker_item" />
  <java-symbol type="id" name="language_picker_header" />

  <java-symbol type="dimen" name="status_bar_height_default" />

  <java-symbol type="string" name="default_card_name"/>

  <java-symbol type="string" name="config_mainDisplayShape"/>
  <java-symbol type="string" name="config_secondaryDisplayShape"/>
  <java-symbol type="array" name="config_displayShapeArray" />

  <java-symbol type="bool" name="config_stopSystemPackagesByDefault"/>
  <java-symbol type="string" name="config_wearServiceComponent" />

  <java-symbol type="string" name="config_sharedConnectivityServicePackage" />
  <java-symbol type="string" name="config_sharedConnectivityServiceIntentAction" />
  <java-symbol type="bool" name="config_hotspotNetworksEnabledForService"/>
  <java-symbol type="bool" name="config_knownNetworksEnabledForService"/>

  <!-- Whether to show weather on the lockscreen by default. -->
  <java-symbol type="bool" name="config_lockscreenWeatherEnabledByDefault" />

  <!-- For keyboard notification -->
  <java-symbol type="string" name="keyboard_layout_notification_selected_title"/>
  <java-symbol type="string" name="keyboard_layout_notification_one_selected_message"/>
  <java-symbol type="string" name="keyboard_layout_notification_two_selected_message"/>
  <java-symbol type="string" name="keyboard_layout_notification_three_selected_message"/>
  <java-symbol type="string" name="keyboard_layout_notification_more_than_three_selected_message"/>
  <java-symbol type="string" name="keyboard_layout_notification_multiple_selected_title"/>
  <java-symbol type="string" name="keyboard_layout_notification_multiple_selected_message"/>

  <java-symbol type="bool" name="config_batteryStatsResetOnUnplugHighBatteryLevel" />
  <java-symbol type="bool" name="config_batteryStatsResetOnUnplugAfterSignificantCharge" />


  <java-symbol name="materialColorOnSecondaryFixedVariant" type="attr"/>
  <java-symbol name="materialColorOnTertiaryFixedVariant" type="attr"/>
  <java-symbol name="materialColorSurfaceContainerLowest" type="attr"/>
  <java-symbol name="materialColorOnPrimaryFixedVariant" type="attr"/>
  <java-symbol name="materialColorOnSecondaryContainer" type="attr"/>
  <java-symbol name="materialColorOnTertiaryContainer" type="attr"/>
  <java-symbol name="materialColorSurfaceContainerLow" type="attr"/>
  <java-symbol name="materialColorOnPrimaryContainer" type="attr"/>
  <java-symbol name="materialColorSecondaryFixedDim" type="attr"/>
  <java-symbol name="materialColorOnErrorContainer" type="attr"/>
  <java-symbol name="materialColorOnSecondaryFixed" type="attr"/>
  <java-symbol name="materialColorOnSurfaceInverse" type="attr"/>
  <java-symbol name="materialColorTertiaryFixedDim" type="attr"/>
  <java-symbol name="materialColorOnTertiaryFixed" type="attr"/>
  <java-symbol name="materialColorPrimaryFixedDim" type="attr"/>
  <java-symbol name="materialColorSecondaryContainer" type="attr"/>
  <java-symbol name="materialColorErrorContainer" type="attr"/>
  <java-symbol name="materialColorOnPrimaryFixed" type="attr"/>
  <java-symbol name="materialColorPrimaryInverse" type="attr"/>
  <java-symbol name="materialColorSecondaryFixed" type="attr"/>
  <java-symbol name="materialColorSurfaceInverse" type="attr"/>
  <java-symbol name="materialColorSurfaceVariant" type="attr"/>
  <java-symbol name="materialColorTertiaryContainer" type="attr"/>
  <java-symbol name="materialColorTertiaryFixed" type="attr"/>
  <java-symbol name="materialColorPrimaryContainer" type="attr"/>
  <java-symbol name="materialColorOnBackground" type="attr"/>
  <java-symbol name="materialColorPrimaryFixed" type="attr"/>
  <java-symbol name="materialColorOnSecondary" type="attr"/>
  <java-symbol name="materialColorOnTertiary" type="attr"/>
  <java-symbol name="materialColorSurfaceDim" type="attr"/>
  <java-symbol name="materialColorSurfaceBright" type="attr"/>
  <java-symbol name="materialColorOnError" type="attr"/>
  <java-symbol name="materialColorSurface" type="attr"/>
  <java-symbol name="materialColorSurfaceContainerHigh" type="attr"/>
  <java-symbol name="materialColorSurfaceContainerHighest" type="attr"/>
  <java-symbol name="materialColorOnSurfaceVariant" type="attr"/>
  <java-symbol name="materialColorOutline" type="attr"/>
  <java-symbol name="materialColorOutlineVariant" type="attr"/>
  <java-symbol name="materialColorOnPrimary" type="attr"/>
  <java-symbol name="materialColorOnSurface" type="attr"/>
  <java-symbol name="materialColorSurfaceContainer" type="attr"/>
  <java-symbol name="materialColorPrimary" type="attr"/>
  <java-symbol name="materialColorSecondary" type="attr"/>
  <java-symbol name="materialColorTertiary" type="attr"/>

  <java-symbol type="attr" name="actionModeUndoDrawable" />
  <java-symbol type="attr" name="actionModeRedoDrawable" />

  <!-- Remaining symbols for Themes -->
  <java-symbol type="style" name="Theme.DeviceDefault.Autofill.Save" />
  <java-symbol type="style" name="Theme.DeviceDefault.AutofillHalfScreenDialogButton" />
  <java-symbol type="style" name="Theme.DeviceDefault.AutofillHalfScreenDialogList" />
  <java-symbol type="style" name="Theme.DeviceDefault.DayNight" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.Alert.DayNight" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.FixedSize" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.MinWidth" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.NoActionBar.FixedSize" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.NoActionBar.MinWidth" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog.Presentation" />
  <java-symbol type="style" name="Theme.DeviceDefault.Dialog" />
  <java-symbol type="style" name="Theme.DeviceDefault.DialogWhenLarge.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.DialogWhenLarge" />
  <java-symbol type="style" name="Theme.DeviceDefault.DocumentsUI" />
  <java-symbol type="style" name="Theme.DeviceDefault.InputMethod" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.DarkActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.FixedSize" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.MinWidth" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.NoActionBar.FixedSize" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.NoActionBar.MinWidth" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog.Presentation" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Dialog" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.DialogWhenLarge.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.DialogWhenLarge" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.NoActionBar.Fullscreen" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.NoActionBar.Overscan" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.NoActionBar.TranslucentDecor" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Panel" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.SearchBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light.Voice" />
  <java-symbol type="style" name="Theme.DeviceDefault.Light" />
  <java-symbol type="style" name="Theme.DeviceDefault.NoActionBar.Fullscreen" />
  <java-symbol type="style" name="Theme.DeviceDefault.NoActionBar.Overscan" />
  <java-symbol type="style" name="Theme.DeviceDefault.NoActionBar.TranslucentDecor" />
  <java-symbol type="style" name="Theme.DeviceDefault.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Notification" />
  <java-symbol type="style" name="Theme.DeviceDefault.Panel" />
  <java-symbol type="style" name="Theme.DeviceDefault.ResolverCommon" />
  <java-symbol type="style" name="Theme.DeviceDefault.SearchBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.Dark.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.Dialog.Alert" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.Dialog.NoActionBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.Dialog" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.DialogBase" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings.DialogWhenLarge" />
  <java-symbol type="style" name="Theme.DeviceDefault.Settings" />
  <java-symbol type="style" name="Theme.DeviceDefault.System.Dialog.Alert" />
  <java-symbol type="style" name="Theme.DeviceDefault.System.Dialog" />
  <java-symbol type="style" name="Theme.DeviceDefault.SystemUI.Dialog" />
  <java-symbol type="style" name="Theme.DeviceDefault.Wallpaper.NoTitleBar" />
  <java-symbol type="style" name="Theme.DeviceDefault.Wallpaper" />
  <java-symbol type="style" name="Theme.DeviceDefault" />
  <java-symbol type="style" name="Theme.DeviceDefaultBase" />
  <java-symbol type="style" name="ThemeOverlay.DeviceDefault.Accent" />
  <java-symbol type="style" name="ThemeOverlay.DeviceDefault.Accent.Light" />
  <java-symbol type="style" name="ThemeOverlay.DeviceDefault.Dark.ActionBar.Accent" />

  <java-symbol type="drawable" name="focus_event_pressed_key_background" />
  <java-symbol type="string" name="lockscreen_too_many_failed_attempts_countdown" />

  <!-- Whether we order unlocking and waking -->
  <java-symbol type="bool" name="config_orderUnlockAndWake" />

  <!-- ======rk code====== -->
  <java-symbol type="bool" name="config_rk_ebook" />
  <!-- =================== -->
  

  <!-- switch channel dialog. -->
     <java-symbol type="color" name="translucent_background" />
     <java-symbol type="drawable" name="custom_switch_shape" />
     <java-symbol type="drawable" name="switch_projector" />
     <java-symbol type="drawable" name="switch_hdmi" />
     <java-symbol type="drawable" name="switch_wireless" />
     <java-symbol type="layout" name="custom_switch_layout" />
      <java-symbol type="style" name="customSwitchDialog" />
      <java-symbol type="string" name="switch_projector" />
      <java-symbol type="string" name="switch_projector2" />
      <java-symbol type="string" name="switch_hdmi" />
      <java-symbol type="string" name="switch_wireless" />
      <java-symbol type="id" name="layout_projector" />
      <java-symbol type="id" name="layout_hdmi" />
      <java-symbol type="id" name="layout_wireless" />
 
      <!-- countdown  dialog. -->

     <java-symbol type="string" name="toast_block_power_by_byom" /> 
     <java-symbol type="drawable" name="custom_cancel_shape" />
     <java-symbol type="drawable" name="custom_confirm_shape" />
     <java-symbol type="layout" name="count_down_layout" />
     <java-symbol type="string" name="device_will_sleep" />
     <java-symbol type="id" name="progress_bar" />

      <java-symbol type="id" name="bt_confirm_temp" />
      <java-symbol type="id" name="count_down_tv" />
      <java-symbol type="layout" name="temp_high_layout" />
      <java-symbol type="string" name="temp_high_title" />
      <java-symbol type="string" name="temp_high_ok" />
      <java-symbol type="string" name="count_down_poweroff" />
      <java-symbol type="string" name="temp_high_text" />
       <java-symbol type="layout" name="touch_pad_offline_layout" />
       <java-symbol type="id" name="touch_pad_iv" />
      <java-symbol type="id" name="touch_pad_tv" />
      <java-symbol type="string" name="touch_pad_offline_tv" />
      <java-symbol type="string" name="touch_pad_lowpower_tv" />
      <java-symbol type="drawable" name="ic_touch_pad_offline" />
      <java-symbol type="drawable" name="ic_touch_pad_power" /> 

      <java-symbol type="id" name="layout_reboot" />
      <java-symbol type="id" name="layout_power_off" />

      <java-symbol type="layout" name="long_press_power_layout" />
      <java-symbol type="layout" name="power_off_layout" />
      <java-symbol type="layout" name="power_off_small_round_screen_layout" />
      <java-symbol type="string" name="custom_reboot_title" />
      <java-symbol type="string" name="custom_power_off_title" />
      <java-symbol type="string" name="custom_power_off_now_title" />
      <java-symbol type="style" name="Widget.Custom.ProgressBar" />
      <java-symbol type="drawable" name="ic_reboot" />
      <java-symbol type="drawable" name="ic_power_off" />

       <java-symbol type="id" name="show_tv" />
       <java-symbol type="id" name="progressbar_project" />
       <java-symbol type="id" name="progressbar_hdmi" />
       <java-symbol type="id" name="progressbar_wireless" />
       <java-symbol type="drawable" name="ic_switch" />
        <java-symbol type="drawable" name="switch_progressbar_bg" />
       <java-symbol type="string" name="show_touch_icon_left" />
       <java-symbol type="string" name="show_touch_icon_right" />
       <java-symbol type="drawable" name="custom_switch_choice_shape" />

       <java-symbol type="layout" name="wake_up_logo_layout" />
       <java-symbol type="drawable" name="wake_up_logo" /> 
       <java-symbol type="drawable" name="wake_up_logo_a" />
       <java-symbol type="drawable" name="wake_up_logo_overseas" />       
        <java-symbol type="id" name="im_logo" />
       <java-symbol type="string" name="czur_sleep_text" /> 
        <java-symbol type="color" name="czur_sleep_text_color" />
        <java-symbol type="color" name="czur_sleep_text_color_cpp" />
        <java-symbol type="id" name="count_down_tv2" />
        <java-symbol type="id" name="count_down_layout" />
        <java-symbol type="layout" name="count_down_layout2" />
        <java-symbol type="drawable" name="czur_toast_shape" /> 
        <java-symbol type="string" name="disable_voicecall" />
        <java-symbol type="id" name="count_down_iv" />
        <java-symbol type="string" name="kill_app_toast" />

        <java-symbol type="layout" name="low_memory_layout" />
        <java-symbol type="string" name="clean" />
        <java-symbol type="string" name="low_memory_tv" /> 
        <java-symbol type="string" name="low_memory_warning" />
        <java-symbol type="id" name="bt_confirm_clean" />
        <java-symbol type="id" name="bt_cancel_clean" />
        <java-symbol type="id" name="low_memory_tv" />
 
        <java-symbol type="layout" name="long_power_key" />
        <java-symbol type="drawable" name="ic_reboot_center" />
        <java-symbol type="drawable" name="ic_power_off_center" />
        <java-symbol type="string" name="str_timeout_poweroff" />
        <java-symbol type="id" name="power_off_iv" />
        <java-symbol type="id" name="power_off_center_iv" />
        <java-symbol type="id" name="reboot_iv" />
        <java-symbol type="id" name="reboot_center_iv" />
        <java-symbol type="id" name="count_down_tv" />
        <java-symbol type="id" name="rlayout_dark" />
        <java-symbol type="id" name="power_progress_bar" />
        <java-symbol type="id" name="power_off_bg" />
        <java-symbol type="id" name="reboot_bg" />
        <java-symbol type="drawable" name="custom_long_power_background" />
        <java-symbol type="drawable" name="ic_shutdown_logo" />

        <java-symbol type="id" name="recommend" />
        <java-symbol type="string" name="str_resolver_recommend" />

        <java-symbol type="xml" name="custom_icon_mapping" />
        <java-symbol type="drawable" name="com_tencent_wemeet_app" /> 
        <java-symbol type="drawable" name="com_tencent_qqmusic" /> 
        <java-symbol type="drawable" name="cn_com_peoplecity" /> 
        <java-symbol type="drawable" name="cn_wps_moffice_eng" /> 
        <java-symbol type="drawable" name="com_alibaba_android_rimet" /> 
        <java-symbol type="drawable" name="com_android_chrome" /> 
        <java-symbol type="drawable" name="com_ss_android_lark" /> 
        <java-symbol type="drawable" name="com_ss_meetx_room" />
        <java-symbol type="drawable" name="com_tencent_qqlive" /> 
        <java-symbol type="drawable" name="us_zoom_videomeetings" /> 
        <java-symbol type="drawable" name="cn_cntvnews" /> 
        <java-symbol type="drawable" name="net_xinhuamm_mainclient" /> 

        <java-symbol type="drawable" name="cn_wps_yun" /> 
        <java-symbol type="drawable" name="com_tencent_mtt" /> 
        <java-symbol type="drawable" name="com_tencent_news" /> 
        <java-symbol type="drawable" name="com_huawei_cloudlink" /> 
        <java-symbol type="drawable" name="com_fenbi_android_zenglish" /> 
        <java-symbol type="drawable" name="com_ainemo_dragoon" /> 
        <java-symbol type="drawable" name="cn_dictcn_android_digitize_swg_xhzd_21003" /> 
        <java-symbol type="drawable" name="com_xiaoe_client" /> 
        <java-symbol type="drawable" name="com_youku_phone" /> 
        <java-symbol type="drawable" name="com_qiyi_video" /> 
        <java-symbol type="drawable" name="com_tencent_edu" /> 
        <java-symbol type="drawable" name="com_cmcc_android_ysx" /> 
        <java-symbol type="drawable" name="tv_danmaku_bili" /> 
        <java-symbol type="drawable" name="com_microsoft_teams" /> 
        <java-symbol type="drawable" name="com_ktcp_video" /> 
        <java-symbol type="drawable" name="com_inpor_fastmeetingcloud" />
        <java-symbol type="drawable" name="com_cisco_webex_meetings" />
        <java-symbol type="drawable" name="com_google_android_apps_meetings" />
        <java-symbol type="drawable" name="com_skype_raider" />
        <java-symbol type="drawable" name="com_microsoft_office_lync15" /> 
        <java-symbol type="drawable" name="cn_xuexi_android" />

        <java-symbol type="drawable" name="com_device_cloudlink_smartrooms" /> 
        <java-symbol type="drawable" name="com_yealink_vc_mobile" /> 

        <java-symbol type="drawable" name="com_amazon_avod_thirdpartyclient" /> 
        <java-symbol type="drawable" name="com_google_android_apps_docs_editors_docs" /> 
        <java-symbol type="drawable" name="com_google_android_apps_docs_editors_slides" /> 
        <java-symbol type="drawable" name="com_google_android_apps_docs_editors_sheets" /> 
        <java-symbol type="drawable" name="com_microsoft_office_excel" /> 
        <java-symbol type="drawable" name="com_google_android_apps_docs" /> 
        <java-symbol type="drawable" name="com_microsoft_office_powerpoint" /> 
        <java-symbol type="drawable" name="com_microsoft_office_word" /> 
        <java-symbol type="drawable" name="com_microsoft_office_officehub" /> 
        <java-symbol type="drawable" name="com_zhiliaoapp_musically" /> 
        <java-symbol type="drawable" name="com_spotify_music" /> 
        <java-symbol type="drawable" name="com_wetransfer_app_live" /> 
        <java-symbol type="drawable" name="tv_twitch_android_app" /> 
        <java-symbol type="drawable" name="com_tencent_voov" /> 
        <java-symbol type="drawable" name="com_tencent_wemeet_rooms" /> 
        <java-symbol type="drawable" name="com_deepseek_chat" /> 
        <java-symbol type="drawable" name="com_tencent_wework" />   
        <java-symbol type="drawable" name="tv_danmaku_bilibilihd" />             

        <java-symbol type="style" name="AlertDialogCustom" />      
        <java-symbol type="id" name="aerr_close2" />
        <java-symbol type="id" name="tv_content" />
        <java-symbol type="layout" name="czur_error_dialog" /> 

            <java-symbol type="drawable" name="loop00000" />
            <java-symbol type="drawable" name="loop00001" />
            <java-symbol type="drawable" name="loop00002" />
            <java-symbol type="drawable" name="loop00003" />
            <java-symbol type="drawable" name="loop00004" />
            <java-symbol type="drawable" name="loop00005" />
            <java-symbol type="drawable" name="loop00006" />
            <java-symbol type="drawable" name="loop00007" />
            <java-symbol type="drawable" name="loop00008" />
            <java-symbol type="drawable" name="loop00009" />
            <java-symbol type="drawable" name="loop00010" />
            <java-symbol type="drawable" name="loop00011" />
            <java-symbol type="drawable" name="loop00012" />
            <java-symbol type="drawable" name="loop00013" />
            <java-symbol type="drawable" name="loop00014" />
            <java-symbol type="drawable" name="loop00015" />
            <java-symbol type="drawable" name="loop00016" />
            <java-symbol type="drawable" name="loop00017" />
            <java-symbol type="drawable" name="loop00018" />
            <java-symbol type="drawable" name="loop00019" />
            <java-symbol type="drawable" name="loop00020" />
            <java-symbol type="drawable" name="loop00021" />
            <java-symbol type="drawable" name="loop00022" />
            <java-symbol type="drawable" name="loop00023" />
            <java-symbol type="drawable" name="loop00024" />
            <java-symbol type="drawable" name="loop00025" />
            <java-symbol type="drawable" name="loop00026" />
            <java-symbol type="drawable" name="loop00027" />
            <java-symbol type="drawable" name="loop00028" />
            <java-symbol type="drawable" name="loop00029" />
            <java-symbol type="drawable" name="loop00030" />
            <java-symbol type="drawable" name="loop00031" />
            <java-symbol type="drawable" name="loop00032" />
            <java-symbol type="drawable" name="loop00033" />
            <java-symbol type="drawable" name="loop00034" />
            <java-symbol type="drawable" name="loop00035" />
            <java-symbol type="drawable" name="loop00036" />
            <java-symbol type="drawable" name="loop00037" />
            <java-symbol type="drawable" name="loop00038" />
            <java-symbol type="drawable" name="loop00039" />
            <java-symbol type="drawable" name="loop00040" />
            <java-symbol type="drawable" name="loop00041" />
            <java-symbol type="drawable" name="loop00042" />
            <java-symbol type="drawable" name="loop00043" />
            <java-symbol type="drawable" name="loop00044" />
            <java-symbol type="drawable" name="loop00045" />
            <java-symbol type="drawable" name="loop00046" />
            <java-symbol type="drawable" name="loop00047" />
            <java-symbol type="drawable" name="loop00048" />
            <java-symbol type="drawable" name="loop00049" />
            <java-symbol type="drawable" name="loop00050" />
            <java-symbol type="drawable" name="loop00051" />
            <java-symbol type="drawable" name="loop00052" />
            <java-symbol type="drawable" name="loop00053" />
            <java-symbol type="drawable" name="loop00054" />
            <java-symbol type="drawable" name="loop00055" />
            <java-symbol type="drawable" name="loop00056" />
            <java-symbol type="drawable" name="loop00057" />
            <java-symbol type="drawable" name="loop00057" />
            <java-symbol type="drawable" name="loop00059" />
            <java-symbol type="drawable" name="loop00060" />
            <java-symbol type="drawable" name="loop00061" />
            <java-symbol type="drawable" name="loop00062" />
            <java-symbol type="drawable" name="loop00063" />
            <java-symbol type="drawable" name="loop00064" />
            <java-symbol type="drawable" name="loop00065" />
            <java-symbol type="drawable" name="loop00066" />
            <java-symbol type="drawable" name="loop00067" />
            <java-symbol type="drawable" name="loop00068" />
            <java-symbol type="drawable" name="loop00069" />
            <java-symbol type="drawable" name="loop00070" />
            <java-symbol type="drawable" name="loop00071" />
            <java-symbol type="drawable" name="loop00072" />
            <java-symbol type="drawable" name="loop00073" />
            <java-symbol type="drawable" name="loop00074" />
            <java-symbol type="drawable" name="loop00075" />
            <java-symbol type="drawable" name="loop00076" />
            <java-symbol type="drawable" name="loop00077" />
            <java-symbol type="drawable" name="loop00078" />
            <java-symbol type="drawable" name="loop00079" />
            <java-symbol type="drawable" name="loop00080" />
            <java-symbol type="drawable" name="loop00081" />
            <java-symbol type="drawable" name="loop00082" />
            <java-symbol type="drawable" name="loop00083" />
            <java-symbol type="drawable" name="loop00084" />
            <java-symbol type="drawable" name="loop00085" />
            <java-symbol type="drawable" name="loop00086" />
            <java-symbol type="drawable" name="loop00087" />
            <java-symbol type="drawable" name="loop00088" />
            <java-symbol type="drawable" name="loop00089" />
            <java-symbol type="drawable" name="loop00090" />
            <java-symbol type="drawable" name="loop00091" />
            <java-symbol type="drawable" name="loop00092" />
            <java-symbol type="drawable" name="loop00093" />
            <java-symbol type="drawable" name="loop00094" />
            <java-symbol type="drawable" name="loop00095" />
            <java-symbol type="drawable" name="loop00096" />
            <java-symbol type="drawable" name="loop00097" />
            <java-symbol type="drawable" name="loop00098" />
            <java-symbol type="drawable" name="loop00099"/>
            <java-symbol type="drawable" name="shutdown_bg_small_round_screen" />
            <java-symbol type="id" name="iv_shutdown" />  
  
</resources>
