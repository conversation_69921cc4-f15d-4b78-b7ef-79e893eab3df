/*
 * Copyright 2023 Rockchip Electronics S.LSI Co. LTD
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.settings.display;

import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.hardware.display.DisplayManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Message;
import android.os.ServiceManager;
import android.os.SystemProperties;
import android.preference.Preference.OnPreferenceChangeListener;
import android.text.TextUtils;
import android.util.Log;
import android.view.IWindowManager;
import android.view.LayoutInflater;
import android.view.Surface;
import android.view.View;
import android.view.ViewGroup;

import androidx.fragment.app.DialogFragment;
import androidx.preference.CheckBoxPreference;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import androidx.preference.SwitchPreference;
import androidx.preference.PreferenceCategory;
import androidx.preference.PreferenceScreen;

import com.android.internal.logging.nano.MetricsProto.MetricsEvent;
import com.android.settings.HdmiListPreference;
import com.android.settings.R;
import com.android.settings.SettingsPreferenceFragment;

import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.android.settings.display.DrmDisplaySetting.DPY_STATUS_CONNECTED;

import android.provider.Settings;
// 反射相关
import java.lang.reflect.Method;
import java.lang.reflect.InvocationTargetException;

// Android OS相关
import android.os.IBinder;
import android.os.RemoteException;
import android.os.ServiceManager;  // 如果使用ServiceManager方式
import android.hardware.hdmi.HdmiControlManager;
import android.hardware.hdmi.HdmiDeviceInfo;
import android.hardware.hdmi.IHdmiControlCallback;
import android.hardware.hdmi.HdmiPlaybackClient;
import android.hardware.hdmi.HdmiTvClient;
import android.hardware.hdmi.HdmiAudioSystemClient;


public class HdmiSettings extends SettingsPreferenceFragment
        implements Preference.OnPreferenceChangeListener,
        Preference.OnPreferenceClickListener {
    /**
     * Called when the activity is first created.
     */
    private static final String TAG = "HdmiSettings";
    private static final String KEY_SYSTEM_ROTATION = "system_rotation";
    private static final String KEY_PRE_CATE = "Display";
    private static final String KEY_PRE_RESOLUTION = "Resolution";
    private static final String KEY_PRE_SCREEN_SCALE = "ScreenScale";
    private static final String KEY_AUX_CATEGORY = "aux_category";
    private static final String KEY_AUX_SCREEN_VH = "aux_screen_vh";
    private static final String KEY_AUX_SCREEN_VH_LIST = "aux_screen_vhlist";
    private final static String SYS_NODE_HDMI_STATUS =
            "/sys/devices/platform/display-subsystem/drm/card0/card0-HDMI-A-1/status";
    private final static String SYS_NODE_DP_STATUS =
            "/sys/devices/platform/display-subsystem/drm/card0/card0-DP-1/status";


    //const
    private static final String KEY_CEC_CATEGORY = "cec_category"; 
    private static final String KEY_HDMI_CEC_SLEEP_BY_RK = "hdmi_cec_sleep_by_rk";
    private static final String KEY_HDMI_CEC_WAKEUP_BY_RK = "hdmi_cec_wakeup_by_rk";
    private static final String KEY_HDMI_CEC_POWERON_BY_RK = "hdmi_cec_poweron_by_rk";
    private static final String KEY_HDMI_CEC_POWEROFF_BY_RK = "hdmi_cec_poweroff_by_rk";
    private static final String KEY_HDMI_CEC_SLEEP_BY_EXTERNAL = "hdmi_cec_sleep_by_external";
    private static final String KEY_HDMI_CEC_WAKEUP_BY_EXTERNAL = "hdmi_cec_wakeup_by_external";
    private static final String KEY_HDMI_CEC_POWERON_BY_EXTERNAL = "hdmi_cec_poweron_by_external";
    private static final String KEY_HDMI_CEC_POWEROFF_BY_EXTERNAL = "hdmi_cec_poweroff_by_external";
    //CEC功能选项开关属性常量：
    private static final String PROPERTY_HDMI_CEC_ENABLE = "persist.sys.hdmi_cec_enable";
    private static final String PROPERTY_HDMI_CEC_SLEEP_BY_RK = "persist.sys.hdmi_cec_sleep_by_rk";
    private static final String PROPERTY_HDMI_CEC_WAKEUP_BY_RK = "persist.sys.hdmi_cec_wakeup_by_rk";
    private static final String PROPERTY_HDMI_CEC_POWERON_BY_RK = "persist.sys.hdmi_cec_poweron_by_rk";
    private static final String PROPERTY_HDMI_CEC_POWEROFF_BY_RK = "persist.sys.hdmi_cec_poweroff_by_rk";
    private static final String PROPERTY_HDMI_CEC_SLEEP_BY_EXTERNAL = "persist.sys.hdmi_cec_sleep_by_external";
    private static final String PROPERTY_HDMI_CEC_WAKEUP_BY_EXTERNAL = "persist.sys.hdmi_cec_wakeup_by_external";
    private static final String PROPERTY_HDMI_CEC_POWERON_BY_EXTERNAL = "persist.sys.hdmi_cec_poweron_by_external";  
    private static final String PROPERTY_HDMI_CEC_POWEROFF_BY_EXTERNAL = "persist.sys.hdmi_cec_poweroff_by_external";   
    //CEC 调用测试
    private boolean isHdmiCecEnabled = false;
    private HdmiPlaybackClient  mHdmiPlaybackClient = null;
    private HdmiTvClient  mHdmiTvClient = null;
    private HdmiAudioSystemClient mHdmiAudioSystemClient = null;
    private HdmiControlManager hdmiManager;
    //CEC SwitchPreference开关
    private SwitchPreference hdmiCecSleepByRkPref;
    private SwitchPreference hdmiCecWakeupByRkPref;
    private SwitchPreference hdmiCecPoweronByRkPref;
    private SwitchPreference hdmiCecPoweroffByRkPref;
    private SwitchPreference hdmiCecSleepByExternalPref;
    private SwitchPreference hdmiCecWakeupByExternalPref;
    private SwitchPreference hdmiCecPoweronByExternalPref;
    private SwitchPreference hdmiCecPoweroffByExternalPref;
    private SwitchPreference hdmiCecEnablePref;

    private static final int MSG_UPDATE_STATUS = 0;
    private static final int MSG_UPDATE_STATUS_UI = 1;
    private static final int MSG_SWITCH_DEVICE_STATUS = 2;
    private static final int MSG_UPDATE_DIALOG_INFO = 3;
    private static final int MSG_SHOW_CONFIRM_DIALOG = 4;
    private static final int SWITCH_STATUS_OFF_ON = 0;
    private static final int SWITCH_STATUS_OFF = 1;
    private static final int SWITCH_STATUS_ON = 2;
    private static final long SWITCH_DEVICE_DELAY_TIME = 200;
    private static final long TIME_WAIT_DEVICE_CONNECT = 10000;
    //we found setprop not effect sometimes if control quickly
    private static final boolean USED_OFFON_RESOLUTION = false;

    /**
     * TODO
     * 目前hwc配置了prop属性开关hdmi和dp，如果是其他的设备，需要配合修改，才能进行开关。因此直接写节点进行开关
     * vendor.hdmi_status.aux：/sys/devices/platform/display-subsystem/drm/card0/card0-HDMI-A-1/status
     * vendor.dp_status.aux暂无：/sys/devices/platform/display-subsystem/drm/card0/card0-DP-1/status
     */
    private String main_switch_node = SYS_NODE_HDMI_STATUS;
    private String aux_switch_node = SYS_NODE_DP_STATUS;

    private ListPreference mSystemRotation;
    private PreferenceCategory mAuxCategory;
    private CheckBoxPreference mAuxScreenVH;
    private ListPreference mAuxScreenVHList;
    private Context mContext;
    private DisplayInfo mSelectDisplayInfo;
    private DisplayManager mDisplayManager;
    private DisplayListener mDisplayListener;
    private IWindowManager mWindowManager;
    private ProgressDialog mProgressDialog;
    private boolean mDestory;
    private boolean mEnableDisplayListener;
    private Object mLock = new Object();//maybe android reboot if not lock with new thread
    private boolean mResume;
    private long mWaitDialogCountTime;
    private int mRotation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED;

    private final String HDMI_ACTION = "android.intent.action.HDMI_PLUGGED";
    private final String DP_ACTION = "android.intent.action.DP_PLUGGED";

    private PreferenceCategory mCecCategory;

    private HashMap<Integer, DisplayInfo> mDisplayInfoList = new HashMap<Integer, DisplayInfo>();

    private boolean mForceRefresh = false;

    enum ITEM_CONTROL {
        SHOW_RESOLUTION_ITEM,//展示分辨率选项
        CHANGE_RESOLUTION,//切换分辨率
        REFRESH_DISPLAY_STATUS_INFO,//刷新主屏信息
    }

    private Handler mHandler = new Handler() {
        @Override
        public void handleMessage(final Message msg) {
            Log.v(TAG, "handleMessage " + msg.what);
            if (mDestory && MSG_SWITCH_DEVICE_STATUS != msg.what) {
                return;
            }
            if (MSG_UPDATE_STATUS == msg.what) {
                final ITEM_CONTROL control = (ITEM_CONTROL) msg.obj;
                new Thread() {
                    @Override
                    public void run() {
                        if (ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO == control
                                || ITEM_CONTROL.CHANGE_RESOLUTION == control) {
                            getDisplayStatusInfo();
                        } else if (ITEM_CONTROL.SHOW_RESOLUTION_ITEM == control) {
                            getDisplayStatusInfo();
                            getDisplayResolutionInfo(mSelectDisplayInfo);
                        }
                        Message message = new Message();
                        message.what = MSG_UPDATE_STATUS_UI;
                        message.obj = control;
                        mHandler.sendMessage(message);
                    }
                }.start();
            } else if (MSG_UPDATE_STATUS_UI == msg.what) {
                ITEM_CONTROL control = (ITEM_CONTROL) msg.obj;
                if (ITEM_CONTROL.SHOW_RESOLUTION_ITEM == control) {
                    updateStateUI();
                    if (null != mSelectDisplayInfo
                            && DPY_STATUS_CONNECTED == mSelectDisplayInfo.getStatus()) {
                        showResolutionItemUI(mSelectDisplayInfo);
                    }
                    hideWaitingDialog();
                } else if (ITEM_CONTROL.CHANGE_RESOLUTION == control) {
                    updateStateUI();
                    showConfirmSetModeDialog();
                    hideWaitingDialog();
                } else if (ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO == control) {
                    updateStateUI();
                    hideWaitingDialog();
                }
                mEnableDisplayListener = true;
                Log.v(TAG, "mEnableDisplayListener set true" );
            } else if (MSG_SWITCH_DEVICE_STATUS == msg.what) {
                final ITEM_CONTROL control = (ITEM_CONTROL) msg.obj;
                if (SWITCH_STATUS_ON == msg.arg1) {
                    if (ITEM_CONTROL.CHANGE_RESOLUTION == control
                            || ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO == control) {
                        showWaitingDialog(R.string.dialog_wait_screen_connect);
                        new Thread() {
                            @Override
                            public void run() {
                                write2Node(main_switch_node, "detect");
                                mWaitDialogCountTime = TIME_WAIT_DEVICE_CONNECT / 1000;
                                mHandler.removeMessages(MSG_UPDATE_DIALOG_INFO);
                                mHandler.sendEmptyMessage(MSG_UPDATE_DIALOG_INFO);
                                sendUpdateStateMsg(control, TIME_WAIT_DEVICE_CONNECT);
                            }
                        }.start();
                    }
                } else {
                    if (ITEM_CONTROL.CHANGE_RESOLUTION == control
                            || ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO == control) {
                        new Thread() {
                            @Override
                            public void run() {
                                write2Node(main_switch_node, "off");
                                if (SWITCH_STATUS_OFF_ON == msg.arg1) {
                                    sendSwitchDeviceOffOnMsg(control, SWITCH_STATUS_ON);
                                } else {
                                    sendUpdateStateMsg(control, 2000);
                                }
                            }
                        }.start();
                    }
                }
            } else if (MSG_UPDATE_DIALOG_INFO == msg.what) {
                if (mWaitDialogCountTime > 0) {//这边
                    if (null != mProgressDialog && mProgressDialog.isShowing()) {
                        mProgressDialog.setMessage(getContext().getString(
                                R.string.dialog_wait_screen_connect) + " " + mWaitDialogCountTime);
                        mWaitDialogCountTime--;
                        mHandler.removeMessages(MSG_UPDATE_DIALOG_INFO);
                        mHandler.sendEmptyMessageDelayed(MSG_UPDATE_DIALOG_INFO, 1000);
                    }
                }
            } else if (MSG_SHOW_CONFIRM_DIALOG == msg.what) {
                mHandler.removeMessages(MSG_SHOW_CONFIRM_DIALOG);
                hideWaitingDialog();
                showConfirmSetModeDialog();
            }
        }
    };

    private final BroadcastReceiver HdmiListener = new BroadcastReceiver() {
        @Override
        public void onReceive(Context ctxt, Intent receivedIt) {
            String action = receivedIt.getAction();
            if (HDMI_ACTION.equals(action) || DP_ACTION.equals(action)) {
                boolean state = receivedIt.getBooleanExtra("state", false);
                if (state) {
                    Log.d(TAG, "BroadcastReceiver.onReceive() : Connected HDMI-TV");
                } else {
                    Log.d(TAG, "BroadcastReceiver.onReceive() : Disconnected HDMI-TV");
                }
                DrmDisplaySetting.updateDisplayInfos();
                if (mEnableDisplayListener) {
                    refreshState();
                } else {
                    mForceRefresh = true;
                }
            }
        }
    };

    @Override
    public int getMetricsCategory() {
        return MetricsEvent.DISPLAY;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = getActivity();
        mRotation = getActivity().getRequestedOrientation();
        getActivity().setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
        mDisplayManager = (DisplayManager) mContext.getSystemService(Context.DISPLAY_SERVICE);
        mWindowManager = IWindowManager.Stub.asInterface(
                ServiceManager.getService(Context.WINDOW_SERVICE));
        mDisplayListener = new DisplayListener();
        addPreferencesFromResource(R.xml.hdmi_settings);
        init();
        init_hdmi_cec();
        mEnableDisplayListener = true;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView----------------------------------------");
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onResume() {
        super.onResume();
        //showWaitingDialog(0, "");
        IntentFilter filter = new IntentFilter();
        filter.addAction(HDMI_ACTION);
        filter.addAction(DP_ACTION);
        getContext().registerReceiver(HdmiListener, filter);
        //refreshState();
        mDisplayManager.registerDisplayListener(mDisplayListener, null);
        mResume = true;
    }

    private void showWaitingDialog(int msgResId) {
        if (mDestory) {
            return;
        }
        if (null == mProgressDialog) {
            mProgressDialog = new ProgressDialog(getActivity());
            mProgressDialog.setCanceledOnTouchOutside(false);
            mProgressDialog.setCancelable(false);
        }
        mProgressDialog.setMessage(getContext().getString(msgResId));
        if (!mProgressDialog.isShowing()) {
            mProgressDialog.show();
        }
    }

    private void hideWaitingDialog() {
        if (null != mProgressDialog && mProgressDialog.isShowing()) {
            mProgressDialog.cancel();
            mProgressDialog = null;
        }
    }

    public void onPause() {
        super.onPause();
        mResume = false;
        Log.d(TAG, "onPause----------------");
        mDisplayManager.unregisterDisplayListener(mDisplayListener);
        getContext().unregisterReceiver(HdmiListener);
    }

    public void onDestroy() {
        mDestory = true;
        getActivity().setRequestedOrientation(mRotation);
        super.onDestroy();
        mHandler.removeMessages(MSG_UPDATE_STATUS);
        mHandler.removeMessages(MSG_SWITCH_DEVICE_STATUS);
        mHandler.removeMessages(MSG_UPDATE_DIALOG_INFO);
        mHandler.removeMessages(MSG_SHOW_CONFIRM_DIALOG);
        hideWaitingDialog();
        Log.d(TAG, "onDestroy----------------");
    }

    private void init() {
        //boolean showSystemRotation = mShowSettings != DISPLAY_SHOW_SETTINGS.ONLY_SHOW_AUX;
        boolean showSystemRotation = false;
        if (showSystemRotation) {
            mSystemRotation = (ListPreference) findPreference(KEY_SYSTEM_ROTATION);
            mSystemRotation.setOnPreferenceChangeListener(this);
            try {
                int rotation = mWindowManager.getDefaultDisplayRotation();
                switch (rotation) {
                    case Surface.ROTATION_0:
                        mSystemRotation.setValue("0");
                        break;
                    case Surface.ROTATION_90:
                        mSystemRotation.setValue("90");
                        break;
                    case Surface.ROTATION_180:
                        mSystemRotation.setValue("180");
                        break;
                    case Surface.ROTATION_270:
                        mSystemRotation.setValue("270");
                        break;
                    default:
                        mSystemRotation.setValue("0");
                }
            } catch (Exception e) {
                Log.e(TAG, e.toString());
            }
        } else {
            removePreference(KEY_SYSTEM_ROTATION);
        }//这边
        int displayNumber = DrmDisplaySetting.getDisplayNumber();
        Log.v(TAG, "displayNumber=" + displayNumber);
        String[] connectorInfos = DrmDisplaySetting.getConnectorInfo();
        if (null != connectorInfos) {
            for (int i = 0; i < connectorInfos.length; i++) {
                Log.v(TAG, i + " connectorInfo====" + connectorInfos[i]);
            }
        }
        mDisplayInfoList.clear();
        for (int i = 0; i < displayNumber; i++) {
            String typeName = "";
            String id = "";
            if (null != connectorInfos && connectorInfos.length == displayNumber) {
                String[] rets = connectorInfos[i].split(",");
                if (null != rets && rets.length > 2) {
                    String type = rets[0].replaceAll("type:", "");
                    typeName = DrmDisplaySetting.CONNECTOR_DISPLAY_NAME.get(type);
                    id = rets[1].replaceAll("id:", "");
                    Log.v(TAG, "type======" + type + ", typeName=" + typeName + ", id=" + id);
                    if (DrmDisplaySetting.DRM_MODE_CONNECTOR_VIRTUAL.equals(typeName)
                            || DrmDisplaySetting.DRM_MODE_CONNECTOR_eDP.equals(typeName)
                            || DrmDisplaySetting.DRM_MODE_CONNECTOR_DSI.equals(typeName)) {
                        continue;
                    }
                }
            }
            int display = i;
            PreferenceCategory category = new PreferenceCategory(mContext);
            category.setKey(KEY_PRE_CATE + display);
            if ("0".equals(id) || "".equals(id)) {
                category.setTitle(typeName);
            } else {
                category.setTitle(typeName + "-" + id);
            }
            getPreferenceScreen().addPreference(category);
            //add resolution preference
            HdmiListPreference resolutionPreference = new HdmiListPreference(mContext);
            resolutionPreference.setKey(KEY_PRE_RESOLUTION + display);
            resolutionPreference.setTitle(mContext.getString(R.string.screen_resolution));
            resolutionPreference.setOnPreferenceClickListener(this);
            resolutionPreference.setOnPreferenceChangeListener(this);
            category.addPreference(resolutionPreference);
            //add scale preference
            Preference scalePreference = new Preference(mContext);
            scalePreference.setKey(KEY_PRE_SCREEN_SCALE + display);
            scalePreference.setTitle(mContext.getString(R.string.screen_scale));
            scalePreference.setOnPreferenceClickListener(this);
            category.addPreference(scalePreference);
            category.setEnabled(false);
            DisplayInfo displayInfo = new DisplayInfo();
            displayInfo.setDisplayNo(display);
            mDisplayInfoList.put(display, displayInfo);

        }
        sendUpdateStateMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, 0);
        mAuxCategory = (PreferenceCategory) findPreference(KEY_AUX_CATEGORY);
        mAuxScreenVH = (CheckBoxPreference) findPreference(KEY_AUX_SCREEN_VH);
        mAuxScreenVH.setChecked(SystemProperties.getBoolean("persist.sys.rotation.efull", false));
        mAuxScreenVH.setOnPreferenceChangeListener(this);
        mAuxCategory.removePreference(mAuxScreenVH);
        mAuxScreenVHList = (ListPreference) findPreference(KEY_AUX_SCREEN_VH_LIST);
        mAuxScreenVHList.setOnPreferenceChangeListener(this);
        mAuxScreenVHList.setOnPreferenceClickListener(this);
        mAuxCategory.removePreference(mAuxScreenVHList);
       
    }
        //init cec service
     
    private void init_hdmi_cec(){
     
        // 使用官方公开的 HdmiControlManager
        Context context = getActivity();

        hdmiManager = (HdmiControlManager) context.getSystemService(Context.HDMI_CONTROL_SERVICE);

        // 检查 CEC 是否启用
        if (hdmiManager != null) {           
            mHdmiPlaybackClient = hdmiManager.getPlaybackClient(); //获取LocalPlaybackClient (HdmiPlaybackClient) getClient(HdmiDeviceInfo.DEVICE_PLAYBACK);
            mHdmiTvClient = hdmiManager.getTvClient();  //获取HdmiTvClient
            mHdmiAudioSystemClient =  hdmiManager.getAudioSystemClient(); //获取dmiAudioSystemClient
        }
       
        hdmiCecSleepByRkPref = findPreference("hdmi_cec_sleep_by_rk");
        if (hdmiCecSleepByRkPref != null) {
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_sleep_by_rk",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecSleepByRkPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecSleepByRkPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    Log.i(TAG,"Hdmi CEC isHdmiCecEnabled =" + isHdmiCecEnabled);
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_sleep_by_rk",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_RK, "1");
                        hdmiCecSleepByRkPref.setChecked(true);                       
                        // 发送休眠命令
                    //    mHdmiPlaybackClient.sendStandby();                        
                        Log.i(TAG,"Hdmi CEC mHdmiPlaybackClient Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_sleep_by_rk",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_RK, "0");
                        hdmiCecSleepByRkPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC mHdmiPlaybackClient Devices sendStandby failed!");
                    }                   
                 }else{
                        
                        Log.i(TAG,"Hdmi CEC mHdmiPlaybackClient Devices ont found,sendStandby failed!");
                 }            

                Log.i(TAG, "hdmiCecSleepByRkPref changed ok !");
                        
                // 3. 通过反射调用HDMI CEC服务
                // try {
                //     // 获取ServiceManager
                //     Class<?> serviceManager = Class.forName("android.os.ServiceManager");
                //     Class<?> hdmiDeviceInfoClass = Class.forName("android.hardware.hdmi.HdmiDeviceInfo");
                //     Method getService = serviceManager.getMethod("getService", String.class);
                //     IBinder binder = (IBinder) getService.invoke(null, "hdmi_control");
                    
                //     if (binder == null) {
                //         Log.e(TAG, "HDMI CEC service not found");
                //         throw new RuntimeException("HDMI CEC service not available");
                //     }

                //     // 获取Stub接口
                //     Class<?> stub = Class.forName("android.hardware.hdmi.IHdmiControlService$Stub");
                //     // 打印所有方法名
                //     Log.i(TAG, "===== IHdmiControlService$Stub 方法列表 =====");  
                //     for (Method method : stub.getDeclaredMethods()) {
                //         Log.i(TAG, "Method:"+ method.getName());
                //     }
                //     Log.i(TAG, "====================================");

                //     Method asInterface = stub.getMethod("asInterface", IBinder.class);
                //     Object hdmiService = asInterface.invoke(null, binder);

                //     // 获取 hdmiService 的所有方法并打印
                //     if (hdmiService != null) {
                //         Log.i(TAG, "===== IHdmiControlService 方法列表 =====");  
                //         for (Method method : hdmiService.getClass().getDeclaredMethods()) {
                //             Log.d(TAG, "Method: " + method.getName() + " | Params: " + Arrays.toString(method.getParameterTypes()));
                //         }
                //         Log.i(TAG, "====================================");
                //     }

                //     // 调用getDeviceList方法
                //     Method getDeviceListMethod = hdmiService.getClass().getMethod("getDeviceList");
                //     List<Object> devices = (List<Object>) getDeviceListMethod.invoke(hdmiService);

                //     if (devices == null || devices.isEmpty()) {
                //         throw new RuntimeException("No HDMI devices found");
                //     }
                    
                // //  Object hdmiDeviceInfo = devices.get(0);
                //     for (Object hdmiDeviceInfo : devices) {
                //         // 调用powerOnDevice方法
                //         Method powerOffRemoteDeviceMethod = hdmiService.getClass().getMethod("powerOffRemoteDevice", int.class,int.class);
                //         // Extract necessary parameters from HdmiDeviceInfo
                //         Method getLogicalAddress = hdmiDeviceInfo.getClass().getMethod("getLogicalAddress");
                //         Method getPhysicalAddress = hdmiDeviceInfo.getClass().getMethod("getPhysicalAddress");
                        
                //         int logicalAddress = (int) getLogicalAddress.invoke(hdmiDeviceInfo);
                //         int physicalAddress = (int) getPhysicalAddress.invoke(hdmiDeviceInfo);
                        
                //         // Invoke with correct parameters
                //         powerOffRemoteDeviceMethod.invoke(hdmiService, logicalAddress, physicalAddress);
                //     }                              
                // } catch (ClassNotFoundException e) {
                //     Log.e(TAG, "HDMI CEC classes not found", e);
                // } catch (NoSuchMethodException e) {
                //     Log.e(TAG, "HDMI CEC method not found", e);
                // } catch (InvocationTargetException e) {
                //     Log.e(TAG, "HDMI CEC invocation failed", e);
                // } catch (IllegalAccessException e) {
                //     Log.e(TAG, "HDMI CEC access denied", e);
                // } catch (Exception e) {
                //     Log.e(TAG, "Unexpected error", e);
                // }

                //Log.i(TAG, "hdmiCecSleepByRkPref changed ok!");

                return false;
            });
        }
        
        hdmiCecWakeupByRkPref = findPreference("hdmi_cec_wakeup_by_rk");
        if (hdmiCecWakeupByRkPref != null) {
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_wakeup_by_rk",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecWakeupByRkPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecWakeupByRkPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;
            
                Log.i(TAG, "hdmiCecWakeupByRkPref changed !");
                isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                             
                //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                if(isHdmiCecEnabled && isEnabled){//如果是使能状态
                    if (mHdmiPlaybackClient != null) {
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_wakeup_by_rk",
                            1
                            );
                        SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_RK,  "1");
                        hdmiCecWakeupByRkPref.setChecked(true);
                        //唤醒方法
                        // mHdmiPlaybackClient.oneTouchPlay(new HdmiPlaybackClient.OneTouchPlayCallback() { 
                        //     @Override
                        //     public void onComplete(int result) {
                        //         switch (result) {
                        //             case HdmiControlManager.RESULT_SUCCESS:
                        //                 // 成功处理
                        //                 Log.w(TAG, "One touch play turn no TV successed: " + result);                                      
                        //                 break;
                        //             case HdmiControlManager.RESULT_TIMEOUT:
                        //                 // 超时处理
                        //                 Log.w(TAG, "One touch play turn no failed,Timeout: " + result);                                
                        //             case HdmiControlManager.RESULT_TARGET_NOT_AVAILABLE:
                        //                 // 目标设备不可用处理
                        //                 Log.w(TAG, "One touch play turn no failed,target unvalable: " + result);
                        //                 Settings.System.putInt(
                        //                     requireContext().getContentResolver(),
                        //                     "hdmi_cec_wakeup_by_rk",
                        //                     0
                        //                     );
                        //                 SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_RK,  "0");
                        //                 hdmiCecWakeupByRkPref.setChecked(false);
                        //                 break;
                        //             default:
                        //                 // 其他错误处理
                        //                 Log.w(TAG, "One touch play turn no failed,other reason: " + result);
                        //                 break;
                        //         }
                        //     }
                        //     });
                    }else{                      
                        Log.w(TAG,"Hdmi CEC mHdmiPlaybackClient Devices ont found!");
                    }
                }else{
                    Settings.System.putInt(
                        requireContext().getContentResolver(),
                        "hdmi_cec_wakeup_by_rk",
                        0
                    );
                    SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_RK,  "0");
                    hdmiCecWakeupByRkPref.setChecked(false);
                    Log.w(TAG, "One touch play turn no TV failed!");
                }    
                 
            // try {
            //     // 1. Get HDMI Service
            //     Class<?> serviceManager = Class.forName("android.os.ServiceManager");
            //     Method getService = serviceManager.getMethod("getService", String.class);
            //     IBinder binder = (IBinder) getService.invoke(null, "hdmi_control");

            //     if (binder == null) {
            //         Log.e(TAG, "HDMI-CEC service not found");
            //         throw new RuntimeException("HDMI CEC service not available");
            //     }

            //     // 2. Get IHdmiControlService interface
            //     Class<?> stub = Class.forName("android.hardware.hdmi.IHdmiControlService$Stub");
            //     Method asInterface = stub.getMethod("asInterface", IBinder.class);
            //     Object hdmiService = asInterface.invoke(null, binder);

            //     // 获取 hdmiService 的所有方法并打印
            //     if (hdmiService != null) {
            //         Log.i(TAG, "===== IHdmiControlService 方法列表 =====");  
            //         for (Method method : hdmiService.getClass().getDeclaredMethods()) {
            //             Log.d(TAG, "Method: " + method.getName() + " | Params: " + Arrays.toString(method.getParameterTypes()));
            //         }
            //         Log.i(TAG, "====================================");
            //     }

            //     // 3. Get HDMI devices
            //     Method getDeviceListMethod = hdmiService.getClass().getMethod("getDeviceList");
            //     List<Object> devices = (List<Object>) getDeviceListMethod.invoke(hdmiService);

            //     if (devices == null || devices.isEmpty()) {
            //         Log.e(TAG, "No HDMI-CEC devices found (check TV connection & CEC support)");
            //        throw new RuntimeException("No HDMI-CEC devices found (check TV connection & CEC support)");
            //     }

            //     // 4. Power on the first device
            //     Class<?> hdmiDeviceInfoClass = Class.forName("android.hardware.hdmi.HdmiDeviceInfo");
            //     Object hdmiDeviceInfo = devices.get(0);

            //     // 调用powerOnRemoteDevice方法
            //     Method powerOnRemoteDeviceMethod = hdmiService.getClass().getMethod("powerOnRemoteDevice", int.class,int.class);
            //     // Extract necessary parameters from HdmiDeviceInfo
            //     Method getLogicalAddress = hdmiDeviceInfo.getClass().getMethod("getLogicalAddress");
            //     Method getPhysicalAddress = hdmiDeviceInfo.getClass().getMethod("getPhysicalAddress");
                
            //     int logicalAddress = (int) getLogicalAddress.invoke(hdmiDeviceInfo);
            //     int physicalAddress = (int) getPhysicalAddress.invoke(hdmiDeviceInfo);
                
            //     // Invoke with correct parameters
            //     powerOnRemoteDeviceMethod.invoke(hdmiService, logicalAddress, physicalAddress);
            //    // Method powerOnRemoteDeviceMethod = hdmiService.getClass().getMethod("powerOnRemoteDevice", int.class,int.class);
            //    // powerOnRemoteDeviceMethod.invoke(hdmiService, hdmiDeviceInfo);
            //         Log.e(TAG, "Power on the first device !");
            //     } catch (ClassNotFoundException e) {
            //         Log.e(TAG, "HDMI CEC classes not found", e);
            //        // return false;
            //     } catch (NoSuchMethodException e) {
            //         Log.e(TAG, "HDMI CEC method not found", e);
            //        // return false;
            //     } catch (InvocationTargetException e) {
            //         Log.e(TAG, "HDMI CEC invocation failed", e);
            //       //  return false;
            //     } catch (IllegalAccessException e) {
            //         Log.e(TAG, "HDMI CEC access denied", e);
            //       //  return false;
            //     } catch (Exception e) {
            //         Log.e(TAG, "Unexpected error", e);
            //       //  return false;
            //     }
            //     Log.i(TAG, "hdmiCecWakeupByRkPref changed ok!");
                 return false; // Success: allow preference change
             });
            
        }

        hdmiCecPoweronByRkPref = findPreference("hdmi_cec_poweron_by_rk");
        if (hdmiCecPoweronByRkPref != null) {
            hdmiCecPoweronByRkPref.setVisible(false);  // 设置为不可见
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_poweron_by_rk",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecPoweronByRkPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecPoweronByRkPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;
                Log.i(TAG, "hdmiCecPoweronByRkPref changed !");
                isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                             
                //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                if(isHdmiCecEnabled && isEnabled){//如果是使能状态
                    if (mHdmiPlaybackClient != null) {
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweron_by_rk",
                            1
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_RK,"1");
                        hdmiCecPoweronByRkPref.setChecked(true);
                        //唤醒方法
                        // mHdmiPlaybackClient.oneTouchPlay(new HdmiPlaybackClient.OneTouchPlayCallback() { 
                        //     @Override
                        //     public void onComplete(int result) {
                        //         switch (result) {
                        //             case HdmiControlManager.RESULT_SUCCESS:
                        //                 // 成功处理
                        //                 Log.w(TAG, "One touch play turn no TV successed: " + result);
 
                        //                 break;
                        //             case HdmiControlManager.RESULT_TIMEOUT:
                        //                 // 超时处理
                        //                 Log.w(TAG, "One touch play turn no failed,Timeout: " + result);                                
                        //             case HdmiControlManager.RESULT_TARGET_NOT_AVAILABLE:
                        //                 // 目标设备不可用处理
                        //                 Log.w(TAG, "One touch play turn no failed,target unvalable: " + result);
                        //                 Settings.System.putInt(
                        //                     requireContext().getContentResolver(),
                        //                     "hdmi_cec_poweron_by_rk",
                        //                     0
                        //                     );
                        //                 SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_RK,  "0");
                        //                 hdmiCecPoweronByRkPref.setChecked(false);
                        //                 break;
                        //             default:
                        //                 // 其他错误处理
                        //                 Log.w(TAG, "One touch play turn no failed,other reason: " + result);
                        //                 break;
                        //         }
                        //     }
                        //     });
                    }else{
                       
                        Log.w(TAG,"Hdmi CEC mHdmiPlaybackClient Devices ont found!");
                    }
                }else{
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweron_by_rk",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_RK,  "0");
                    hdmiCecPoweronByRkPref.setChecked(false);
                    Log.w(TAG, "One touch play turn no TV failed ");
                }
                return false;
            });
        }

        hdmiCecPoweroffByRkPref = findPreference("hdmi_cec_poweroff_by_rk");
        if (hdmiCecPoweroffByRkPref != null) {
            hdmiCecPoweroffByRkPref.setVisible(false);  // 设置为不可见
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_poweroff_by_rk",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecPoweroffByRkPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecPoweroffByRkPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_rk",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_RK, "1");
                        hdmiCecPoweroffByRkPref.setChecked(true);                       
                        // 发送休眠命令
                        //mHdmiPlaybackClient.sendStandby();
                        
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByRkPref Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_rk",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_RK, "0");
                        hdmiCecPoweroffByRkPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByRkPref Devices sendStandby failed!");
                    }                   
                 }else{                  
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_rk",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_RK,  "0");
                    hdmiCecPoweroffByRkPref.setChecked(false);      
                    Log.w(TAG,"Hdmi CEC mHdmiPlaybackClient Devices ont found,poweroff failed!");
                 }         
                return false;
            });
        }

        hdmiCecSleepByExternalPref = findPreference("hdmi_cec_sleep_by_external");
        if (hdmiCecSleepByExternalPref != null) {
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_sleep_by_external",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecSleepByExternalPref.setChecked(currentValue);  // 设置初始状态
            hdmiCecSleepByExternalPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_sleep_by_external",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_EXTERNAL, "1");
                        hdmiCecSleepByExternalPref.setChecked(true);                       
                        // 发送休眠命令
                        //mHdmiPlaybackClient.sendStandby();
                        
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByRkPref Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_sleep_by_external",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_EXTERNAL, "0");
                        hdmiCecSleepByExternalPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByRkPref Devices sendStandby failed!");
                    }                   
                 }else{                  
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_sleep_by_external",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_EXTERNAL,  "0");
                    hdmiCecSleepByExternalPref.setChecked(false);      
                    Log.w(TAG,"Hdmi CEC hdmiCecSleepByExternalPref Devices ont found,poweroff failed!");
                 }         
                return false;
            });
        }


        hdmiCecWakeupByExternalPref = findPreference("hdmi_cec_wakeup_by_external");
        if (hdmiCecWakeupByExternalPref != null) {
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_wakeup_by_external",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecWakeupByExternalPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecWakeupByExternalPref.setOnPreferenceChangeListener((preference, newValue) -> {
                 boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_wakeup_by_external",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_EXTERNAL, "1");
                        hdmiCecWakeupByExternalPref.setChecked(true);                       
                        // 发送休眠命令
                        //mHdmiPlaybackClient.sendStandby();
                        
                        Log.w(TAG,"Hdmi CEC hdmiCecWakeupByExternalPref Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_wakeup_by_external",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_EXTERNAL, "0");
                        hdmiCecWakeupByExternalPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC hdmiCecWakeupByExternalPref Devices sendStandby failed!");
                    }                   
                 }else{                  
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_wakeup_by_external",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_EXTERNAL,  "0");
                    hdmiCecWakeupByExternalPref.setChecked(false);      
                    Log.w(TAG,"Hdmi CEC hdmiCecWakeupByExternalPref Devices ont found,poweroff failed!");
                 }         
                return false;
            });
        }

        hdmiCecPoweronByExternalPref = findPreference("hdmi_cec_poweron_by_external");
        if (hdmiCecPoweronByExternalPref != null) {
            hdmiCecPoweronByExternalPref.setVisible(false);  // 设置为不可见
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_poweron_by_external",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecPoweronByExternalPref.setChecked(currentValue);  // 设置初始状态
            hdmiCecPoweronByExternalPref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweron_by_external",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_EXTERNAL, "1");
                        hdmiCecPoweronByExternalPref.setChecked(true);                       
                        // 发送休眠命令
                        //mHdmiPlaybackClient.sendStandby();
                        
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweronByExternalPref Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweron_by_external",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_EXTERNAL, "0");
                        hdmiCecPoweronByExternalPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC hdmiCecWakeupByExternalPref Devices sendStandby failed!");
                    }                   
                 }else{                  
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweron_by_external",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_EXTERNAL,  "0");
                    hdmiCecPoweronByExternalPref.setChecked(false);      
                    Log.w(TAG,"Hdmi CEC hdmiCecWakeupByExternalPref Devices ont found,poweroff failed!");
                 }         
                return false;
            });
        }

        hdmiCecPoweroffByExternalPref = findPreference("hdmi_cec_poweroff_by_external");
        if (hdmiCecPoweroffByExternalPref != null) {
            hdmiCecPoweroffByExternalPref.setVisible(false);  // 设置为不可见
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_poweroff_by_external",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            
            hdmiCecPoweroffByExternalPref.setChecked(currentValue);  // 设置初始状态

            hdmiCecPoweroffByExternalPref.setOnPreferenceChangeListener((preference, newValue) -> {
                 boolean isEnabled = (Boolean) newValue;                
                 //异步操作的回调接口,用于让 TV 切换到当前设备的输入源
                 if (mHdmiPlaybackClient != null) {
                    isHdmiCecEnabled = hdmiManager.getHdmiCecEnabled() == HdmiControlManager.HDMI_CEC_CONTROL_ENABLED;                 
                    if(isHdmiCecEnabled && isEnabled){
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_external",
                            1 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_EXTERNAL, "1");
                        hdmiCecPoweroffByExternalPref.setChecked(true);                       
                        // 发送休眠命令
                        //mHdmiPlaybackClient.sendStandby();
                        
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByExternalPref Devices sendStandby succeed!");
                    }else{
                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_external",
                            0 
                        );
                        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_EXTERNAL, "0");
                        hdmiCecPoweroffByExternalPref.setChecked(false);
                        Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByExternalPref Devices sendStandby failed!");
                    }                   
                 }else{                  
                    Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_poweroff_by_external",
                            0
                            );
                    SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_EXTERNAL,  "0");
                    hdmiCecPoweroffByExternalPref.setChecked(false);      
                    Log.w(TAG,"Hdmi CEC hdmiCecPoweroffByExternalPref Devices ont found,poweroff failed!");
                 }         
                return false;
            });
        }
        // mCecCategory = (PreferenceCategory) findPreference(KEY_CEC_CATEGORY);
        // //add sleep by rk preference
        // HdmiListPreference sleepByRkPreference = new HdmiListPreference(mContext);
        // sleepByRkPreference.setKey(KEY_HDMI_CEC_SLEEP_BY_RK);
        // sleepByRkPreference.setTitle(mContext.getString(R.string.hdmi_cec_sleep_by_rk_title));
        // sleepByRkPreference.setOnPreferenceClickListener(this);
        // sleepByRkPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(sleepByRkPreference);

        // //add wakeup by rk preference
        // HdmiListPreference wakeupByRkPreference = new HdmiListPreference(mContext);
        // wakeupByRkPreference.setKey(KEY_HDMI_CEC_WAKEUP_BY_RK);
        // wakeupByRkPreference.setTitle(mContext.getString(R.string.hdmi_cec_wakeup_by_rk_title));
        // wakeupByRkPreference.setOnPreferenceClickListener(this);
        // wakeupByRkPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(wakeupByRkPreference);

        // //add poweron by rk preference
        // HdmiListPreference poweronByRkPreference = new HdmiListPreference(mContext);
        // poweronByRkPreference.setKey(KEY_HDMI_CEC_POWERON_BY_RK);
        // poweronByRkPreference.setTitle(mContext.getString(R.string.hdmi_cec_poweron_by_rk_title));
        // poweronByRkPreference.setOnPreferenceClickListener(this);
        // poweronByRkPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(poweronByRkPreference);

        // //add poweroff by rk preference
        // HdmiListPreference poweroffByRkPreference = new HdmiListPreference(mContext);
        // poweroffByRkPreference.setKey(KEY_HDMI_CEC_POWEROFF_BY_RK);
        // poweroffByRkPreference.setTitle(mContext.getString(R.string.hdmi_cec_poweroff_by_rk_title));
        // poweroffByRkPreference.setOnPreferenceClickListener(this);
        // poweroffByRkPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(poweroffByRkPreference);

        // //add sleep by external preference
        // HdmiListPreference sleepByExternalPreference = new HdmiListPreference(mContext);
        // sleepByExternalPreference.setKey(KEY_HDMI_CEC_SLEEP_BY_EXTERNAL);
        // sleepByExternalPreference.setTitle(mContext.getString(R.string.hdmi_cec_sleep_by_external_title));
        // sleepByExternalPreference.setOnPreferenceClickListener(this);
        // sleepByExternalPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(sleepByExternalPreference);

        // //add wakeup by external preference
        // HdmiListPreference wakeupByExternalPreference = new HdmiListPreference(mContext);
        // wakeupByExternalPreference.setKey(KEY_HDMI_CEC_WAKEUP_BY_EXTERNAL);
        // wakeupByExternalPreference.setTitle(mContext.getString(R.string.hdmi_cec_wakeup_by_external_title));
        // wakeupByExternalPreference.setOnPreferenceClickListener(this);
        // wakeupByExternalPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(wakeupByExternalPreference);

        // //add poweron by external preference
        // HdmiListPreference poweronByExternalPreference = new HdmiListPreference(mContext);
        // poweronByExternalPreference.setKey(KEY_HDMI_CEC_POWERON_BY_EXTERNAL);
        // poweronByExternalPreference.setTitle(mContext.getString(R.string.hdmi_cec_poweron_by_external_title));
        // poweronByExternalPreference.setOnPreferenceClickListener(this);
        // poweronByExternalPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(poweronByExternalPreference);

        // //add poweroff by external preference
        // HdmiListPreference poweroffByExternalPreference = new HdmiListPreference(mContext);
        // poweroffByExternalPreference.setKey(KEY_HDMI_CEC_POWEROFF_BY_EXTERNAL);
        // poweroffByExternalPreference.setTitle(mContext.getString(R.string.hdmi_cec_poweroff_by_external_title));
        // poweroffByExternalPreference.setOnPreferenceClickListener(this);
        // poweroffByExternalPreference.setOnPreferenceChangeListener(this);
        // mCecCategory.addPreference(poweroffByExternalPreference);

        // 获取 SwitchPreference 并设置监听
        hdmiCecEnablePref = findPreference("hdmi_cec_enable");
        if (hdmiCecEnablePref != null) {
            // 从 Settings.System 读取当前值，并设置给 SwitchPreference
            boolean currentValue = Settings.System.getInt(
                requireContext().getContentResolver(),
                "hdmi_cec_enable",
                1  // 默认值（1 表示 true，0 表示 false）
            ) == 1;
            if(currentValue){ 
                hdmiCecEnablePref.setChecked(currentValue);  // 设置初始状态             
            }else{//没有使能CEC，所以禁掉全部功能
                disabeHdmiCecAndSyncStatus();
               
            }
           
            hdmiCecEnablePref.setOnPreferenceChangeListener((preference, newValue) -> {
                boolean isEnabled = (Boolean) newValue;             
                
                // 启用/禁用 HDMI-CEC
                if (hdmiManager != null) {                   
                    boolean enabled = (Boolean) newValue; // 显式转换为boolean
                    if(enabled){//打开HDMI CEC使能功能
                        int cecEnabled = enabled ? HdmiControlManager.HDMI_CEC_CONTROL_ENABLED 
                            : HdmiControlManager.HDMI_CEC_CONTROL_DISABLED;
                        hdmiManager.setHdmiCecEnabled(cecEnabled);

                        Settings.System.putInt(
                            requireContext().getContentResolver(),
                            "hdmi_cec_enable",
                            1
                        );
                        //同步到 SystemProperties（系统服务可读取）
                        SystemProperties.set(PROPERTY_HDMI_CEC_ENABLE, "1");
                        hdmiCecEnablePref.setChecked(true);
                        
                    }else{//关闭CEC功能，所以禁掉全部功能
                         hdmiManager.setHdmiCecEnabled(0);
                         disabeHdmiCecAndSyncStatus();
                    }                 
                    Log.i(TAG, "hdmiManager.setHdmiCecEnabled= "+enabled);
                }else{
                        //如果没有HDMI CEC，则关闭CEC功能，所以禁掉全部功能
                         disabeHdmiCecAndSyncStatus();
                }
           
                // Log.i(TAG, "hdmiCecEnablePref changed !");
                // // 2. 通过反射调用 HdmiControlManager
                // try {
                //     // 获取 HdmiControlManager
                //     Object hdmiControlManager = requireContext().getSystemService("hdmi_control");
                    
                //     // 调用 setHdmiCecEnabled 方法
                //     Class<?> clazz = Class.forName("android.hardware.hdmi.HdmiControlManager");
                //      // 打印所有方法名
                //     Log.i(TAG, "===== hdmiControlManager 方法列表 =====");  
                //     for (Method method : clazz.getDeclaredMethods()) {
                //         System.out.println(method.getName());
                //         Log.i(TAG, "Method:"+ method.getName());
                //     }
                //     Log.i(TAG, "====================================");

                //     Method setEnabled = clazz.getMethod("setHdmiCecEnabled", boolean.class);
                //     setEnabled.invoke(hdmiControlManager, isEnabled);
                    
                // } catch (Exception e) {
                //     Log.e(TAG, "Failed to set HDMI CEC via reflection", e);
                // }
                Log.i(TAG, "hdmiCecEnablePref changed ok!");

                return false;
            });
        }

    }
    private void disabeHdmiCecAndSyncStatus(){ 
        //同步到Setting属性和系统属性：
        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_sleep_by_rk",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_RK, "0");
        hdmiCecSleepByRkPref.setChecked(false);
 
        
        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_wakeup_by_rk",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_RK, "0");
        hdmiCecWakeupByRkPref.setChecked(false);
  

        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_poweron_by_rk",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_RK, "0");
        hdmiCecPoweronByRkPref.setChecked(false);
       

        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_poweroff_by_rk",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_RK, "0");
        hdmiCecPoweroffByRkPref.setChecked(false);
    

        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_sleep_by_external",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_SLEEP_BY_EXTERNAL, "0");
        hdmiCecSleepByExternalPref.setChecked(false);
 

        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_wakeup_by_external",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_WAKEUP_BY_EXTERNAL, "0");
        hdmiCecWakeupByExternalPref.setChecked(false);
     

        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_poweron_by_external",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_POWERON_BY_EXTERNAL, "0");
        hdmiCecPoweronByExternalPref.setChecked(false);


        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_poweroff_by_external",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_POWEROFF_BY_EXTERNAL, "0");
        hdmiCecPoweroffByExternalPref.setChecked(false);


        Settings.System.putInt(
            requireContext().getContentResolver(),
            "hdmi_cec_enable",
            0
        );
        SystemProperties.set(PROPERTY_HDMI_CEC_ENABLE, "0");
        hdmiCecEnablePref.setChecked(false);

        // 真正执行关闭CEC功能
        hdmiManager.setHdmiCecEnabled(0);
    }

    private void sendSwitchDeviceOffOnMsg(ITEM_CONTROL control, int status) {
        mEnableDisplayListener = false;
        Message msg = new Message();
        msg.what = MSG_SWITCH_DEVICE_STATUS;
        msg.arg1 = status;
        msg.obj = control;
        mHandler.removeMessages(MSG_SWITCH_DEVICE_STATUS, control);
        mHandler.sendMessageDelayed(msg, SWITCH_DEVICE_DELAY_TIME);
    }

    public static void write2Node(String node, String values) {
        Log.v(TAG, "write " + node + " " + values);
        RandomAccessFile raf = null;
        try {
            raf = new RandomAccessFile(node, "rw");
            raf.writeBytes(values);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != raf) {
                try {
                    raf.close();
                } catch (Exception e) {
                    //e.printStackTrace();
                }
            }
        }
    }

    private void updateResolution(final ITEM_CONTROL control, final int index) {
        showWaitingDialog(R.string.dialog_update_resolution);
        new Thread() {
            @Override
            public void run() {
                if (ITEM_CONTROL.CHANGE_RESOLUTION == control) {
                    mEnableDisplayListener = false;
                    synchronized (mLock) {
                        int display = mSelectDisplayInfo.getDisplayNo();
                        DrmDisplaySetting.updateDisplayInfos();
                        DrmDisplaySetting.updateDisplayModesInfo(mSelectDisplayInfo);
                        String lastResolution = mSelectDisplayInfo.getLastResolution();
                        Log.i(TAG, "display " + display + ", lastResolution=" + lastResolution);
                        if (TextUtils.isEmpty(lastResolution)) {
                            mSelectDisplayInfo.setLastResolution(mSelectDisplayInfo.getCurrentResolution());
                        }
                        int status = DrmDisplaySetting.getCurrentDpyConnState(display);
                        mSelectDisplayInfo.setStatus(status);
                        String[] modes = mSelectDisplayInfo.getOrginModes();
                        Log.v(TAG, "display " + display + ", status=" + status + ", modes=" + modes);
                        if (DPY_STATUS_CONNECTED == status && null != modes && modes.length > 0) {
                            DrmDisplaySetting.setDisplayModeTemp(mSelectDisplayInfo, index);
                            //                            String mode = Arrays.asList(modes).get(index);
                            //                            DrmDisplaySetting.setMode(display, mode);
                            if (USED_OFFON_RESOLUTION) {
                                sendSwitchDeviceOffOnMsg(control, SWITCH_STATUS_OFF_ON);
                            } else {
                                Message message = new Message();
                                message.what = MSG_SHOW_CONFIRM_DIALOG;
                                message.obj = control;
                                mHandler.sendMessageDelayed(message, 300);
                            }
                        } else {
                            Message message = new Message();
                            message.what = MSG_UPDATE_STATUS_UI;
                            message.obj = ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO;
                            mHandler.sendMessage(message);
                        }
                    }
                }
            }
        }.start();
    }

    private void sendUpdateStateMsg(ITEM_CONTROL control, long delayMillis) {
        if (mDestory) {
            return;
        }
        Message msg = new Message();
        msg.what = MSG_UPDATE_STATUS;
        msg.obj = control;
        //增加延迟，保证数据能够拿到
        mHandler.removeMessages(MSG_UPDATE_STATUS, control);
        mHandler.sendMessageDelayed(msg, delayMillis);
    }

    private void getDisplayStatusInfo() {
        synchronized (mLock) {
            if (mDestory) {
                return;
            }
            for (Map.Entry<Integer, DisplayInfo> entry : mDisplayInfoList.entrySet()) {
                int display = entry.getKey();
                int status = DrmDisplaySetting.getCurrentDpyConnState(display);
                DisplayInfo displayInfo = entry.getValue();
                displayInfo.setStatus(status);
                Log.v(TAG, "display " + display + ", status=" + status);
            }
        }
    }

    private void getDisplayResolutionInfo(DisplayInfo displayInfo) {
        synchronized (mLock) {
            if (mDestory) {
                return;
            }
            if (null != displayInfo
                    && DPY_STATUS_CONNECTED == displayInfo.getStatus()) {
                DrmDisplaySetting.updateDisplayModesInfo(displayInfo);
            }
        }
    }

    private void updateStateUI() {
        if (mDestory) {
            return;
        }
        for (Map.Entry<Integer, DisplayInfo> entry : mDisplayInfoList.entrySet()) {
            int display = entry.getKey();
            DisplayInfo displayInfo = entry.getValue();
            Preference cate = findPreference(KEY_PRE_CATE + display);
            if (DPY_STATUS_CONNECTED == displayInfo.getStatus()) {
                cate.setEnabled(true);
            } else {
                cate.setEnabled(false);
            }
        }
    }

    private void showResolutionItemUI(DisplayInfo displayInfo) {
        String[] modes = null == displayInfo.getOrginModes() ? new String[]{} :
                displayInfo.getOrginModes();
        HdmiListPreference resolutionPreference = (HdmiListPreference) findPreference(
                KEY_PRE_RESOLUTION + displayInfo.getDisplayNo());
        String[] enteresModes = new String[]{};
        if (null != modes) {
            enteresModes = new String[modes.length];
            for(int i=0; i < modes.length;i++) {
                String temp = modes[i];
                String[] temps = temp.split("-");
                if (null != temps && temps.length > 1) {
                    temp = temps[0];
                }
                enteresModes[i] = temp;
            }
        }
        resolutionPreference.setEntries(enteresModes);
        resolutionPreference.setEntryValues(modes);
        resolutionPreference.setValue(displayInfo.getCurrentResolution());
        resolutionPreference.showClickDialogItem();
    }

    protected void showConfirmSetModeDialog() {
        //mMainDisplayInfo = getDisplayInfo(0);
        if (mSelectDisplayInfo != null && mResume) {
            Log.v(TAG, "showConfirmSetModeDialog");
            DialogFragment df = ConfirmSetModeDialogFragment.newInstance(mSelectDisplayInfo, new ConfirmSetModeDialogFragment.OnDialogDismissListener() {
                @Override
                public void onDismiss(boolean isok) {
                    Log.i(TAG, "showConfirmSetModeDialog->onDismiss->isok:" + isok);
                    Log.i(TAG, "showConfirmSetModeDialog->onDismiss->mOldResolution:" + mSelectDisplayInfo.getCurrentResolution());
                    synchronized (mLock) {
                        DrmDisplaySetting.confirmSaveDisplayMode(mSelectDisplayInfo, isok);
                        if (!isok) {
                            Preference cate = findPreference(KEY_PRE_CATE + mSelectDisplayInfo.getDisplayNo());
                            cate.setEnabled(false);
                            if (USED_OFFON_RESOLUTION) {
                                showWaitingDialog(R.string.dialog_wait_screen_connect);
                                sendSwitchDeviceOffOnMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, SWITCH_STATUS_OFF_ON);
                            } else {
                                showWaitingDialog(R.string.dialog_update_resolution);
                                sendUpdateStateMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, 1000);
                            }
                        } else if (!USED_OFFON_RESOLUTION) {
                            mEnableDisplayListener = true;
                            updateStateUI();
                            if (mForceRefresh) {
                                mForceRefresh = false;
                                Log.v(TAG, "force refresh once");
                                refreshState();
                            }
                        }
                    }
                }
            });
            df.show(getFragmentManager(), "ConfirmDialog");
        }
    }

    @Override
    public boolean onPreferenceTreeClick(Preference preference) {
        // TODO Auto-generated method stub
        return true;
    }

    @Override
    public boolean onPreferenceClick(Preference preference) {
        String key = preference.getKey();
        Log.i(TAG, "onPreferenceClick " + key);
        if (key.startsWith(KEY_PRE_SCREEN_SCALE)) {
            int display = Integer.parseInt(key.replace(KEY_PRE_SCREEN_SCALE, ""));
            int status = DrmDisplaySetting.getCurrentDpyConnState(display);
            if (DPY_STATUS_CONNECTED == status) {
                Intent screenScaleIntent = new Intent(getActivity(), ScreenScaleActivity.class);
                screenScaleIntent.putExtra(ScreenScaleActivity.EXTRA_DISPLAY, display);
                startActivity(screenScaleIntent);
            } else {
                Preference cate = findPreference(KEY_PRE_CATE + display);
                cate.setEnabled(false);
            }
        } else if (key.startsWith(KEY_PRE_RESOLUTION)) {
            for (Map.Entry<Integer, DisplayInfo> entry : mDisplayInfoList.entrySet()) {
                int display = Integer.parseInt(key.replace(KEY_PRE_RESOLUTION, ""));
                if (display == entry.getKey()) {
                    mSelectDisplayInfo = entry.getValue();
                }
            }
            showWaitingDialog(R.string.dialog_getting_screen_info);
            sendUpdateStateMsg(ITEM_CONTROL.SHOW_RESOLUTION_ITEM, 1000);
        }else if (preference == mAuxScreenVHList) {
            String value = SystemProperties.get("persist.sys.rotation.einit", "0");
            mAuxScreenVHList.setValue(value);
        }       
        return true;
    }

    @Override
    public boolean onPreferenceChange(Preference preference, Object obj) {
        String key = preference.getKey();
        Log.i(TAG, key + " onPreferenceChange:" + obj);
        if (key.startsWith(KEY_PRE_RESOLUTION)) {
            if (null == mSelectDisplayInfo
                    || obj.equals(mSelectDisplayInfo.getCurrentResolution())) {
                return true;
            }
            HdmiListPreference resolutionPreference = (HdmiListPreference) preference;
            int index = resolutionPreference.findIndexOfValue((String) obj);
            Log.i(TAG, "resolutionPreference: index= " + index);
            if (-1 == index) {
                Log.e(TAG, "onPreferenceChange: index=-1 start print");
                CharSequence[] temps = resolutionPreference.getEntryValues();
                if (null == temps) {
                    for (CharSequence temp : temps) {
                        Log.i(TAG, "=======" + temp);
                    }
                } else {
                    Log.e(TAG, "mResolution.getEntryValues() is null, but set " + obj);
                }
                Log.e(TAG, "onPreferenceChange: index=-1 end print");
            }
            preference.getParent().setEnabled(false);
            updateResolution(ITEM_CONTROL.CHANGE_RESOLUTION, index);
        } else if (preference == mSystemRotation) {
            if (KEY_SYSTEM_ROTATION.equals(key)) {
                try {
                    int value = Integer.parseInt((String) obj);
                    android.os.SystemProperties.set("persist.sys.orientation", (String) obj);
                    Log.d(TAG, "freezeRotation~~~value:" + (String) obj);
                    if (value == 0) {
                        mWindowManager.freezeRotation(Surface.ROTATION_0);
                    } else if (value == 90) {
                        mWindowManager.freezeRotation(Surface.ROTATION_90);
                    } else if (value == 180) {
                        mWindowManager.freezeRotation(Surface.ROTATION_180);
                    } else if (value == 270) {
                        mWindowManager.freezeRotation(Surface.ROTATION_270);
                    } else {
                        return true;
                    }
                    //android.os.SystemProperties.set("sys.boot_completed", "1");
                } catch (Exception e) {
                    Log.e(TAG, "freezeRotation error");
                }
            }
        } else if (preference == mAuxScreenVH) {
            mEnableDisplayListener = false;
            showWaitingDialog(R.string.dialog_wait_screen_connect);
            if ((Boolean) obj) {
                SystemProperties.set("persist.sys.rotation.efull", "true");
            } else {
                SystemProperties.set("persist.sys.rotation.efull", "false");
            }
            sendSwitchDeviceOffOnMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, SWITCH_STATUS_OFF_ON);
        } else if (preference == mAuxScreenVHList) {
            mEnableDisplayListener = false;
            showWaitingDialog(R.string.dialog_wait_screen_connect);
            SystemProperties.set("persist.sys.rotation.einit", obj.toString());
            //mDisplayManager.forceScheduleTraversalLocked();
            sendSwitchDeviceOffOnMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, SWITCH_STATUS_OFF_ON);
        }
        return true;
    }

    public static boolean isAvailable() {
        return "true".equals(SystemProperties.get("ro.vendor.hdmi_settings"));
    }

    private void refreshState() {
        Log.v(TAG, "refreshState");
        showWaitingDialog(R.string.dialog_getting_screen_info);
        sendUpdateStateMsg(ITEM_CONTROL.REFRESH_DISPLAY_STATUS_INFO, 1000);
    }

    class DisplayListener implements DisplayManager.DisplayListener {
        @Override
        public void onDisplayAdded(int displayId) {
            Log.v(TAG, "onDisplayAdded displayId=" + displayId);
            if (mEnableDisplayListener) {
//                refreshState();
            }
        }

        @Override
        public void onDisplayChanged(int displayId) {
            Log.v(TAG, "onDisplayChanged displayId=" + displayId);
            //refreshState();
        }

        @Override
        public void onDisplayRemoved(int displayId) {
            Log.v(TAG, "onDisplayRemoved displayId=" + displayId);
            if (mEnableDisplayListener) {
//                refreshState();
            }
        }
    }
}
