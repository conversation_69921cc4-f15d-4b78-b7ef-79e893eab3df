<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright 2023 Rockchip Electronics S.LSI Co. LTD

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android">
    <ListPreference
        android:entries="@array/hdmi_rotation_entries"
        android:entryValues="@array/hdmi_rotation_values"
        android:key="system_rotation"
        android:title="@string/system_rotation" />

    <PreferenceCategory
        android:key="aux_category"
        android:title="@string/screen_title">
        <CheckBoxPreference
            android:key="aux_screen_vh"
            android:title="@string/screen_full_vh" />
        <ListPreference
            android:entries="@array/dual_screen_ver_hor_entries"
            android:entryValues="@array/dual_screen_ver_hor_values"
            android:key="aux_screen_vhlist"
            android:title="@string/screen_rotation" />
    </PreferenceCategory>

    <!-- 新增 HDMI CEC 控制选项 -->
    <PreferenceCategory
        android:key="cec_category"
        android:title="@string/hdmi_cec_title">
        
        <SwitchPreference
            android:key="hdmi_cec_enable"
            android:title="@string/hdmi_cec_switch_title"
            android:summary="@string/hdmi_cec_summary"
            android:defaultValue="true" />

        <SwitchPreference
        android:key="hdmi_cec_sleep_by_rk"
        android:title="@string/hdmi_cec_sleep_by_rk_title"
        android:summary="@string/hdmi_cec_sleep_by_rk_summary"
        android:defaultValue="true" />
        <SwitchPreference
        android:key="hdmi_cec_wakeup_by_rk"
        android:title="@string/hdmi_cec_wakeup_by_rk_title"
        android:summary="@string/hdmi_cec_wakeup_by_rk_summary"
        android:defaultValue="true" />
        <SwitchPreference
        android:key="hdmi_cec_poweron_by_rk"
        android:title="@string/hdmi_cec_poweron_by_rk_title"
        android:summary="@string/hdmi_cec_poweron_by_rk_summary"
        android:defaultValue="false" />
        <SwitchPreference
        android:key="hdmi_cec_poweroff_by_rk"
        android:title="@string/hdmi_cec_poweroff_by_rk_title"
        android:summary="@string/hdmi_cec_poweroff_by_rk_summary"
        android:defaultValue="false" />

        <SwitchPreference
        android:key="hdmi_cec_sleep_by_external"
        android:title="@string/hdmi_cec_sleep_by_external_title"
        android:summary="@string/hdmi_cec_sleep_by_external_summary"
        android:defaultValue="true" />
        <SwitchPreference
        android:key="hdmi_cec_wakeup_by_external"
        android:title="@string/hdmi_cec_wakeup_by_external_title"
        android:summary="@string/hdmi_cec_wakeup_by_external_summary"
        android:defaultValue="true" />
        <SwitchPreference
        android:key="hdmi_cec_poweron_by_external"
        android:title="@string/hdmi_cec_poweron_by_external_title"
        android:summary="@string/hdmi_cec_poweron_by_external_summary"
        android:defaultValue="false" />
        <SwitchPreference
        android:key="hdmi_cec_poweroff_by_external"
        android:title="@string/hdmi_cec_poweroff_by_external_title"
        android:summary="@string/hdmi_cec_poweroff_by_external_summary"
        android:defaultValue="false" />

        <ListPreference
            android:key="hdmi_cec_device_type"
            android:title="@string/hdmi_cec_device_type_title"
            android:entries="@array/hdmi_cec_device_types"
            android:entryValues="@array/hdmi_cec_device_values"
            android:defaultValue="1" />
    </PreferenceCategory>
</PreferenceScreen>
