<?xml version="1.0" encoding="utf-8"?>
<!--
/*
**
** Copyright 2007 The Android Open Source Project
**
** Licensed under the Apache License, Version 2.0 (the "License");
** you may not use this file except in compliance with the License.
** You may obtain a copy of the License at
**
**     http://www.apache.org/licenses/LICENSE-2.0
**
** Unless required by applicable law or agreed to in writing, software
** distributed under the License is distributed on an "AS IS" BASIS,
** WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
** See the License for the specific language governing permissions and
** limitations under the License.
*/
-->
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <!-- Display settings.  The delay in inactivity before the screen is turned off. These are shown in a list dialog. -->
    <string-array name="screen_timeout_entries">
        <item>15 seconds</item>
        <item>30 seconds</item>
        <item>1 minute</item>
        <item>2 minutes</item>
        <item>5 minutes</item>
        <item>10 minutes</item>
        <item>30 minutes</item>
    </string-array>

    <!-- Do not translate. -->
    <string-array name="screen_timeout_values" translatable="false">
        <!-- Do not translate. -->
        <item>15000</item>
        <!-- Do not translate. -->
        <item>30000</item>
        <!-- Do not translate. -->
        <item>60000</item>
        <!-- Do not translate. -->
        <item>120000</item>
        <!-- Do not translate. -->
        <item>300000</item>
        <!-- Do not translate. -->
        <item>600000</item>
        <!-- Do not translate. -->
        <item>1800000</item>
    </string-array>

    <!-- Dark theme scheduling preferences  [CHAR LIMIT=NONE] -->
    <string-array name="dark_ui_scheduler_preference_titles">
        <!-- 0: None -->
        <item>@string/dark_ui_auto_mode_never</item>
        <!-- 1: Custom -->
        <item>@string/dark_ui_auto_mode_custom</item>
        <!-- 2: Auto -->
        <item>@string/dark_ui_auto_mode_auto</item>
    </string-array>

    <!-- Dark theme scheduling preferences  [CHAR LIMIT=NONE] -->
    <string-array name="dark_ui_scheduler_with_bedtime_preference_titles">
        <!-- 0: None -->
        <item>@string/dark_ui_auto_mode_never</item>
        <!-- 1: Custom -->
        <item>@string/dark_ui_auto_mode_custom</item>
        <!-- 2: Auto -->
        <item>@string/dark_ui_auto_mode_auto</item>
        <!-- 3: Bedtime -->
        <item>@string/dark_ui_auto_mode_custom_bedtime</item>
    </string-array>

    <!-- Security settings.  The delay after screen is turned off until device locks.
         These are shown in a list dialog. -->
    <string-array name="lock_after_timeout_entries">
        <item>Immediately</item>
        <item>5 seconds</item>
        <item>15 seconds</item>
        <item>30 seconds</item>
        <item>1 minute</item>
        <item>2 minutes</item>
        <item>5 minutes</item>
        <item>10 minutes</item>
        <item>30 minutes</item>
    </string-array>

    <!-- Do not translate. -->
    <string-array name="lock_after_timeout_values" translatable="false">
        <!-- Do not translate. -->
        <item>0</item>
        <!-- Do not translate. -->
        <item>5000</item>
        <!-- Do not translate. -->
        <item>15000</item>
        <!-- Do not translate. -->
        <item>30000</item>
        <!-- Do not translate. -->
        <item>60000</item>
        <!-- Do not translate. -->
        <item>120000</item>
        <!-- Do not translate. -->
        <item>300000</item>
        <!-- Do not translate. -->
        <item>600000</item>
        <!-- Do not translate. -->
        <item>1800000</item>
    </string-array>

    <!-- Wi-Fi settings -->

    <!-- Match this with the order of NetworkInfo.DetailedState. --> <skip />
    <!-- Wi-Fi settings. The status messages when the network is unknown. -->
    <string-array name="wifi_status">
        <!-- Status message of Wi-Fi when it is idle. -->
        <item></item>
        <!-- Status message of Wi-Fi when it is scanning. -->
        <item>Scanning\u2026</item>
        <!-- Status message of Wi-Fi when it is connecting. -->
        <item>Connecting\u2026</item>
        <!-- Status message of Wi-Fi when it is authenticating. -->
        <item>Authenticating\u2026</item>
        <!-- Status message of Wi-Fi when it is obtaining IP address. -->
        <item>Obtaining IP address\u2026</item>
        <!-- Status message of Wi-Fi when it is connected. -->
        <item>Connected</item>
        <!-- Status message of Wi-Fi when it is suspended. -->
        <item>Suspended</item>
        <!-- Status message of Wi-Fi when it is disconnecting. -->
        <item>Disconnecting\u2026</item>
        <!-- Status message of Wi-Fi when it is disconnected. -->
        <item>Disconnected</item>
        <!-- Status message of Wi-Fi when it is a failure. -->
        <item>Unsuccessful</item>
        <!-- Status message of Wi-Fi when it is blocked. -->
        <item>Blocked</item>
        <!-- Status message of Wi-Fi when connectiong is being verified. -->
        <item>Temporarily avoiding poor connection</item>
    </string-array>

    <!-- Security types for wireless tether -->
    <string-array name="wifi_tether_security">
        <!-- Do not translate. -->
        <item>@string/wifi_security_sae</item>
        <!-- Do not translate. -->
        <item>@string/wifi_security_psk_sae</item>
        <!-- Do not translate. -->
        <item>@string/wifi_security_wpa2</item>
        <!-- Do not translate. -->
        <item>@string/wifi_security_none</item>
    </string-array>

    <!-- Values for security type for wireless tether -->
    <string-array name="wifi_tether_security_values" translatable="false">
        <!-- Do not translate. -->
        <item>3</item>
        <!-- Do not translate. -->
        <item>2</item>
        <!-- Do not translate. -->
        <item>1</item>
        <!-- Do not translate. -->
        <item>0</item>
    </string-array>

    <!-- Match this with the constants in WifiDialog. --> <skip />
    <!-- Wi-Fi settings.  The type of EAP method a Wi-Fi network has. -->
    <string-array name="wifi_eap_method" translatable="false">
        <item>PEAP</item>
        <item>TLS</item>
        <item>TTLS</item>
        <item>PWD</item>
        <item>SIM</item>
        <item>AKA</item>
        <item>AKA\'</item>
    </string-array>

    <!-- Target EAP methods that will have different TTS strings -->
    <!-- Note that this array length should be same as the wifi_eap_method_tts_strings-->
    <string-array name="wifi_eap_method_target_strings" translatable="false">
        <item>AKA\'</item>
    </string-array>

    <!-- TTS strings for the target EAP methods -->
    <!-- Note that this array length should be same as the wifi_eap_method_target_strings-->
    <string-array name="wifi_eap_method_tts_strings" translatable="false">
        <item>AKA prime</item>
    </string-array>

    <!-- Type of EAP method when EAP SIM, AKA, AKA' are not supported -->
    <string-array name="eap_method_without_sim_auth" translatable="false">
        <item>PEAP</item>
        <item>TLS</item>
        <item>TTLS</item>
        <item>PWD</item>
    </string-array>

    <!-- Type of OCSP -->
    <string-array name="eap_ocsp_type" translatable="true">
        <item>Do not verify</item>
        <item>Request certificate status</item>
        <item>Require certificate status</item>
    </string-array>

    <!-- Match this with the integer value of WifiEnterpriseConfig.TlsVersion -->
    <!-- Type of TlsVersion -->
    <string-array name="wifi_eap_tls_ver" translatable="false">
        <item>TLS v1.0</item>
        <item>TLS v1.1</item>
        <item>TLS v1.2</item>
        <item>TLS v1.3</item>
    </string-array>

    <!-- Wi-Fi AP band settings.  Either 2.4GHz or 5GHz prefer. -->
    <!-- Note that adding/removing/moving the items will need wifi settings code change. -->
    <string-array translatable="false" name="wifi_ap_band">
        <item>1</item>
        <item>3</item>
    </string-array>

    <string-array translatable="false" name="wifi_ap_band_summary">
        <item>@string/wifi_ap_choose_2G</item>
        <item>@string/wifi_ap_prefer_5G</item>
    </string-array>

    <!-- Match this with the order of WifiP2pDevice.Status -->
    <!-- Wi-Fi p2p settings device status message -->
    <string-array name="wifi_p2p_status">
        <item>Connected</item>
        <item>Invited</item>
        <item>Unsuccessful</item>
        <item>Available</item>
        <item>Out-of-range</item>
    </string-array>


    <!-- Bluetooth Settings -->

    <!-- Bluetooth developer settings: Titles for maximum number of connected audio devices -->
    <string-array name="bluetooth_max_connected_audio_devices">
        <item>Use System Default: <xliff:g id="default_bluetooth_max_connected_audio_devices">%1$d</xliff:g></item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>

    <!-- Bluetooth developer settings: Values for maximum number of connected audio devices -->
    <string-array translatable="false" name="bluetooth_max_connected_audio_devices_values">
        <item></item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>

    <!-- Bluetooth audio routing settings: Titles for audio routing options -->
    <string-array name="bluetooth_audio_routing_titles">
        <item>Use app default</item>
        <item>Play on hearing device</item>
        <item>Play on phone speaker</item>
    </string-array>

    <!-- Bluetooth audio routing settings: Values for audio routing options -->
    <string-array name="bluetooth_audio_routing_values" translatable="false">
        <!-- Decide automatically -->
        <item>0</item>
        <!-- Play on hearing device -->
        <item>1</item>
        <!-- Play on phone speaker -->
        <item>2</item>
    </string-array>

    <!-- Match this with drawable.wifi_signal. --> <skip />
    <!-- Wi-Fi settings. The signal strength a Wi-Fi network has. -->
    <string-array name="wifi_signal">
        <item>Poor</item>
        <item>Poor</item>
        <item>Fair</item>
        <item>Good</item>
        <item>Excellent</item>
    </string-array>

    <!-- Phase 2 options for PEAP -->
    <string-array name="wifi_peap_phase2_entries">
        <item>MSCHAPV2</item>
        <item>GTC</item>
    </string-array>

    <!-- Type of EAP method when EAP SIM, AKA, AKA' are supported -->
    <string-array name="wifi_peap_phase2_entries_with_sim_auth">
        <item translatable="false">MSCHAPV2</item>
        <item translatable="false">GTC</item>
        <item translatable="false">SIM</item>
        <item translatable="false">AKA</item>
        <item translatable="false">AKA\'</item>
    </string-array>

    <!-- Phase 2 options for TTLS -->
    <string-array name="wifi_ttls_phase2_entries">
        <item>PAP</item>
        <item>MSCHAP</item>
        <item>MSCHAPV2</item>
        <item>GTC</item>
    </string-array>

    <!-- Wi-Fi IP settings. -->
    <!-- Note that adding/removing/moving the items will need wifi settings code change. -->
    <string-array name="wifi_ip_settings">
        <!-- Use DHCP (Dynamic Host Configuration Protocol) for obtaining IP settings [CHAR LIMIT=25] -->
        <item>DHCP</item>
        <!-- Use statically defined IP settings [CHAR LIMIT=25]-->
        <item>Static</item>
    </string-array>

    <!-- Wi-Fi proxy settings. -->
    <!-- Note that adding/removing/moving the items will need wifi settings code change. -->
    <string-array name="wifi_proxy_settings">
        <!-- No HTTP proxy is used for the current wifi network [CHAR LIMIT=25] -->
        <item>None</item>
        <!-- Manual HTTP proxy settings are used for the current wifi network [CHAR LIMIT=25] -->
        <item>Manual</item>
        <!-- Proxy Auto-Config URL that is used for the current wifi network [CHAR LIMIT=25] -->
        <item>Proxy Auto-Config</item>
    </string-array>

    <!-- Authentication Types used in APN editor -->
    <string-array name="apn_auth_entries">
        <item>None</item>
        <item>PAP</item>
        <item>CHAP</item>
        <item>PAP or CHAP</item>
    </string-array>

    <string-array translatable="false" name="apn_auth_values">
        <!-- Do not translate. -->
        <item>0</item>
        <!-- Do not translate. -->
        <item>1</item>
        <!-- Do not translate. -->
        <item>2</item>
        <!-- Do not translate. -->
        <item>3</item>
    </string-array>

    <!-- Authentication Types used in APN editor -->
    <string-array name="apn_protocol_entries">
        <item>IPv4</item>
        <item>IPv6</item>
        <item>IPv4/IPv6</item>
    </string-array>

    <string-array translatable="false" name="apn_protocol_values">
        <!-- Do not translate. -->
        <item>IP</item>
        <!-- Do not translate. -->
        <item>IPV6</item>
        <!-- Do not translate. -->
        <item>IPV4V6</item>
    </string-array>

    <!-- Bearer Info used in APN editor -->
    <string-array name="bearer_entries">
        <item>Unspecified</item>
        <item>LTE</item>
        <item>HSPAP</item>
        <item>HSPA</item>
        <item>HSUPA</item>
        <item>HSDPA</item>
        <item>UMTS</item>
        <item>EDGE</item>
        <item>GPRS</item>
        <item>eHRPD</item>
        <item>EVDO_B</item>
        <item>EVDO_A</item>
        <item>EVDO_0</item>
        <item>1xRTT</item>
        <item>IS95B</item>
        <item>IS95A</item>
        <item>NR</item>
    </string-array>

    <string-array translatable="false" name="bearer_values">
        <!-- Do not translate. -->
        <item>0</item>
        <!-- Do not translate. -->
        <item>14</item>
        <!-- Do not translate. -->
        <item>15</item>
        <!-- Do not translate. -->
        <item>11</item>
        <!-- Do not translate. -->
        <item>10</item>
        <!-- Do not translate. -->
        <item>9</item>
        <!-- Do not translate. -->
        <item>3</item>
        <!-- Do not translate. -->
        <item>2</item>
        <!-- Do not translate. -->
        <item>1</item>
        <!-- Do not translate. -->
        <item>13</item>
        <!-- Do not translate. -->
        <item>12</item>
        <!-- Do not translate. -->
        <item>8</item>
        <!-- Do not translate. -->
        <item>7</item>
        <!-- Do not translate. -->
        <item>6</item>
        <!-- Do not translate. -->
        <item>5</item>
        <!-- Do not translate. -->
        <item>4</item>
        <!-- Do not translate. -->
        <item>20</item>
    </string-array>

    <!-- MVNO Info used in APN editor -->
    <string-array name="mvno_type_entries">
        <item>None</item>
        <item translatable="false">SPN</item>
        <item translatable="false">IMSI</item>
        <item translatable="false">GID</item>
    </string-array>

    <string-array translatable="false" name="mvno_type_values">
        <!-- Do not translate. -->
        <item></item>
        <!-- Do not translate. -->
        <item>spn</item>
        <!-- Do not translate. -->
        <item>imsi</item>
        <!-- Do not translate. -->
        <item>gid</item>
    </string-array>

    <!-- User display names for app ops codes -->
    <string-array name="app_ops_summaries">
        <item>coarse location</item>
        <item>fine location</item>
        <item>GPS</item>
        <item>vibrate</item>
        <item>read contacts</item>
        <item>modify contacts</item>
        <item>read call log</item>
        <item>modify call log</item>
        <item>read calendar</item>
        <item>modify calendar</item>
        <item>wi-fi scan</item>
        <item>notification</item>
        <item>cell scan</item>
        <item>call phone</item>
        <item>read SMS</item>
        <item>write SMS</item>
        <item>receive SMS</item>
        <item>receive emergency SMS</item>
        <item>receive MMS</item>
        <item>receive WAP push</item>
        <item>send SMS</item>
        <item>read ICC SMS</item>
        <item>write ICC SMS</item>
        <item>modify settings</item>
        <item>draw on top</item>
        <item>access notifications</item>
        <item>camera</item>
        <item>record audio</item>
        <item>play audio</item>
        <item>read clipboard</item>
        <item>modify clipboard</item>
        <item>media buttons</item>
        <item>audio focus</item>
        <item>master volume</item>
        <item>voice volume</item>
        <item>ring volume</item>
        <item>media volume</item>
        <item>alarm volume</item>
        <item>notification volume</item>
        <item>bluetooth volume</item>
        <item>keep awake</item>
        <item>monitor location</item>
        <item>monitor high power location</item>
        <item>get usage stats</item>
        <item>mute/unmute microphone</item>
        <item>show toast</item>
        <item>project media</item>
        <item>activate VPN</item>
        <item>write wallpaper</item>
        <item>assist structure</item>
        <item>assist screenshot</item>
        <item>read phone state</item>
        <item>add voicemail</item>
        <item>use sip</item>
        <item>process outgoing call</item>
        <item>fingerprint</item>
        <item>body sensors</item>
        <item>read cell broadcasts</item>
        <item>mock location</item>
        <item>read storage</item>
        <item>write storage</item>
        <item>turn on screen</item>
        <item>get accounts</item>
        <item>run in background</item>
        <item>accessibility volume</item>
    </string-array>

    <!-- User display names for app ops codes -->
    <string-array name="app_ops_labels">
        <item>Location</item>
        <item>Location</item>
        <item>Location</item>
        <item>Vibrate</item>
        <item>Read contacts</item>
        <item>Modify contacts</item>
        <item>Read call log</item>
        <item>Modify call log</item>
        <item>Read calendar</item>
        <item>Modify calendar</item>
        <item>Location</item>
        <item>Post notification</item>
        <item>Location</item>
        <item>Call phone</item>
        <item>Read SMS/MMS</item>
        <item>Write SMS/MMS</item>
        <item>Receive SMS/MMS</item>
        <item>Receive SMS/MMS</item>
        <item>Receive SMS/MMS</item>
        <item>Receive SMS/MMS</item>
        <item>Send SMS/MMS</item>
        <item>Read SMS/MMS</item>
        <item>Write SMS/MMS</item>
        <item>Modify settings</item>
        <item>Draw on top</item>
        <item>Access notifications</item>
        <item>Camera</item>
        <item>Record audio</item>
        <item>Play audio</item>
        <item>Read clipboard</item>
        <item>Modify clipboard</item>
        <item>Media buttons</item>
        <item>Audio focus</item>
        <item>Master volume</item>
        <item>Voice volume</item>
        <item>Ring volume</item>
        <item>Media volume</item>
        <item>Alarm volume</item>
        <item>Notification volume</item>
        <item>Bluetooth volume</item>
        <item>Keep awake</item>
        <item>Location</item>
        <item>Location</item>
        <item>Get usage stats</item>
        <item>Mute/unmute microphone</item>
        <item>Show toast</item>
        <item>Project media</item>
        <item>Activate VPN</item>
        <item>Write wallpaper</item>
        <item>Assist structure</item>
        <item>Assist screenshot</item>
        <item>Read phone state</item>
        <item>Add voicemail</item>
        <item>Use sip</item>
        <item>Process outgoing call</item>
        <item>Fingerprint</item>
        <item>Body sensors</item>
        <item>Read cell broadcasts</item>
        <item>Mock location</item>
        <item>Read storage</item>
        <item>Write storage</item>
        <item>Turn on screen</item>
        <item>Get accounts</item>
        <item>Run in background</item>
        <item>Accessibility volume</item>
    </string-array>

    <!-- Keys for the list of accessibility auto click xml. -->
    <string-array name="accessibility_autoclick_control_selector_keys" translatable="false">
        <item>accessibility_control_autoclick_default</item>
        <item>accessibility_control_autoclick_200ms</item>
        <item>accessibility_control_autoclick_600ms</item>
        <item>accessibility_control_autoclick_1sec</item>
        <item>accessibility_control_autoclick_custom</item>
    </string-array>

    <!-- Values for the list of accessibility auto click, pairs to Keys . -->
    <integer-array name="accessibility_autoclick_selector_values" translatable="false">
        <item>0</item>
        <item>200</item>
        <item>600</item>
        <item>1000</item>
        <item>2000</item>
    </integer-array>

    <!-- Keys for the list of accessibility timeouts xml. -->
    <string-array name="accessibility_timeout_control_selector_keys" translatable="false">
        <item>accessibility_control_timeout_default</item>
        <item>accessibility_control_timeout_10secs</item>
        <item>accessibility_control_timeout_30secs</item>
        <item>accessibility_control_timeout_1min</item>
        <item>accessibility_control_timeout_2mins</item>
    </string-array>

    <!-- Values for the list of accessibility timeouts, pairs to Keys . -->
    <integer-array name="accessibility_timeout_selector_values" translatable="false">
        <item>0</item>
        <item>10000</item>
        <item>30000</item>
        <item>60000</item>
        <item>120000</item>
    </integer-array>

    <!-- Summaries of accessibility timeout, pairs to Values -->
    <string-array name="accessibility_timeout_summaries" translatable="false">
        <item>@string/accessibility_timeout_default</item>
        <item>@string/accessibility_timeout_10secs</item>
        <item>@string/accessibility_timeout_30secs</item>
        <item>@string/accessibility_timeout_1min</item>
        <item>@string/accessibility_timeout_2mins</item>
    </string-array>

    <!-- Titles for the list of long press timeout options. -->
    <string-array name="long_press_timeout_selector_titles">
        <!-- A title for the option for short long-press timeout [CHAR LIMIT=25] -->
        <item>Short</item>
        <!-- A title for the option for medium long-press timeout [CHAR LIMIT=25] -->
        <item>Medium</item>
        <!-- A title for the option for long long-press timeout [CHAR LIMIT=25] -->
        <item>Long</item>
    </string-array>

    <!-- Avoid the gender issue, create the new fork of the string long_press_timeout_selector_titles -->
    <!-- Titles for the list of long press timeout options in preference. -->
    <string-array name="long_press_timeout_selector_list_titles">
        <!-- A title for the option for short long-press timeout [CHAR LIMIT=25] -->
        <item>Short</item>
        <!-- A title for the option for medium long-press timeout [CHAR LIMIT=25] -->
        <item>Medium</item>
        <!-- A title for the option for long long-press timeout [CHAR LIMIT=25] -->
        <item>Long</item>
    </string-array>

    <!-- Values for the list of long press timeout options. -->
    <string-array name="long_press_timeout_selector_values" translatable="false">
        <item>400</item>
        <item>1000</item>
        <item>1500</item>
    </string-array>

    <!-- Titles for captioning typeface preference. [CHAR LIMIT=35] -->
    <string-array name="captioning_typeface_selector_titles">
        <item>Default</item>
        <item>Sans-serif</item>
        <item>Sans-serif condensed</item>
        <item>Sans-serif monospace</item>
        <item>Serif</item>
        <item>Serif monospace</item>
        <item>Casual</item>
        <item>Cursive</item>
        <item>Small capitals</item>
    </string-array>

    <!-- Values for captioning typeface preference. -->
    <string-array name="captioning_typeface_selector_values" translatable="false" >
        <item></item>
        <item>sans-serif</item>
        <item>sans-serif-condensed</item>
        <item>sans-serif-monospace</item>
        <item>serif</item>
        <item>serif-monospace</item>
        <item>casual</item>
        <item>cursive</item>
        <item>sans-serif-smallcaps</item>
    </string-array>

    <!-- Titles for captioning font size preference. [CHAR LIMIT=35] -->
    <string-array name="captioning_font_size_selector_titles">
        <item>Very small</item>
        <item>Small</item>
        <item>Medium</item>
        <item>Large</item>
        <item>Very large</item>
    </string-array>

    <!-- Values for captioning font size preference. -->
    <string-array name="captioning_font_size_selector_values" translatable="false" >
        <item>0.25</item>
        <item>0.5</item>
        <item>1.0</item>
        <item>1.5</item>
        <item>2.0</item>
    </string-array>

    <!-- Titles for captioning character edge type preference. [CHAR LIMIT=35] -->
    <string-array name="captioning_edge_type_selector_titles">
        <item>Default</item>
        <item>None</item>
        <item>Outline</item>
        <item>Drop shadow</item>
        <item>Raised</item>
        <item>Depressed</item>
    </string-array>

    <!-- Values for captioning character edge type preference. -->
    <integer-array name="captioning_edge_type_selector_values" translatable="false" >
        <item>-1</item>
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
    </integer-array>

    <!-- Titles for captioning color preference. -->
    <string-array name="captioning_color_selector_titles" translatable="false" >
        <item>@string/color_unspecified</item>
        <item>@string/color_white</item>
        <item>@string/color_black</item>
        <item>@string/color_red</item>
        <item>@string/color_yellow</item>
        <item>@string/color_green</item>
        <item>@string/color_cyan</item>
        <item>@string/color_blue</item>
        <item>@string/color_magenta</item>
    </string-array>

    <!-- Values for captioning color preference. -->
    <integer-array name="captioning_color_selector_values" translatable="false" >
        <item>0x00FFFFFF</item>
        <item>0xFFFFFFFF</item>
        <item>0xFF000000</item>
        <item>0xFFFF0000</item>
        <item>0xFFFFFF00</item>
        <item>0xFF00FF00</item>
        <item>0xFF00FFFF</item>
        <item>0xFF0000FF</item>
        <item>0xFFFF00FF</item>
        <item>0xFF000055</item>
        <item>0xFF0000AA</item>
        <item>0xFF005500</item>
        <item>0xFF005555</item>
        <item>0xFF0055AA</item>
        <item>0xFF0055FF</item>
        <item>0xFF00AA00</item>
        <item>0xFF00AA55</item>
        <item>0xFF00AAAA</item>
        <item>0xFF00AAFF</item>
        <item>0xFF00FF55</item>
        <item>0xFF00FFAA</item>
        <item>0xFF550000</item>
        <item>0xFF550055</item>
        <item>0xFF5500AA</item>
        <item>0xFF5500FF</item>
        <item>0xFF555500</item>
        <item>0xFF555555</item>
        <item>0xFF5555AA</item>
        <item>0xFF5555FF</item>
        <item>0xFF55AA00</item>
        <item>0xFF55AA55</item>
        <item>0xFF55AAAA</item>
        <item>0xFF55AAFF</item>
        <item>0xFF55FF00</item>
        <item>0xFF55FF55</item>
        <item>0xFF55FFAA</item>
        <item>0xFF55FFFF</item>
        <item>0xFFAA0000</item>
        <item>0xFFAA0055</item>
        <item>0xFFAA00AA</item>
        <item>0xFFAA00FF</item>
        <item>0xFFAA5500</item>
        <item>0xFFAA5555</item>
        <item>0xFFAA55AA</item>
        <item>0xFFAA55FF</item>
        <item>0xFFAAAA00</item>
        <item>0xFFAAAA55</item>
        <item>0xFFAAAAAA</item>
        <item>0xFFAAAAFF</item>
        <item>0xFFAAFF00</item>
        <item>0xFFAAFF55</item>
        <item>0xFFAAFFAA</item>
        <item>0xFFAAFFFF</item>
        <item>0xFFFF0055</item>
        <item>0xFFFF00AA</item>
        <item>0xFFFF5500</item>
        <item>0xFFFF5555</item>
        <item>0xFFFF55AA</item>
        <item>0xFFFF55FF</item>
        <item>0xFFFFAA00</item>
        <item>0xFFFFAA55</item>
        <item>0xFFFFAAAA</item>
        <item>0xFFFFAAFF</item>
        <item>0xFFFFFF55</item>
        <item>0xFFFFFFAA</item>
    </integer-array>

    <!-- Titles for captioning opacity preference. [CHAR LIMIT=35] -->
    <string-array name="captioning_opacity_selector_titles" >
        <item>25%</item>
        <item>50%</item>
        <item>75%</item>
        <item>100%</item>
    </string-array>

    <!-- Values for captioning opacity preference. -->
    <integer-array name="captioning_opacity_selector_values" translatable="false" >
        <item>0x40FFFFFF</item>
        <item>0x80FFFFFF</item>
        <item>0xC0FFFFFF</item>
        <item>0xFFFFFFFF</item>
    </integer-array>

    <!-- Titles for captioning text style preset preference. [CHAR LIMIT=35] -->
    <string-array name="captioning_preset_selector_titles" >
        <item>Set by app</item>
        <item>White on black</item>
        <item>Black on white</item>
        <item>Yellow on black</item>
        <item>Yellow on blue</item>
        <item>Custom</item>
    </string-array>

    <!-- Values for captioning text style preset preference. -->
    <integer-array name="captioning_preset_selector_values" translatable="false" >
        <item>4</item>
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>-1</item>
    </integer-array>

    <!-- Titles for the accessibility button location. [CHAR LIMIT=35] -->
    <string-array name="accessibility_button_location_selector_titles">
        <item>Floating over other apps</item>
        <item>Navigation bar</item>
    </string-array>

    <!-- Values for the accessibility button location. -->
    <!-- Should Keep in sync with Settings.Secure.ACCESSIBILITY_BUTTON_MODE_* -->
    <string-array name="accessibility_button_location_selector_values" translatable="false">
        <!-- Floating over other apps -->
        <item>1</item>
        <!-- Navigation bar -->
        <item>0</item>
    </string-array>

    <!-- Titles for the accessibility button or gesture. [CHAR LIMIT=35] -->
    <string-array name="accessibility_button_gesture_selector_titles">
        <item>Button</item>
        <item>Gesture</item>
    </string-array>

    <!-- Values for the accessibility button or gesture. -->
    <!-- Should Keep in sync with Settings.Secure.ACCESSIBILITY_BUTTON_MODE_* -->
    <string-array name="accessibility_button_gesture_selector_values" translatable="false">
        <!-- Button -->
        <item>1</item>
        <!-- Gesture -->
        <item>2</item>
    </string-array>

    <!-- Titles for the accessibility button size. [CHAR LIMIT=35] -->
    <string-array name="accessibility_button_size_selector_titles">
        <item>Small</item>
        <item>Large</item>
    </string-array>

    <!-- Values for the accessibility button size. -->
    <string-array name="accessibility_button_size_selector_values" translatable="false" >
        <!-- Small -->
        <item>0</item>
        <!-- Large -->
        <item>1</item>
    </string-array>

    <!-- Match this with the constants in VpnProfile. --> <skip />
    <!-- Short names for each VPN type, not really translatable. [CHAR LIMIT=20] -->
    <string-array name="vpn_types" translatable="false">
        <item>PPTP</item>
        <item>L2TP/IPSec PSK</item>
        <item>L2TP/IPSec RSA</item>
        <item>IPSec Xauth PSK</item>
        <item>IPSec Xauth RSA</item>
        <item>IPSec Hybrid RSA</item>
        <item>IKEv2/IPSec MSCHAPv2</item>
        <item>IKEv2/IPSec PSK</item>
        <item>IKEv2/IPSec RSA</item>
    </string-array>

    <!-- VPN proxy settings. -->
    <string-array name="vpn_proxy_settings">
        <!-- No HTTP proxy is used for the current VPN [CHAR LIMIT=25] -->
        <item>None</item>
        <!-- Manual HTTP proxy settings are used for the current VPN [CHAR LIMIT=25] -->
        <item>Manual</item>
    </string-array>

    <!-- Match this with the constants in LegacyVpnInfo. --> <skip />
    <!-- Status for a VPN network. [CHAR LIMIT=100] -->
    <string-array name="vpn_states">
        <!-- Status message when VPN is disconnected. -->
        <item>Disconnected</item>
        <!-- Status message when VPN is initializing. -->
        <item>Initializing\u2026</item>
        <!-- Status message when VPN is connecting. -->
        <item>Connecting\u2026</item>
        <!-- Status message when VPN is connected. -->
        <item>Connected</item>
        <!-- Status message when VPN is timeout. -->
        <item>Timeout</item>
        <!-- Status message when VPN is failed. -->
        <item>Unsuccessful</item>
    </string-array>

    <!-- Values for premium SMS permission selector [CHAR LIMIT=30] -->
    <string-array name="security_settings_premium_sms_values">
        <!-- Ask user before sending to premium SMS short code. -->
        <item>Ask</item>
        <!-- Never allow app to send to premium SMS short code. -->
        <item>Never allow</item>
        <!-- Always allow app to send to premium SMS short code. -->
        <item>Always allow</item>
    </string-array>

    <!-- [CHAR LIMIT=40] Labels for memory states -->
    <string-array name="ram_states">
        <!-- Normal desired memory state. -->
        <item>Normal</item>
        <!-- Moderate memory state, not as good as normal. -->
        <item>Moderate</item>
        <!-- Memory is running low. -->
        <item>Low</item>
        <!-- Memory is critical. -->
        <item>Critical</item>
        <!-- Unknown memory state -->
        <item>\?</item>
    </string-array>

    <!-- Summary for magnification adjustment modes for accessibility -->
    <string-array name="magnification_mode_summaries" translatable="false">
        <item>@string/accessibility_magnification_area_settings_full_screen_summary</item>
        <item>@string/accessibility_magnification_area_settings_window_screen_summary</item>
        <item>@string/accessibility_magnification_area_settings_all_summary</item>
    </string-array>

    <!-- Values for magnification adjustment modes for accessibility -->
    <integer-array name="magnification_mode_values" translatable="false">
        <!-- Full screen -->
        <item>1</item>
        <!-- Window screen -->
        <item>2</item>
        <!-- Full screen and window screen. -->
        <item>3</item>
    </integer-array>

    <!-- Keys for color space adjustment modes for accessibility -->
    <string-array name="daltonizer_mode_keys" translatable="false">
        <item>daltonizer_mode_deuteranomaly</item>
        <item>daltonizer_mode_protanomaly</item>
        <item>daltonizer_mode_tritanomaly</item>
        <item>daltonizer_mode_grayscale</item>
    </string-array>

    <!-- Values for display color space adjustment modes for accessibility -->
    <integer-array name="daltonizer_type_values" translatable="false">
        <item>12</item>
        <item>11</item>
        <item>13</item>
        <item>0</item>
    </integer-array>

    <!-- Array of color for sim color for multi-sim in light mode -->
    <integer-array name="sim_color_light">
        <item>@color/SIM_color_cyan</item>
        <item>@color/SIM_color_blue800</item>
        <item>@color/SIM_color_green800</item>
        <item>@color/SIM_color_purple800</item>
        <item>@color/SIM_color_pink800</item>
        <item>@color/SIM_color_orange</item>
    </integer-array>

    <!-- Array of titles for sim color for multi-sim -->
    <string-array name="color_picker">
        <item>Cyan</item>
        <item>Blue</item>
        <item>Green</item>
        <item>Purple</item>
        <item>Pink</item>
        <item>Orange</item>
    </string-array>

    <!-- Automatic storage management settings. The amount of days for the automatic storage manager
         to retain. These are shown in a list dialog. [CHAR LIMIT=70] -->
    <string-array name="automatic_storage_management_days">
        <item>Over 30 days old</item>
        <item>Over 60 days old</item>
        <item>Over 90 days old</item>
    </string-array>

    <string-array name="automatic_storage_management_days_values" translatable="false">
        <item>30</item>
        <item>60</item>
        <item>90</item>
    </string-array>

    <!-- Options for screensaver "When to start" for devices that do not support screensavers
         while on battery -->
    <string-array name="when_to_start_screensaver_entries_no_battery" translatable="false">
        <item>@string/screensaver_settings_summary_sleep</item>
        <item>@string/screensaver_settings_summary_dock_and_charging</item>
    </string-array>

    <!-- Values for screensaver "When to start" for devices that do not support screensavers
         while on battery -->
    <string-array name="when_to_start_screensaver_values_no_battery" translatable="false">
        <item>while_charging_only</item>
        <item>while_docked_only</item>
    </string-array>

    <string-array name="when_to_start_screensaver_entries" translatable="false">
        <item>@string/screensaver_settings_summary_sleep</item>
        <item>@string/screensaver_settings_summary_dock</item>
        <item>@string/screensaver_settings_summary_either_long</item>
    </string-array>

    <string-array name="when_to_start_screensaver_values" translatable="false">
        <item>while_charging_only</item>
        <item>while_docked_only</item>
        <item>either_charging_or_docked</item>
    </string-array>

    <string-array name="zen_mode_contacts_calls_entries" translatable="false">
        <item>@string/zen_mode_from_anyone</item>
        <item>@string/zen_mode_from_contacts</item>
        <item>@string/zen_mode_from_starred</item>
        <item>@string/zen_mode_none_calls</item>
    </string-array>

    <string-array name="zen_mode_contacts_values" translatable="false">
        <item>zen_mode_from_anyone</item>
        <item>zen_mode_from_contacts</item>
        <item>zen_mode_from_starred</item>
        <item>zen_mode_from_none</item>
    </string-array>

    <!--String arrays for notification swipe direction -->
    <string-array name="swipe_direction_titles">
        <item>@string/swipe_direction_rtl</item>
        <item>@string/swipe_direction_ltr</item>
    </string-array>

    <string-array name="swipe_direction_values">
        <item>1</item>
        <item>0</item>
    </string-array>

    <string-array name="wifi_metered_entries">
        <item>Detect automatically</item>
        <item>Treat as metered</item>
        <item>Treat as unmetered</item>
    </string-array>

    <string-array name="wifi_privacy_entries">
        <item>Use randomized MAC (default)</item>
        <item>Use device MAC</item>
    </string-array>

    <string-array name="wifi_hidden_entries">
        <item>No</item>
        <item>Yes</item>
    </string-array>

    <string-array name="wifi_metered_values" translatable="false">
        <item>0</item>
        <item>1</item>
        <item>2</item>
    </string-array>

    <string-array name="wifi_privacy_values" translatable="false">
        <item>1</item>
        <item>0</item>
    </string-array>

    <!-- Titles for autofill logging level preference. [CHAR LIMIT=50] -->
    <string-array name="autofill_logging_level_entries">
        <item>Off</item>
        <item>Debug</item>
        <item>Verbose</item>
    </string-array>

    <!-- Values for autofill logging level preference. -->
    <string-array name="autofill_logging_level_values" translatable="false" >
        <item>0</item> <!-- AutofillManager.NO_LOGGING -->
        <item>2</item> <!-- AutofillManager.FLAG_ADD_CLIENT_DEBUG -->
        <item>4</item> <!-- AutofillManager.FLAG_ADD_CLIENT_VERBOSE -->
    </string-array>

    <string-array name="enabled_networks_choices" translatable="false">
        <item>@string/network_lte</item>
        <item>@string/network_3G</item>
        <item>@string/network_2G</item>
    </string-array>
    <string-array name="enabled_networks_values" translatable="false">
        <item>"9"</item>
        <item>"0"</item>
        <item>"1"</item>
    </string-array>

    <string-array name="enabled_networks_except_gsm_values" translatable="false">
        <item>"9"</item>
        <item>"0"</item>
    </string-array>

    <string-array name="enabled_networks_except_gsm_3g_values" translatable="false">
        <item>"9"</item>
    </string-array>

    <string-array name="enabled_networks_except_lte_values" translatable="false">
        <item>"0"</item>
        <item>"1"</item>
    </string-array>

    <string-array name="enabled_networks_except_gsm_lte_values" translatable="false">
        <item>"0"</item>
    </string-array>

    <string-array name="enabled_networks_cdma_values" translatable="false">
        <item>"8"</item>
        <item>"4"</item>
        <item>"5"</item>
        <item>"10"</item>
    </string-array>

    <string-array name="enabled_networks_cdma_no_lte_values" translatable="false">
        <item>"4"</item>
        <item>"5"</item>
    </string-array>

    <string-array name="enabled_networks_cdma_only_lte_values" translatable="false">
        <item>"8"</item>
        <item>"10"</item>
    </string-array>

    <string-array name="enabled_networks_tdscdma_values" translatable="false">
        <item>"22"</item>
        <item>"18"</item>
        <item>"1"</item>
    </string-array>

    <string-array name="preferred_network_mode_values_world_mode" translatable="false">
        <item>"10"</item>
        <item>"8"</item>
        <item>"9"</item>
    </string-array>

    <string-array name="cdma_system_select_choices">
        <!-- System select dialog screen, setting option name -->
        <item>Home only</item>
        <!-- Remove the following option "Affiliated Networks" from the option list -->
        <!-- <item>Affiliated Networks</item> -->
        <!-- System select dialog screen, setting option name -->
        <item>Automatic</item>
    </string-array>
    <string-array name="cdma_system_select_values" translatable="false">
        <!-- Do not translate. -->
        <item>"0"</item>
        <!-- Remove the following value "1" which corresponds to "Affiliated Networks" above -->
        <!-- <item>"1"</item>  -->
        <!-- Do not translate. -->
        <item>"2"</item>
    </string-array>

    <!-- The preferred network modes in Mobile network settings -->
    <string-array name="preferred_network_mode_choices">
        <item>GSM/WCDMA preferred</item>
        <item>GSM only</item>
        <item>WCDMA only</item>
        <item>GSM/WCDMA auto</item>
        <item>CDMA/EvDo auto</item>
        <item>CDMA w/o EvDo</item>
        <item>EvDo only</item>
        <item>CDMA/EvDo/GSM/WCDMA</item>
        <item>CDMA + LTE/EvDo</item>
        <item>GSM/WCDMA/LTE</item>
        <item>LTE/CDMA/EvDo/GSM/WCDMA</item>
        <item>LTE</item>
        <item>LTE / WCDMA</item>
        <item>TDSCDMA only</item>
        <item>TDSCDMA/WCDMA</item>
        <item>LTE/TDSCDMA</item>
        <item>TDSCDMA/GSM</item>
        <item>LTE/TDSCDMA/GSM</item>
        <item>TDSCDMA/GSM/WCDMA</item>
        <item>LTE/TDSCDMA/WCDMA</item>
        <item>LTE/TDSCDMA/GSM/WCDMA</item>
        <item>TDSCDMA/CDMA/EVDO/GSM/WCDMA </item>
        <item>LTE/TDSCDMA/CDMA/EVDO/GSM/WCDMA</item>
        <item>NR only</item>
        <item>NR/LTE</item>
        <item>NR/LTE/CDMA/EvDo</item>
        <item>NR/LTE/GSM/WCDMA</item>
        <!-- Global includes NR/LTE/CDMA/EvDo/GSM/WCDMA -->
        <item>Global</item>
        <item>NR/LTE/WCDMA</item>
        <item>NR/LTE/TDSCDMA</item>
        <item>NR/LTE/TDSCDMA/GSM</item>
        <item>NR/LTE/TDSCDMA/WCDMA</item>
        <item>NR/LTE/TDSCDMA/GSM/WCDMA</item>
        <item>NR/LTE/TDSCDMA/CDMA/EvDo/GSM/WCDMA</item>
    </string-array>
    <!-- The preferred network modes RIL constants, in order of the modes above,
         e.g. the choice "GSM/WCDMA preferred" has the corresponding value "0" -->
    <string-array name="preferred_network_mode_values"  translatable="false">
        <item>"0"</item>
        <item>"1"</item>
        <item>"2"</item>
        <item>"3"</item>
        <item>"4"</item>
        <item>"5"</item>
        <item>"6"</item>
        <item>"7"</item>
        <item>"8"</item>
        <item>"9"</item>
        <item>"10"</item>
        <item>"11"</item>
        <item>"12"</item>
        <item>"13"</item>
        <item>"14"</item>
        <item>"15"</item>
        <item>"16"</item>
        <item>"17"</item>
        <item>"18"</item>
        <item>"19"</item>
        <item>"20"</item>
        <item>"21"</item>
        <item>"22"</item>
        <item>"23"</item>
        <item>"24"</item>
        <item>"25"</item>
        <item>"26"</item>
        <item>"27"</item>
        <item>"28"</item>
        <item>"29"</item>
        <item>"30"</item>
        <item>"31"</item>
        <item>"32"</item>
        <item>"33"</item>
    </string-array>

    <!-- Choices for CDMA subscription-->
    <string-array name="cdma_subscription_choices">
        <item>RUIM/SIM</item>
        <item>NV</item>
    </string-array>
    <!-- Values for CDMA subscription-->
    <string-array name="cdma_subscription_values" translatable="false">
        <item>"0"</item>
        <item>"1"</item>
    </string-array>


    <!-- WiFi calling mode array -->
    <string-array name="wifi_calling_mode_summaries" translatable="false">
        <item>@string/wifi_calling_mode_wifi_preferred_summary</item>
        <item>@string/wifi_calling_mode_cellular_preferred_summary</item>
        <item>@string/wifi_calling_mode_wifi_only_summary</item>
    </string-array>

    <!-- WiFi calling mode array without wifi only mode -->
    <string-array name="wifi_calling_mode_summaries_without_wifi_only" translatable="false">
        <item>@string/wifi_calling_mode_wifi_preferred_summary</item>
        <item>@string/wifi_calling_mode_cellular_preferred_summary</item>
    </string-array>

    <!-- Carrier variant of Enhaced 4G LTE Mode title.  [CHAR LIMIT=NONE] -->
    <string-array name="enhanced_4g_lte_mode_title_variant">
        <!-- 0: Default -->
        <item>@string/enhanced_4g_lte_mode_title</item>
        <!-- 1: Verizon -->
        <item>@string/enhanced_4g_lte_mode_title_advanced_calling</item>
        <!-- 2: All carriers who want 4G -->
        <item>@string/enhanced_4g_lte_mode_title_4g_calling</item>
    </string-array>

    <!-- Array of titles palette list for accessibility. -->
    <string-array name="setting_palette_data" translatable="false" >
        <item>@string/color_red</item>
        <item>@string/color_orange</item>
        <item>@string/color_yellow</item>
        <item>@string/color_green</item>
        <item>@string/color_cyan</item>
        <item>@string/color_blue</item>
        <item>@string/color_purple</item>
        <item>@string/color_gray</item>
    </string-array>

    <!-- Values for palette list view preference. -->
    <array name="setting_palette_colors" translatable="false" >
        <item>@color/palette_list_color_red</item>
        <item>@color/palette_list_color_orange</item>
        <item>@color/palette_list_color_yellow</item>
        <item>@color/palette_list_color_green</item>
        <item>@color/palette_list_color_cyan</item>
        <item>@color/palette_list_color_blue</item>
        <item>@color/palette_list_color_purple</item>
        <item>@color/palette_list_color_gray</item>
    </array>

    <!--String arrays for showing the rtt settings options -->
    <string-array name="rtt_setting_mode">
        <!-- 0: Invalid value -->
        <item></item>
        <!-- 1:  Not visible -->
        <item>@string/rtt_settings_no_visible</item>
        <!-- 2:  Visible during call -->
        <item>@string/rtt_settings_visible_during_call</item>
        <!-- 3:  Always visible -->
        <item>@string/rtt_settings_always_visible</item>
    </string-array>

    <!-- All the preference when the open app supports TapPay -->
    <string-array name="nfc_payment_favor">
        <item>Always</item>
        <item>Except when another payment app is open</item>
    </string-array>
    <!-- All the values of the preference when the open app supports TapPay -->
    <string-array name="nfc_payment_favor_values">
        <item>0</item>
        <item>1</item>
    </string-array>

    <!-- Setting entries for timing out to the Dock User when docked. -->
    <string-array name="switch_to_dock_user_when_docked_timeout_entries">
        <item>Never</item>
        <item>After 1 minute</item>
        <item>After 5 minutes</item>
    </string-array>

    <!-- Setting values for timing out to the Dock User when docked. [DO NOT TRANSLATE] -->
    <string-array name="switch_to_dock_user_when_docked_timeout_values" translatable="false">
        <!-- Never -->
        <item>0</item>
        <!-- 1 minute -->
        <item>60000</item>
        <!-- 5 minutes -->
        <item>300000</item>
    </string-array>

    <!-- Developer settings: ingress rate limit entries. [DO NOT TRANSLATE] -->
    <string-array name="ingress_rate_limit_entries">
        <item>@string/ingress_rate_limit_no_limit_entry</item>
        <item>128kbps</item>
        <item>256kbps</item>
        <item>1Mbps</item>
        <item>5Mbps</item>
        <item>15Mbps</item>
    </string-array>

    <!-- Developer settings: ingress rate limit values. [DO NOT TRANSLATE] -->
    <string-array name="ingress_rate_limit_values">
        <item>-1</item> <!-- -1 codes for disabled -->
        <item>16000</item> <!-- 128kbps == 16000B/s -->
        <item>32000</item> <!-- 256kbps == 32000B/s -->
        <item>125000</item> <!-- 1Mbps == 125000B/s -->
        <item>625000</item> <!-- 5Mbps == 625000/s -->
        <item>1875000</item> <!-- 15Mbps == 1875000/s -->
    </string-array>

    <!-- An allowlist which packages won't show summary in battery usage screen.
         [CHAR LIMIT=NONE] -->
    <string-array name="allowlist_hide_summary_in_battery_usage" translatable="false">
    </string-array>

    <!--ethernet mode select -->
    <string-array name="ethernet_mode_location">
        <item>@string/usestatic</item>
        <item>@string/usedhcp</item>
    </string-array>
    <string-array name="ethernet_mode_values">
        <item>StaticIP</item>
        <item>DHCP</item>
    </string-array>

    <!-- A list for all temperature units. [DO NOT TRANSLATE] -->
    <string-array name="temperature_units">
        <item>default</item>
        <item>celsius</item>
        <item>fahrenhe</item>
    </string-array>

    <!-- A list for all days of a week. [DO NOT TRANSLATE] -->
    <string-array name="first_day_of_week">
        <item>default</item>
        <item>sun</item>
        <item>mon</item>
        <item>tue</item>
        <item>wed</item>
        <item>thu</item>
        <item>fri</item>
        <item>sat</item>
    </string-array>

    <!-- Screen flash notification color when activating -->
    <array name="screen_flash_notification_preset_opacity_colors">
        <item>@color/screen_flash_preset_opacity_color_01</item>
        <item>@color/screen_flash_preset_opacity_color_02</item>
        <item>@color/screen_flash_preset_opacity_color_03</item>
        <item>@color/screen_flash_preset_opacity_color_04</item>
        <item>@color/screen_flash_preset_opacity_color_05</item>
        <item>@color/screen_flash_preset_opacity_color_06</item>
        <item>@color/screen_flash_preset_opacity_color_07</item>
        <item>@color/screen_flash_preset_opacity_color_08</item>
        <item>@color/screen_flash_preset_opacity_color_09</item>
        <item>@color/screen_flash_preset_opacity_color_10</item>
        <item>@color/screen_flash_preset_opacity_color_11</item>
        <item>@color/screen_flash_preset_opacity_color_12</item>
    </array>

    <!-- hide 3G option from preferred network type UI -->
    <integer-array name="network_mode_3g_deprecated_carrier_id" translatable="false">
    </integer-array>

    <!-- =======================rk code========== -->
    <!-- ram extension -->
    <string-array name="ram_extension_entries">
        <item>none</item>
        <item>256 M</item>
        <item>512 M</item>
        <item>1 GB</item>
        <item>2 GB</item>
        <item>4 GB</item>
    </string-array>
    <string-array name="ram_extension_values" translatable="false">
        <item>none</item>
        <item>256</item>
        <item>512</item>
        <item>1024</item>
        <item>2048</item>
        <item>4096</item>
    </string-array>

    <!-- screenshot setting-->
    <string-array name="screenshot_delay_values">
        <item>15</item>
        <item>30</item>
        <item>60</item>
        <item>120</item>
        <item>180</item>
        <item>240</item>
        <item>300</item>
    </string-array>
    <string-array name="screenshot_delay_entries">
        <item>15 seconds later</item>
        <item>30 seconds later</item>
        <item>1 minute later</item>
        <item>2 minute later</item>
        <item>3 minute later</item>
        <item>4 minute later</item>
        <item>5 minute later</item>
    </string-array>

    <!-- HDMI -->
    <string-array name="hdmi_resolution_entries">
        <item>1920x1080p-50Hz </item>
        <item>1920x1080p-60Hz</item>
        <item>1280x720p-50Hz</item>
        <item>1280x720p-60Hz</item>
        <item>720x576p-50Hz </item>
        <item>720x480p-60Hz </item>
    </string-array>
    <string-array name="hdmi_resolution_values">
        <item>1920x1080p-50\n</item>
        <item>1920x1080p-60\n</item>
        <item>1280x720p-50\n</item>
        <item>1280x720p-60\n</item>
        <item>720x576p-50\n</item>
        <item>720x480p-60\n</item>
    </string-array>
    <string-array name="hdmi_rotation_entries">
        <item>0</item>
        <item>90</item>
        <item>180</item>
        <item>270</item>
    </string-array>
    <string-array name="hdmi_rotation_values">
        <item>0</item>
        <item>90</item>
        <item>180</item>
        <item>270</item>
    </string-array>
<!-- HDMI CEC -->
    <string-array name="hdmi_cec_device_types">
        <item>电视</item>
        <item>录像机</item>
        <item>音响系统</item>
        <item>播放器</item>
    </string-array>

    <string-array name="hdmi_cec_device_values">
        <item>0</item>  <!-- TV -->
        <item>1</item>  <!-- Recorder -->
        <item>2</item>  <!-- Audio System -->
        <item>3</item>  <!-- Player -->
    </string-array>

    <string-array name="dual_screen_ver_hor_entries">
        <item>0</item>
        <item>90</item>
        <item>180</item>
        <item>270</item>
    </string-array>
    <string-array name="dual_screen_ver_hor_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
    </string-array>
    <!-- ======================================== -->
    <!-- The following 2 arrays are for battery tips card. Please keep them the same size. -->
    <string-array name="battery_tips_card_icons" translatable="false">
        <item>ic_battery_tips_lightbulb</item>
        <item>ic_battery_tips_warning_icon</item>
    </string-array>

    <string-array name="battery_tips_card_colors" translatable="false">
        <item>color_accent_selector</item>
        <item>color_battery_anomaly_yellow_selector</item>
    </string-array>

    <!-- The following 4 arrays are for power anomaly tips card. Please keep them the same size. -->
    <string-array name="power_anomaly_title_ids" translatable="false">
        <item>battery_tips_settings_summary_brightness</item>
        <item>battery_tips_settings_summary_screen_timeout</item>
        <item>battery_tips_apps_summary_always_high</item>
        <item>battery_tips_apps_summary_higher_than_usual</item>
        <item>battery_tips_apps_summary_always_high_in_background</item>
        <item>battery_tips_apps_summary_higher_than_usual_in_background</item>
        <item>battery_tips_apps_summary_always_high_in_foreground</item>
        <item>battery_tips_apps_summary_higher_than_usual_in_foreground</item>
    </string-array>

    <string-array name="power_anomaly_main_btn_strings" translatable="false">
        <item>@string/battery_tips_card_action_button</item>
        <item>@string/battery_tips_card_action_button</item>
        <item>@string/battery_tips_card_action_button_check</item>
        <item>@string/battery_tips_card_action_button_check</item>
        <item>@string/battery_tips_card_action_button_check</item>
        <item>@string/battery_tips_card_action_button_check</item>
        <item>@string/battery_tips_card_action_button_check</item>
        <item>@string/battery_tips_card_action_button_check</item>
    </string-array>

    <string-array name="power_anomaly_dismiss_btn_strings" translatable="false">
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
        <item>@string/battery_tips_card_dismiss_button</item>
    </string-array>

    <string-array name="power_anomaly_hint_messages" translatable="false">
        <item></item>
        <item></item>
        <item>@string/battery_app_item_hint</item>
        <item>@string/battery_app_item_hint</item>
        <item>@string/battery_app_item_hint_in_bg</item>
        <item>@string/battery_app_item_hint_in_bg</item>
        <item>@string/battery_app_item_hint_in_fg</item>
        <item>@string/battery_app_item_hint_in_fg</item>
    </string-array>

</resources>
