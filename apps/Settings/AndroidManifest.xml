<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools"
          package="com.android.settings"
          coreApp="true"
          android:sharedUserId="android.uid.system">

    <original-package android:name="com.android.settings"/>

    <!-- Permissions for reading or writing battery-related data. -->
    <permission
        android:name="com.android.settings.BATTERY_DATA"
        android:protectionLevel="signature|privileged"/>

    <uses-permission android:name="android.permission.REQUEST_NETWORK_SCORES" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION" />
    <uses-permission android:name="android.permission.CONTROL_UI_TRACING" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.BLUETOOTH_PRIVILEGED" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.HARDWARE_TEST" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS_PRIVILEGED" />
    <uses-permission android:name="android.permission.QUERY_AUDIO_STATE" />
    <uses-permission android:name="android.permission.MASTER_CLEAR" />
    <uses-permission android:name="com.google.android.googleapps.permission.GOOGLE_AUTH" />
    <uses-permission android:name="android.permission.ACCESS_DOWNLOAD_MANAGER" />
    <uses-permission android:name="android.permission.READ_CONTACTS" />
    <uses-permission android:name="android.permission.WRITE_CONTACTS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.LOCAL_MAC_ADDRESS" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="com.android.certinstaller.INSTALL_AS_USER" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.TETHER_PRIVILEGED" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CLEAR_APP_USER_DATA" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.MODIFY_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission android:name="android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.WRITE_APN_SETTINGS"/>
    <uses-permission android:name="android.permission.ACCESS_CHECKIN_PROPERTIES"/>
    <uses-permission android:name="android.permission.READ_USER_DICTIONARY"/>
    <uses-permission android:name="android.permission.WRITE_USER_DICTIONARY"/>
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES"/>
    <uses-permission android:name="android.permission.PACKAGE_USAGE_STATS"/>
    <uses-permission android:name="android.permission.BATTERY_STATS"/>
    <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS" />
    <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MOVE_PACKAGE" />
    <uses-permission android:name="android.permission.USE_CREDENTIALS" />
    <uses-permission android:name="android.permission.BACKUP" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_DEVICE_CONFIG" />
    <uses-permission android:name="android.permission.STATUS_BAR" />
    <uses-permission android:name="android.permission.MANAGE_USB" />
    <uses-permission android:name="android.permission.MANAGE_DEBUGGING" />
    <uses-permission android:name="android.permission.SET_POINTER_SPEED" />
    <uses-permission android:name="android.permission.SET_KEYBOARD_LAYOUT" />
    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS_FULL" />
    <uses-permission android:name="android.permission.COPY_PROTECTED_DATA" />
    <uses-permission android:name="android.permission.MANAGE_USERS" />
    <uses-permission android:name="android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS" />
    <uses-permission android:name="android.permission.READ_PROFILE" />
    <uses-permission android:name="android.permission.CONFIGURE_WIFI_DISPLAY" />
    <uses-permission android:name="android.permission.CONFIGURE_DISPLAY_COLOR_MODE" />
    <uses-permission android:name="android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS" />
    <uses-permission android:name="android.permission.SUGGEST_MANUAL_TIME_AND_ZONE" />
    <uses-permission android:name="android.permission.ACCESS_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_ADMINS" />
    <uses-permission android:name="android.permission.READ_SEARCH_INDEXABLES" />
    <uses-permission android:name="android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE" />
    <uses-permission android:name="android.permission.BIND_REMOTE_LOCKSCREEN_VALIDATION_SERVICE" />
    <uses-permission android:name="android.permission.OEM_UNLOCK_STATE" />
    <uses-permission android:name="android.permission.MANAGE_USER_OEM_UNLOCK_STATE" />
    <uses-permission android:name="android.permission.OVERRIDE_WIFI_CONFIG" />
    <uses-permission android:name="android.permission.RESTART_WIFI_SUBSYSTEM" />
    <uses-permission android:name="android.permission.MANAGE_FINGERPRINT" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC_INTERNAL" />
    <uses-permission android:name="android.permission.USER_ACTIVITY" />
    <uses-permission android:name="android.permission.CHANGE_APP_IDLE_STATE" />
    <uses-permission android:name="android.permission.PEERS_MAC_ADDRESS"/>
    <uses-permission android:name="android.permission.MANAGE_NOTIFICATIONS"/>
    <uses-permission android:name="android.permission.DELETE_PACKAGES"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES" />
    <uses-permission android:name="android.permission.MANAGE_APP_OPS_RESTRICTIONS"/>
    <uses-permission android:name="android.permission.MANAGE_APP_OPS_MODES" />
    <uses-permission android:name="android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS"/>
    <uses-permission android:name="android.permission.READ_PRINT_SERVICES" />
    <uses-permission android:name="android.permission.NETWORK_SETTINGS" />
    <uses-permission android:name="android.permission.TEST_BLACKLISTED_PASSWORD" />
    <uses-permission android:name="android.permission.USE_RESERVED_DISK" />
    <uses-permission android:name="android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.MEDIA_CONTENT_CONTROL" />
    <uses-permission android:name="android.permission.INSTALL_DYNAMIC_SYSTEM" />
    <uses-permission android:name="android.permission.BIND_CELL_BROADCAST_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.READ_DREAM_STATE" />
    <uses-permission android:name="android.permission.READ_DREAM_SUPPRESSION" />
    <uses-permission android:name="android.permission.MANAGE_APP_HIBERNATION" />
    <uses-permission android:name="android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK" />
    <uses-permission android:name="android.permission.ALLOW_PLACE_IN_MULTI_PANE_SETTINGS" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.READ_APP_SPECIFIC_LOCALES" />
    <uses-permission android:name="android.permission.QUERY_ADMIN_POLICY" />
    <uses-permission android:name="android.permission.READ_SAFETY_CENTER_STATUS" />
    <uses-permission android:name="android.permission.SEND_SAFETY_CENTER_UPDATE" />
    <uses-permission android:name="android.permission.START_VIEW_APP_FEATURES" />
    <uses-permission android:name="android.permission.LIST_ENABLED_CREDENTIAL_PROVIDERS" />
    <uses-permission android:name="android.permission.CUSTOMIZE_SYSTEM_UI" />
    <uses-permission android:name="android.permission.REMAP_MODIFIER_KEYS" />
    <uses-permission android:name="android.permission.ACCESS_GPU_SERVICE" />
    <uses-permission android:name="android.permission.HDMI_CEC"/>
    <uses-permission android:name="android.permission.REFLECTION"/>
    
    <application
            android:name=".SettingsApplication"
            android:label="@string/settings_label"
            android:icon="@drawable/ic_launcher_settings"
            android:theme="@style/Theme.Settings"
            android:hardwareAccelerated="true"
            android:requiredForAllUsers="true"
            android:supportsRtl="true"
            android:backupAgent="com.android.settings.backup.SettingsBackupHelper"
            android:usesCleartextTraffic="true"
            android:defaultToDeviceProtectedStorage="true"
            android:directBootAware="true"
            android:appComponentFactory="androidx.core.app.CoreComponentFactory"
            android:gwpAsanMode="always"
            android:enableOnBackInvokedCallback="true">

        <uses-library android:name="org.apache.http.legacy" />

        <property
            android:name="android.window.PROPERTY_ACTIVITY_EMBEDDING_SPLITS_ENABLED"
            android:value="true" />

        <!-- Settings -->
        <activity android:name=".homepage.SettingsHomepageActivity"
                  android:label="@string/settings_label_launcher"
                  android:theme="@style/Theme.Settings.Home"
                  android:taskAffinity="com.android.settings.root"
                  android:exported="true"
                  android:configChanges="orientation|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <!-- Activity for launching deep link page in 2-pane. -->
        <activity android:name=".homepage.DeepLinkHomepageActivity"
                  android:label="@string/settings_label_launcher"
                  android:theme="@style/Theme.Settings.Home"
                  android:launchMode="singleTask"
                  android:exported="true"
                  android:enabled="false"
                  android:configChanges="orientation|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize"
                  android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                  android:permission="android.permission.LAUNCH_MULTI_PANE_SETTINGS_DEEP_LINK">
            <intent-filter>
                <action android:name="android.settings.SETTINGS_EMBED_DEEP_LINK_ACTIVITY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name=".homepage.DeepLinkHomepageActivityInternal"
                  android:label="@string/settings_label_launcher"
                  android:theme="@style/Theme.Settings.Home.NoAnimation"
                  android:taskAffinity=""
                  android:launchMode="singleTask"
                  android:exported="false"
                  android:excludeFromRecents="true"
                  android:configChanges="orientation|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize">
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <!-- Alias for launcher activity only, as this belongs to each profile. -->
        <activity-alias android:name="Settings"
                android:label="@string/settings_label_launcher"
                android:taskAffinity="com.android.settings.root"
                android:exported="true"
                android:targetActivity=".homepage.SettingsHomepageActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <meta-data android:name="android.app.shortcuts" android:resource="@xml/shortcuts"/>
        </activity-alias>

        <receiver android:name=".SettingsInitialize"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.USER_INITIALIZE"/>
                <action android:name="android.intent.action.PRE_BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <activity android:name=".SubSettings"
                  android:exported="false"
                  android:theme="@style/Theme.SubSettings"
                  android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                  android:taskAffinity="com.android.settings.root" />

        <activity android:name=".Settings$CreateShortcutActivity"
                  android:exported="true"
                  android:label="@string/settings_shortcut">
            <intent-filter>
                <action android:name="android.intent.action.CREATE_SHORTCUT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.shortcut.CreateShortcut" />
        </activity>

        <!-- Wireless Controls -->
        <activity
            android:name=".Settings$NetworkDashboardActivity"
            android:label="@string/network_dashboard_title"
            android:exported="true"
            android:icon="@drawable/ic_homepage_network">
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIRELESS_SETTINGS" />
                <action android:name="android.settings.AIRPLANE_MODE_SETTINGS" />
                <action android:name="com.android.settings.sim.SIM_SUB_INFO_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.network.NetworkDashboardFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name=".Settings$MobileNetworkActivity"
            android:label="@string/network_settings_title"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:launchMode="singleTop"
            android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.telephony.MobileNetworkSettings"/>
            <intent-filter android:priority="1">
                <!-- Displays the MobileNetworkActivity and opt-in dialog for capability discovery. -->
                <!-- Please sync with a list created within MobileNetworkIntentConverter.java -->
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.telephony.ims.action.SHOW_CAPABILITY_DISCOVERY_OPT_IN" />
                <action android:name="android.settings.NETWORK_OPERATOR_SETTINGS" />
                <action android:name="android.settings.DATA_ROAMING_SETTINGS" />
                <action android:name="android.settings.MMS_MESSAGE_SETTING" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".Settings$MobileNetworkListActivity"
                  android:exported="true"
                  android:label="@string/network_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MOBILE_NETWORK_LIST" />
                <action android:name="android.settings.MANAGE_ALL_SIM_PROFILES_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.MobileNetworkListFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name=".Settings$ConnectedDeviceDashboardActivity"
            android:label="@string/connected_devices_dashboard_title"
            android:exported="true"
            android:icon="@drawable/ic_homepage_connected_device">
            <intent-filter android:priority="1">
                <action android:name="android.settings.BLUETOOTH_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.connecteddevice.ConnectedDeviceDashboardFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name="AirplaneModeVoiceActivity"
                android:label="@string/wireless_networks_settings_title"
                android:theme="@*android:style/Theme.DeviceDefault.Light.Voice"
                android:exported="true">
            <intent-filter>
                <action android:name="android.settings.VOICE_CONTROL_AIRPLANE_MODE" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE" />
            </intent-filter>
        </activity>

        <activity android:name=".search.SearchResultTrampoline"
                  android:theme="@android:style/Theme.NoDisplay"
                  android:excludeFromRecents="true"
                  android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                  android:exported="true">
            <intent-filter>
                <action android:name="com.android.settings.SEARCH_RESULT_TRAMPOLINE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <receiver android:name=".search.SearchStateReceiver"
                  android:exported="true"
                  android:enabled="false"
                  android:permission="android.permission.READ_SEARCH_INDEXABLES">
            <intent-filter>
                <action android:name="com.android.settings.SEARCH_START"/>
                <action android:name="com.android.settings.SEARCH_EXIT"/>
            </intent-filter>
        </receiver>

        <activity
            android:name="Settings$MemtagPageActivity"
            android:label="@string/memtag_title"
            android:icon="@drawable/ic_homepage_security"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ADVANCED_MEMORY_PROTECTION_SETTINGS"/>
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.security.MemtagPage"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <activity
            android:name="Settings$WifiSettingsActivity"
            android:label="@string/wifi_settings"
            android:icon="@drawable/ic_homepage_network"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIFI_SETTINGS"/>
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.NetworkProviderSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias
            android:name="Settings$WifiSettings2Activity"
            android:targetActivity="Settings$WifiSettingsActivity"
            android:label="@string/wifi_settings"
            android:icon="@drawable/ic_homepage_network"
            android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.NetworkProviderSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity-alias>

        <activity
            android:name="Settings$NetworkProviderSettingsActivity"
            android:label="@string/provider_internet_settings"
            android:icon="@drawable/ic_homepage_network"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NETWORK_PROVIDER_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="com.android.settings.SHORTCUT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.NetworkProviderSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <activity
            android:name="Settings$NetworkSelectActivity"
            android:label="@string/choose_network_title"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|screenLayout|smallestScreenSize">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.network.telephony.NetworkSelectSettings" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$WifiDetailsSettingsActivity"
            android:label="@string/wifi_details_title"
            android:icon="@drawable/ic_homepage_network"
            android:exported="true"
            android:permission="android.permission.CHANGE_WIFI_STATE"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <!-- The intent action is only public to OEM, because a special library is required. -->
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIFI_DETAILS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data
                android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.details.WifiNetworkDetailsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name=".wifi.WifiPickerActivity"
            android:permission="android.permission.CHANGE_WIFI_STATE"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.net.wifi.PICK_WIFI_NETWORK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".Settings$AdaptiveBrightnessActivity"
            android:label="@string/auto_brightness_title"
            android:exported="true">
            <intent-filter>
                <action android:name="android.settings.ADAPTIVE_BRIGHTNESS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.AutoBrightnessSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_display"/>
        </activity>

        <activity
            android:name="Settings$ConfigureWifiSettingsActivity"
            android:label="@string/wifi_configure_settings_preference_title"
            android:icon="@drawable/ic_settings_wireless"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIFI_IP_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.ConfigureWifiSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$SavedAccessPointsSettingsActivity"
            android:label="@string/wifi_saved_access_points_label"
            android:icon="@drawable/ic_settings_wireless"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIFI_SAVED_NETWORK_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.savedaccesspoints2.SavedAccessPointsWifiSettings2" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".Settings$WifiInfoActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.DEVELOPMENT_PREFERENCE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.wifi.WifiInfo" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity android:name=".wifi.WifiConfigInfo"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.DEVELOPMENT_PREFERENCE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".Settings$WifiAPITestActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.DEVELOPMENT_PREFERENCE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.wifi.WifiAPITest" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity android:name=".wifi.WifiStatusTest"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.DEVELOPMENT_PREFERENCE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".wifi.WifiNoInternetDialog"
                  android:clearTaskOnLaunch="true"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:permission="android.permission.NETWORK_STACK"
                  android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight">
            <!-- TODO: Consider removing below two intent filters.
                 It seems like below two intent filters can be removed because when the notification
                 is clicked, this activity will be launched anyway. -->
            <intent-filter>
                <action android:name="android.net.action.PROMPT_UNVALIDATED" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.net.action.PROMPT_LOST_VALIDATION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name="Settings$ApnSettingsActivity"
                android:label="@string/apn_settings"
                android:exported="true"
                android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APN_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.apn.ApnSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias
            android:name="Settings$BluetoothSettingsActivity"
            android:label="@string/devices_title"
            android:targetActivity=".Settings$ConnectedDeviceDashboardActivity"
            android:exported="true">
            <intent-filter android:priority="10">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.connecteddevice.ConnectedDeviceDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity-alias>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name=".bluetooth.BluetoothSettings"
                        android:label="@string/devices_title"
                        android:targetActivity="Settings$BluetoothSettingsActivity"
                        android:exported="true"
                        android:clearTaskOnLaunch="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.connecteddevice.ConnectedDeviceDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity-alias>

        <activity android:name="Settings$AssistGestureSettingsActivity"
            android:label="@string/assist_gesture_title"
            android:exported="true"
            android:icon="@drawable/ic_settings_gestures">
            <intent-filter>
                <action android:name="android.settings.ASSIST_GESTURE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.gestures.AssistGestureSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name="Settings$FaceSettingsActivity"
            android:label="@string/security_settings_face_preference_title"
            android:exported="true"
            android:theme="@style/Theme.Settings.NoActionBar"
            android:icon="@drawable/ic_face_header">
            <intent-filter>
                <action android:name="android.settings.FACE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.biometrics.face.FaceSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity android:name="Settings$FaceSettingsInternalActivity"
                  android:label="@string/security_settings_face_preference_title"
                  android:exported="false"
                  android:theme="@style/Theme.Settings.NoActionBar"
                  android:icon="@drawable/ic_face_header"
                  android:taskAffinity="com.android.settings.root">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.biometrics.face.FaceSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity android:name="Settings$FingerprintSettingsActivity"
                  android:label="@string/security_settings_fingerprint_preference_title"
                  android:exported="true"
                  android:icon="@drawable/ic_fingerprint_header">
            <intent-filter>
                <action android:name="android.settings.FINGERPRINT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.biometrics.fingerprint.FingerprintSettings$FingerprintSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity android:name="Settings$CombinedBiometricSettingsActivity"
                  android:label="@string/security_settings_biometric_preference_title"
                  android:exported="false"
                  android:taskAffinity="com.android.settings.root">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.biometrics.combination.CombinedBiometricSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity android:name="Settings$CombinedBiometricProfileSettingsActivity"
                  android:label="@string/security_settings_work_biometric_preference_title"
                  android:exported="false">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.biometrics.combination.CombinedBiometricProfileSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity android:name=".bluetooth.DevicePickerActivity"
                android:label="@string/device_picker"
                android:configChanges="orientation|keyboardHidden|screenSize"
                android:exported="true"
                android:clearTaskOnLaunch="true">
            <intent-filter>
                <action android:name="android.bluetooth.devicepicker.action.LAUNCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service android:name=".wifi.tether.TetherService"
            android:exported="true"
            android:permission="android.permission.TETHER_PRIVILEGED" />

        <activity android:name=".network.TetherProvisioningActivity"
            android:exported="true"
            android:permission="android.permission.TETHER_PRIVILEGED"
            android:excludeFromRecents="true"
            android:theme="@style/Theme.ProvisioningActivity">
            <intent-filter android:priority="1">
                <action android:name="android.settings.TETHER_PROVISIONING_UI" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".network.TetherProvisioningCarrierDialogActivity"
            android:exported="true"
            android:permission="android.permission.TETHER_PRIVILEGED"
            android:excludeFromRecents="true"
            android:theme="@*android:style/Theme.DeviceDefault.Settings.Dialog.NoActionBar">
            <intent-filter android:priority="1">
                <action android:name="android.settings.TETHER_UNSUPPORTED_CARRIER_UI" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".network.telephony.ToggleSubscriptionDialogActivity"
                  android:exported="false"
                  android:permission="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"
                  android:theme="@style/Theme.AlertDialog.SimConfirmDialog"/>

        <activity android:name=".network.telephony.DeleteEuiccSubscriptionDialogActivity"
                  android:exported="false"
                  android:permission="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"
                  android:theme="@style/Theme.AlertDialog.SimConfirmDialog"/>

        <activity
            android:name="Settings$TetherSettingsActivity"
            android:label="@string/tether_settings_title_all"
            android:exported="true"
            android:icon="@drawable/ic_homepage_network">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.settings.TETHER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.network.tether.TetherSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity
            android:name="Settings$WifiTetherSettingsActivity"
            android:label="@string/wifi_hotspot_checkbox_text"
            android:exported="true"
            android:icon="@drawable/ic_homepage_wifi_tethering">
            <intent-filter>
                <action android:name="com.android.settings.WIFI_TETHER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="4">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.tether.WifiTetherSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>


        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name=".TetherSettings"
                  android:label="@string/tether_settings_title_all"
                  android:clearTaskOnLaunch="true"
                  android:exported="true"
                  android:targetActivity="Settings$TetherSettingsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.network.tether.TetherSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity-alias>

        <activity android:name="Settings$WifiP2pSettingsActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.p2p.WifiP2pSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity
            android:name="Settings$VpnSettingsActivity"
            android:label="@string/vpn_settings_title"
            android:exported="true"
            android:icon="@drawable/ic_homepage_vpn">
            <intent-filter android:priority="1">
                <action android:name="android.settings.VPN_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.net.vpn.SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="5">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.vpn2.VpnSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$DataSaverSummaryActivity"
            android:exported="true"
            android:label="@string/data_saver_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.DATA_SAVER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.datausage.DataSaverSummary" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity
            android:name="Settings$LongBackgroundTasksActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/long_background_tasks_label">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_APP_LONG_RUNNING_JOBS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$LongBackgroundTasksAppActivity"
            android:exported="true"
            android:label="@string/long_background_tasks_label">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_APP_LONG_RUNNING_JOBS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.LongBackgroundTasksDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$DateTimeSettingsActivity"
            android:label="@string/date_and_time"
            android:exported="true"
            android:icon="@drawable/ic_settings_date_time">
            <intent-filter android:priority="1">
                <action android:name="android.settings.DATE_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.intent.action.QUICK_CLOCK" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.datetime.DateTimeSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$LocalePickerActivity"
            android:label="@string/language_picker_title"
            android:exported="true"
            android:icon="@drawable/ic_settings_language">
            <intent-filter android:priority="1">
                <action android:name="android.settings.LOCALE_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.localepicker.LocaleListEditor" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".localepicker.LocalePickerWithRegionActivity"
                  android:excludeFromRecents="true"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:exported="false"
                  android:theme="@style/Theme.LocalePickerWithRegionActivity">
        </activity>

        <activity
            android:name=".localepicker.AppLocalePickerActivity"
            android:label="@string/app_locale_picker_title"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.settings.APP_LOCALE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </activity>

        <activity
            android:name=".Settings$LanguageAndInputSettingsActivity"
            android:label="@string/language_settings"
            android:exported="true"
            android:icon="@drawable/ic_settings_language">
            <intent-filter>
                <action android:name="android.settings.LANGUAGE_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.VOICE_LAUNCH"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.language.LanguageAndInputSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <activity
            android:name=".Settings$LanguageSettingsActivity"
            android:label="@string/languages_settings"
            android:exported="true"
            android:icon="@drawable/ic_settings_languages">
            <intent-filter>
                <action android:name="android.settings.LANGUAGE_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.VOICE_LAUNCH"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.language.LanguageSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <activity
            android:name=".Settings$NavigationModeSettingsActivity"
            android:label="@string/system_navigation_title"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.settings.NAVIGATION_MODE_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.gestures.SystemNavigationGestureSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true"/>
        </activity>

        <activity
            android:name=".Settings$RegionalPreferencesActivity"
            android:label="@string/regional_preferences_title"
            android:exported="true" >
            <intent-filter android:priority="1">
                <action android:name="android.settings.REGIONAL_PREFERENCES_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.regionalpreferences.RegionalPreferencesEntriesFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true"/>
        </activity>

        <activity
            android:name=".Settings$KeyboardSettingsActivity"
            android:label="@string/keyboard_settings"
            android:exported="true"
            android:icon="@drawable/ic_settings_language">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.VOICE_LAUNCH"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.inputmethod.KeyboardSettings"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true"/>
        </activity>

        <activity android:name="Settings$AvailableVirtualKeyboardActivity"
            android:exported="true"
            android:label="@string/available_virtual_keyboard_category">
            <intent-filter android:priority="1">
                <action android:name="android.settings.INPUT_METHOD_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.inputmethod.AvailableVirtualKeyboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity
            android:name="Settings$ManageAssistActivity"
            android:exported="true"
            android:label="@string/assist_and_voice_input_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.VOICE_INPUT_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.assist.ManageAssist" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name="Settings$KeyboardLayoutPickerActivity"
            android:label="@string/keyboard_layout_picker_title"
            android:clearTaskOnLaunch="true"
            android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.inputmethod.KeyboardLayoutPickerFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name="Settings$PhysicalKeyboardActivity"
            android:label="@string/physical_keyboard_title"
            android:exported="true"
            android:clearTaskOnLaunch="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.HARD_KEYBOARD_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.inputmethod.PhysicalKeyboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name="LanguageSettings"
                android:label="@string/language_input_gesture_title"
                android:clearTaskOnLaunch="true"
                android:exported="true"
                android:targetActivity="Settings$LanguageAndInputSettingsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.language.LanguageAndInputSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity-alias>

        <activity
            android:name="Settings$SpellCheckersSettingsActivity"
            android:exported="true"
            android:label="@string/spellcheckers_settings_title">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.inputmethod.SpellCheckersSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name=".inputmethod.InputMethodAndSubtypeEnablerActivity"
                android:label=""
                android:exported="true"
                android:clearTaskOnLaunch="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.INPUT_METHOD_SUBTYPE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$UserDictionarySettingsActivity"
            android:exported="true"
            android:label="@string/user_dict_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.USER_DICTIONARY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.inputmethod.UserDictionaryList" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name=".inputmethod.UserDictionaryAddWordActivity"
                  android:visibleToInstantApps="true"
                  android:label="@string/user_dict_settings_title"
                  android:theme="@*android:style/Theme.DeviceDefault.Settings.Dialog.NoActionBar"
                  android:windowSoftInputMode="stateVisible"
                  android:noHistory="true"
                  android:exported="true"
                  android:excludeFromRecents="true">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.USER_DICTIONARY_INSERT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$ZenModeSettingsActivity"
            android:label="@string/zen_mode_settings_title"
            android:icon="@drawable/ic_homepage_notification"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_PRIORITY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="41">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.zen.ZenModeSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
        android:name="Settings$EthernetSettingsActivity"
            android:label="@string/ethernet_settings_title"
            android:icon="@drawable/ic_settings_wireless"
            android:taskAffinity=""
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.ethernet.EthernetSettings" />
        </activity>
        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias
        android:name=".EthernetSettings"
        android:label="@string/ethernet_settings"
        android:clearTaskOnLaunch="true"
            android:targetActivity="Settings$EthernetSettingsActivity"
            android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.EthernetSettings" />
        </activity-alias>

        <activity
            android:name=".notification.zen.ZenSuggestionActivity"
            android:label="@string/zen_mode_settings_title"
            android:icon="@drawable/ic_suggestion_dnd"
            android:exported="true"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.ZEN" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>

            <meta-data android:name="com.android.settings.dismiss"
                       android:value="0" />
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/zen_suggestion_title" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/zen_suggestion_summary" />
        </activity>

        <activity
            android:name=".notification.zen.ZenOnboardingActivity"
            android:label="@string/zen_onboarding_dnd_visual_disturbances_header"
            android:icon="@drawable/ic_notifications"
            android:theme="@*android:style/Theme.DeviceDefault.Settings.Dialog.NoActionBar"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_ONBOARDING" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="Settings$ZenModeAutomationSettingsActivity"
            android:label="@string/zen_mode_automation_settings_title"
            android:icon="@drawable/ic_notifications"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_AUTOMATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACTION_CONDITION_PROVIDER_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.zen.ZenModeAutomationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$WallpaperSettingsActivity"
                  android:label="@string/wallpaper_settings_fragment_title"
                  android:icon="@drawable/ic_wallpaper"
                  android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.wallpaper.WallpaperTypeSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_wallpaper"/>
        </activity>

        <activity android:name=".wallpaper.WallpaperSuggestionActivity"
                  android:label="@string/wallpaper_settings_title"
                  android:icon="@drawable/ic_wallpaper"
                  android:exported="true"
                  android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.settings.WALLPAPER_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="com.android.settings.suggested.category.PERSONALIZE"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/wallpaper_suggestion_title"/>
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/wallpaper_suggestion_summary" />
            <meta-data android:name="com.android.settings.dismiss"
                       android:value="3,7,30" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity>

        <activity android:name=".wallpaper.StyleSuggestionActivity"
                  android:label="@string/style_suggestion_title"
                  android:icon="@drawable/ic_theme"
                  android:exported="true"
                  android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/style_suggestion_title" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/style_suggestion_summary" />
            <meta-data android:name="com.android.settings.dismiss"
                       android:value="3,7,30" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity>

        <activity
            android:name="Settings$ZenModeScheduleRuleSettingsActivity"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_SCHEDULE_RULE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.zen.ZenModeScheduleRuleSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$ZenModeEventRuleSettingsActivity"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ZEN_MODE_EVENT_RULE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.zen.ZenModeEventRuleSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$DisplaySettingsActivity"
            android:label="@string/display_settings"
            android:exported="true"
            android:icon="@drawable/ic_homepage_display">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.DISPLAY_SETTINGS" />
                <action android:name="android.settings.DISPLAY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="30">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.DisplaySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$SmartAutoRotateSettingsActivity"
            android:label="@string/accelerometer_title"
            android:icon="@drawable/ic_screen_rotation"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.AUTO_ROTATE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="32">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.SmartAutoRotatePreferenceFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
        </activity>

        <activity
            android:name="Settings$NightDisplaySettingsActivity"
            android:label="@string/night_display_title"
            android:enabled="@*android:bool/config_nightDisplayAvailable"
            android:exported="true"
            android:icon="@drawable/ic_homepage_night_display">
            <intent-filter android:priority="32">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.NIGHT_DISPLAY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.NightDisplaySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
                android:name="Settings$DarkThemeSettingsActivity"
                android:label="@string/dark_ui_mode"
                android:exported="true"
                android:enabled="true">
            <intent-filter android:priority="32">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.DARK_THEME_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.darkmode.DarkModeSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$NightDisplaySuggestionActivity"
            android:enabled="@*android:bool/config_nightDisplayAvailable"
            android:exported="true"
            android:icon="@drawable/ic_suggestion_night_display">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.dismiss"
                android:value="6,10,30" />
            <meta-data android:name="com.android.settings.title"
                android:resource="@string/night_display_suggestion_title" />
            <meta-data android:name="com.android.settings.summary"
                android:resource="@string/night_display_suggestion_summary" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.NightDisplaySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
        </activity>

        <activity android:name=".Settings$MyDeviceInfoActivity"
                  android:label="@string/about_settings"
                  android:exported="true"
                  android:icon="@drawable/ic_homepage_about">
            <intent-filter android:priority="1">
                <action android:name="android.settings.DEVICE_INFO_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.settings.DEVICE_NAME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="71">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.deviceinfo.aboutphone.MyDeviceInfoFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_about_device"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name=".Settings$LockScreenSettingsActivity"
            android:label="@string/lockscreen_settings_title"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.LOCK_SCREEN_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.security.LockscreenDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name=".Settings$BlueToothPairingActivity"
            android:label="@string/bluetooth_pairing_page_title"
            android:permission="android.permission.BLUETOOTH_SCAN"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.BLUETOOTH_PAIRING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.bluetooth.BluetoothPairingDetail" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="SettingsLicenseActivity"
            android:label="@string/settings_license_activity_title"
            android:exported="true"
            android:configChanges="orientation|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.LICENSE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".Settings$ModuleLicensesActivity"
                  android:exported="true"
                  android:label="@string/module_license_title">
            <intent-filter>
                <action android:name="android.settings.MODULE_LICENSES" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.deviceinfo.legal.ModuleLicensesDashboard" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_about_device"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$ManageApplicationsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/applications_settings">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APPLICATION_SETTINGS" />
                <action android:name="android.settings.MANAGE_APPLICATIONS_SETTINGS" />
                <action android:name="android.settings.MANAGE_ALL_APPLICATIONS_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="20">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name=".applications.ManageApplications"
                        android:label="@string/applications_settings"
                        android:exported="true"
                        android:targetActivity="Settings$ManageApplicationsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity-alias>

        <activity android:name="Settings$UserAspectRatioAppListActivity"
            android:exported="true"
            android:label="@string/aspect_ratio_experimental_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_USER_ASPECT_RATIO_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name="Settings$UserAspectRatioAppActivity"
                  android:exported="true"
                  android:label="@string/aspect_ratio_experimental_title">
            <intent-filter>
                <action android:name="android.settings.MANAGE_USER_ASPECT_RATIO_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appcompat.UserAspectRatioDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$ManageDomainUrlsActivity"
            android:exported="true"
            android:label="@string/domain_urls_title">
            <intent-filter>
                <action android:name="android.settings.MANAGE_DOMAIN_URLS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.managedomainurls.ManageDomainUrls" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name="Settings$AppMemoryUsageActivity"
                  android:label="@string/app_list_memory_use"
                  android:exported="true"
                  android:icon="@drawable/ic_settings_memory">
            <intent-filter>
                <action android:name="android.settings.APP_MEMORY_USAGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.ProcessStatsUi" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$HighPowerApplicationsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/high_power_apps">
            <intent-filter android:priority="1">
                <action android:name="android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name=".datausage.AppDataUsageActivity"
            android:exported="true"
            android:noHistory="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.IGNORE_BACKGROUND_DATA_RESTRICTIONS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </activity>

        <activity
            android:name=".fuelgauge.RequestIgnoreBatteryOptimizations"
            android:label="@string/high_power_apps"
            android:exported="true"
            android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </activity>

        <activity
            android:name=".slices.SliceDeepLinkSpringBoard"
            android:excludeFromRecents="true"
            android:theme="@android:style/Theme.NoDisplay"
            android:exported="true"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:permission="android.permission.MODIFY_PHONE_STATE">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="settings"
                      android:host="com.android.settings.slices" />
            </intent-filter>
        </activity>

        <!-- Provide direct entry into manage apps showing running services.
             This is for compatibility with old shortcuts. -->
        <activity-alias android:name=".RunningServices"
                android:label="@string/runningservices_settings_title"
                android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                android:exported="true"
                android:targetActivity="Settings$ManageApplicationsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity-alias>

        <!-- Provide direct entry into manage apps showing storage usage for apps.
             This is for compatibility with old shortcuts. -->
        <activity-alias android:name=".applications.StorageUse"
                android:label="@string/storageuse_settings_title"
                android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                android:exported="true"
                android:targetActivity="Settings$ManageApplicationsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity-alias>

        <!-- Still need a top-level activity for showing app details.  Aliasing
             trick is so the code that is now a fragment can still be called
             InstalledAppDetails. -->
        <activity android:name=".applications.InstalledAppDetailsTop"
                  android:label="@string/application_info_label"
                  android:exported="true" />

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name=".applications.InstalledAppDetails"
                android:label="@string/application_info_label"
                android:exported="true"
                android:targetActivity=".applications.InstalledAppDetailsTop">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APPLICATION_DETAILS_SETTINGS" />
                <action android:name="android.intent.action.AUTO_REVOKE_PERMISSIONS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity-alias>

        <activity-alias android:name="ManageFullScreenIntent"
                        android:exported="true"
                        android:targetActivity=".spa.SpaBridgeActivity">
            <meta-data android:name="com.android.settings.spa.DESTINATION"
                       android:value="TogglePermissionAppList/UseFullScreenIntent"/>
        </activity-alias>

        <activity-alias android:name="AppManageFullScreenIntent"
                        android:exported="true"
                        android:targetActivity=".spa.SpaAppBridgeActivity">
            <intent-filter>
                <action android:name="android.settings.MANAGE_APP_USE_FULL_SCREEN_INTENT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.spa.DESTINATION"
                       android:value="TogglePermissionAppInfoPage/UseFullScreenIntent"/>
        </activity-alias>

        <activity android:name=".applications.InstalledAppOpenByDefaultActivity"
                  android:label="@string/application_info_label"
                  android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APP_OPEN_BY_DEFAULT_SETTINGS" />
                <!-- Also catch legacy "com." prefixed action. -->
                <action android:name="com.android.settings.APP_OPEN_BY_DEFAULT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <!-- Provide direct entry into manage apps showing running services. -->
        <activity android:name="Settings$RunningServicesActivity"
                android:exported="true"
                android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                android:label="@string/runningservices_settings_title">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <!-- Provide direct entry into manage apps showing storage usage of apps. -->
        <activity
            android:name="Settings$StorageUseActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/storageuse_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.MANAGE_PACKAGE_STORAGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$NotificationStationActivity"
            android:exported="true"
            android:label="@string/notification_log_title">
            <intent-filter android:priority="22">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.history.NotificationStation" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name=".notification.history.NotificationHistoryActivity"
            android:exported="true"
            android:label="@string/notification_history_title"
            android:theme="@style/Theme.Settings.NoActionBar">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NOTIFICATION_HISTORY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".notification.zen.ZenModeVoiceActivity"
                android:theme="@*android:style/Theme.DeviceDefault.Settings.Dialog.NoActionBar"
                android:exported="true"
                android:label="@string/zen_mode_settings_title">
            <intent-filter>
                <action android:name="android.settings.VOICE_CONTROL_DO_NOT_DISTURB_MODE" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$LocationSettingsActivity"
            android:label="@string/location_settings_title"
            android:icon="@drawable/ic_homepage_location"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.LOCATION_SOURCE_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="52">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.location.LocationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_location"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>
        <activity
            android:name="Settings$ScanningSettingsActivity"
            android:label="@string/location_services_screen_title"
            android:icon="@drawable/ic_homepage_location"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.LOCATION_SCANNING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.location.LocationServices" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_location"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$WifiScanningSettingsActivity"
            android:label="@string/location_scanning_wifi_always_scanning_title"
            android:icon="@drawable/ic_homepage_location"
            android:exported="true"
            android:permission="android.permission.CHANGE_WIFI_STATE"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.WIFI_SCANNING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.location.WifiScanningFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_location"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name=".Settings$SecurityDashboardActivity"
            android:label="@string/security_settings_title"
            android:icon="@drawable/ic_homepage_security"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SECURITY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.credentials.UNLOCK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.security.SecuritySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$SecurityAdvancedSettings"
            android:label="@string/security_advanced_settings"
            android:exported="true"
            android:icon="@drawable/ic_settings_security">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.security.SECURITY_ADVANCED_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.security.SecurityAdvancedSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity
            android:name="Settings$MoreSecurityPrivacySettingsActivity"
            android:label="@string/more_security_privacy_settings"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.MORE_SECURITY_PRIVACY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.settings.PRIVACY_ADVANCED_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.safetycenter.MoreSecurityPrivacyFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_safety_center"/>
        </activity>

        <activity android:name="MonitoringCertInfoActivity"
                android:label=""
                android:theme="@style/Transparent"
                android:exported="true"
                android:excludeFromRecents="true">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.MONITORING_CERT_INFO" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$TrustedCredentialsSettingsActivity"
            android:label="@string/trusted_credentials"
            android:exported="true"
            android:icon="@drawable/ic_settings_security">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.TRUSTED_CREDENTIALS" />
                <action android:name="com.android.settings.TRUSTED_CREDENTIALS_USER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.TrustedCredentialsSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name="Settings$PrivacySettingsActivity"
                android:label="@string/privacy_settings_title"
                android:icon="@drawable/ic_settings_backup"
                android:exported="true"
                android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.backup.PrivacySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_privacy"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name="Settings$PrivacyDashboardActivity"
                  android:label="@string/privacy_dashboard_title"
                  android:exported="true"
                  android:icon="@drawable/ic_settings_privacy">
            <intent-filter>
                <action android:name="android.settings.PRIVACY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_ENABLE_CONTENT_CAPTURE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.privacy.PrivacyDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_privacy"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$PrivacyControlsActivity"
                  android:label="@string/privacy_controls_title"
                  android:exported="true"
                  android:icon="@drawable/ic_settings_privacy">
            <intent-filter android:priority="1">
                <action android:name="android.settings.PRIVACY_CONTROLS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.privacy.PrivacyControlsFragment" />
        </activity>

        <activity android:name=".development.tare.TareHomePage"
                  android:label="@string/tare_settings"
                  android:exported="false" />

        <activity android:name=".development.tare.DropdownActivity"
                  android:exported="false" />

        <activity android:name="SetFullBackupPassword"
                  android:label="@string/local_backup_password_title"
                  android:exported="false" />

        <activity android:name=".security.CredentialStorage"
                android:theme="@style/Transparent"
                android:launchMode="singleTop"
                android:exported="true"
                android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="com.android.credentials.INSTALL" />
                <action android:name="com.android.credentials.RESET" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".security.InstallCaCertificateWarning"
                  android:theme="@style/GlifV3Theme.Light"
                  android:exported="false">
        </activity>

        <activity android:name=".security.RequestManageCredentials"
                  android:theme="@style/Theme.SubSettings"
                  android:exported="true">
            <intent-filter>
                <action android:name="android.security.MANAGE_CREDENTIALS"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$DeviceAdminSettingsActivity"
            android:exported="true"
            android:label="@string/device_admin_settings_title">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.specialaccess.deviceadmin.DeviceAdminSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <!-- Keep compatibility with old shortcuts. -->
        <activity-alias android:name="DeviceAdminSettings"
                android:label="@string/device_admin_settings_title"
                android:exported="true"
                android:targetActivity="Settings$DeviceAdminSettingsActivity">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.specialaccess.deviceadmin.DeviceAdminSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity-alias>

        <activity android:name=".applications.specialaccess.deviceadmin.DeviceAdminAdd"
                  android:label="@string/device_admin_add_title"
                  android:exported="true"
                  android:clearTaskOnLaunch="true"
                  android:theme="@style/Theme.Settings.NoActionBar">
            <intent-filter android:priority="1000">
                <action android:name="android.app.action.ADD_DEVICE_ADMIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".applications.specialaccess.deviceadmin.ProfileOwnerAdd"
                  android:excludeFromRecents="true"
                  android:theme="@style/Transparent"
                  android:exported="true"
                  android:clearTaskOnLaunch="true">
            <intent-filter android:priority="1000">
                <action android:name="android.app.action.SET_PROFILE_OWNER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$UsageAccessSettingsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/usage_access_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.USAGE_ACCESS_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppUsageAccessSettingsActivity"
            android:exported="true"
            android:label="@string/usage_access_title">
            <intent-filter>
                <action android:name="android.settings.USAGE_ACCESS_SETTINGS"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:scheme="package"/>
            </intent-filter>
            <meta-data
                android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.UsageAccessDetails"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name="Settings$IccLockSettingsActivity"
                android:exported="true"
                android:label="@string/sim_lock_settings">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.IccLockSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_security"/>
        </activity>

        <activity
            android:name="Settings$AccessibilitySettingsActivity"
            android:label="@string/accessibility_settings"
            android:icon="@drawable/ic_homepage_accessibility"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACCESSIBILITY_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="60">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.AccessibilitySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$AccessibilityDetailsSettingsActivity"
            android:label="@string/accessibility_settings"
            android:exported="true"
            android:permission="android.permission.OPEN_ACCESSIBILITY_DETAILS_SETTINGS">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACCESSIBILITY_DETAILS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.AccessibilityDetailsSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".accessibility.AccessibilitySettingsForSetupWizardActivity"
                android:icon="@drawable/ic_accessibility_suggestion"
                android:label="@string/vision_settings_title"
                android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACCESSIBILITY_SETTINGS_FOR_SUW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.AccessibilitySettingsForSetupWizard" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity-alias
            android:name=".FontSizeSettingsForSetupWizardActivity"
            android:exported="true"
            android:targetActivity=".accessibility.AccessibilitySettingsForSetupWizardActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
            <meta-data android:name="com.android.settings.title"
                android:resource="@string/vision_settings_suggestion_title" />
            <meta-data android:name="com.android.settings.icon_tintable"
                android:value="true" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.AccessibilitySettingsForSetupWizard" />
        </activity-alias>

        <activity-alias
            android:name=".TextReadingForSetupWizardActivity"
            android:exported="true"
            android:icon="@drawable/ic_font_download"
            android:targetActivity=".accessibility.AccessibilitySettingsForSetupWizardActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <!-- TODO(b/1352717): Consider replacing it with another category, temporarily
                using an older one to avoid side problems. -->
                <category android:name="com.android.settings.suggested.category.DISPLAY_SETTINGS" />
            </intent-filter>

            <meta-data
                android:name="com.android.settings.title"
                android:resource="@string/accessibility_text_reading_options_suggestion_title" />
            <meta-data
                android:name="com.android.settings.icon_tintable"
                android:value="true" />
            <meta-data
                android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.TextReadingPreferenceFragmentForSetupWizard" />
        </activity-alias>

        <activity
            android:name="Settings$TextReadingSettingsActivity"
            android:exported="true"
            android:label="@string/accessibility_text_reading_options_title">
            <intent-filter>
                <action android:name="android.settings.TEXT_READING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.TextReadingPreferenceFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$AccessibilityDaltonizerSettingsActivity"
            android:exported="true"
            android:label="@string/accessibility_display_daltonizer_preference_title">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.ACCESSIBILITY_COLOR_SPACE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.ToggleDaltonizerPreferenceFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$ReduceBrightColorsSettingsActivity"
            android:exported="true"
            android:label="@string/reduce_bright_colors_preference_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REDUCE_BRIGHT_COLORS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.accessibility.ToggleReduceBrightColorsPreferenceFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AccessibilityInversionSettingsActivity"
            android:exported="true"
            android:label="@string/accessibility_display_inversion_preference_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.COLOR_INVERSION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.ToggleColorInversionPreferenceFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$CaptioningSettingsActivity"
            android:exported="true"
            android:label="@string/accessibility_captioning_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.CAPTIONING_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.CaptioningPropertiesFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$ColorAndMotionActivity"
            android:exported="true"
            android:label="@string/accessibility_color_and_motion_title">
            <intent-filter>
                <action android:name="android.settings.ACCESSIBILITY_COLOR_MOTION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accessibility.ColorAndMotionFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$TextToSpeechSettingsActivity"
            android:exported="true"
            android:label="@string/tts_settings">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.TTS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.tts.TextToSpeechSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accessibility"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$EnterprisePrivacySettingsActivity"
            android:exported="true"
            android:label="@string/enterprise_privacy_settings">
            <intent-filter>
                <action android:name="android.settings.ENTERPRISE_PRIVACY_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.enterprise.EnterprisePrivacySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_privacy"/>
        </activity>

        <!-- Lock screen settings -->
        <activity android:name=".password.ConfirmDeviceCredentialActivity"
            android:exported="true"
            android:taskAffinity="com.android.settings.workmode"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter android:priority="1">
                <action android:name="android.app.action.CONFIRM_DEVICE_CREDENTIAL" />
                <action android:name="android.app.action.CONFIRM_FRP_CREDENTIAL" />
                <action android:name="android.app.action.PREPARE_REPAIR_MODE_DEVICE_CREDENTIAL" />
                <action android:name="android.app.action.CONFIRM_REPAIR_MODE_DEVICE_CREDENTIAL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <!-- Activity alias for compatibility -->
        <activity-alias android:name=".ConfirmDeviceCredentialActivity"
            android:targetActivity=".password.ConfirmDeviceCredentialActivity"
            android:exported="true" />
        <!-- Activity alias for remote lockscreen validation. Enforces required permission -->
        <activity-alias
            android:name=".ConfirmRemoteDeviceCredentialActivity"
            android:targetActivity=".password.ConfirmDeviceCredentialActivity"
            android:permission="android.permission.CHECK_REMOTE_LOCKSCREEN"
            android:exported="true">
            <intent-filter>
                <action android:name="android.app.action.CONFIRM_REMOTE_DEVICE_CREDENTIAL"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity-alias>

        <!-- Note this must not be exported since it authenticates the given user -->
        <activity android:name=".password.ConfirmDeviceCredentialActivity$InternalActivity"
            android:exported="false"
            android:permission="android.permission.MANAGE_USERS"
            android:taskAffinity="com.android.settings.workmode"
            android:theme="@android:style/Theme.Translucent.NoTitleBar">
            <intent-filter android:priority="1">
                <action android:name="android.app.action.CONFIRM_DEVICE_CREDENTIAL_WITH_USER" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".SetupRedactionInterstitial"
            android:enabled="false"
            android:exported="true"
            android:theme="@style/SudThemeGlif.DayNight"
            android:label="@string/lock_screen_notifications_interstitial_title"
            android:icon="@drawable/ic_suggested_notifications">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.LOCK_SCREEN_REDACTION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.require_user_type"
                       android:value="primary" />
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/notification_suggestion_title" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/notification_suggestion_summary" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity>

        <activity android:name=".notification.RedactionInterstitial"
            android:theme="@style/GlifTheme.Light" />

        <activity android:name=".notification.RedactionSettingsStandalone"
                  android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACTION_APP_NOTIFICATION_REDACTION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".password.ConfirmLockPattern"
            android:theme="@style/GlifTheme.Light"/>

        <activity android:name=".password.ConfirmLockPassword"
            android:windowSoftInputMode="stateHidden|adjustResize"
            android:theme="@style/GlifTheme.Light"/>

        <activity android:name=".password.ForgotPasswordActivity"
            android:theme="@style/GlifV3Theme.DayNight"
            android:exported="false"/>

        <activity android:name=".biometrics.face.FaceEnrollParentalConsent"
                  android:exported="false"
                  android:screenOrientation="portrait"/>

        <activity android:name=".biometrics.face.FaceEnrollIntroduction"
                  android:exported="true"
                  android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.settings.FACE_ENROLL"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:name=".biometrics.face.FaceEnrollIntroductionInternal"
                  android:exported="false"
                  android:screenOrientation="portrait"
                  android:taskAffinity="com.android.settings.root" />

        <activity android:name=".biometrics.face.FaceEnrollEducation"
            android:exported="false"
            android:screenOrientation="portrait"/>

        <activity android:name=".biometrics.face.FaceEnrollEnrolling"
            android:exported="false"
            android:screenOrientation="portrait"/>

        <activity android:name=".biometrics.face.FaceEnrollFinish"
            android:exported="false"
            android:screenOrientation="portrait"/>

        <activity android:name=".biometrics.BiometricHandoffActivity"
                  android:exported="false"/>

        <!-- Must not be exported -->
        <activity android:name=".biometrics.BiometricEnrollActivity$InternalActivity"
            android:exported="false"
            android:theme="@style/GlifTheme.Light"/>

        <activity android:name=".biometrics.BiometricEnrollActivity"
            android:exported="true"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|smallestScreenSize|screenLayout|density"
            android:theme="@style/GlifTheme.Light">
            <intent-filter>
                <action android:name="android.settings.BIOMETRIC_ENROLL" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".biometrics.fingerprint.FingerprintSettings"
                  android:exported="false"
                  android:taskAffinity="com.android.settings.root" />
        <activity android:name=".biometrics.fingerprint.FingerprintEnrollFindSensor" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.FingerprintEnrollEnrolling" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.FingerprintEnrollFinish" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.FingerprintEnrollParentalConsent" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.FingerprintEnrollIntroduction"
            android:exported="true"
            android:theme="@style/GlifTheme.Light">
            <intent-filter>
                <action android:name="android.settings.FINGERPRINT_ENROLL" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".biometrics2.ui.view.FingerprintEnrollmentActivity"
            android:exported="true"
            android:permission="android.permission.MANAGE_FINGERPRINT"
            android:theme="@style/GlifTheme.Light"/>

        <activity android:name=".biometrics.fingerprint.FingerprintEnrollIntroductionInternal"
                  android:exported="false"
                  android:theme="@style/GlifTheme.Light"
                  android:taskAffinity="com.android.settings.root" />

        <activity android:name=".biometrics.fingerprint.SetupFingerprintEnrollFindSensor" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.SetupFingerprintEnrollEnrolling" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.SetupFingerprintEnrollFinish" android:exported="false"/>
        <activity android:name=".biometrics.fingerprint.SetupFingerprintEnrollIntroduction"
            android:exported="true"
            android:permission="android.permission.MANAGE_FINGERPRINT"
            android:theme="@style/GlifTheme.Light">
            <intent-filter>
                <action android:name="android.settings.FINGERPRINT_SETUP" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".biometrics.fingerprint.FingerprintSuggestionActivity"
            android:exported="true"
            android:permission="android.permission.MANAGE_FINGERPRINT"
            android:icon="@drawable/ic_suggestion_fingerprint"
            android:theme="@style/GlifTheme.Light">
            <meta-data android:name="com.android.settings.require_feature"
                android:value="android.hardware.fingerprint" />
            <meta-data android:name="com.android.settings.title"
                android:resource="@string/suggestion_additional_fingerprints" />
            <meta-data android:name="com.android.settings.summary"
                android:resource="@string/suggestion_additional_fingerprints_summary" />
        </activity>

        <activity-alias android:name=".SetupFingerprintSuggestionActivity"
            android:enabled="false"
            android:exported="true"
            android:targetActivity=".biometrics.fingerprint.FingerprintSuggestionActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FINGERPRINT_ENROLL" />
            </intent-filter>
            <meta-data android:name="com.android.settings.require_feature"
                       android:value="android.hardware.fingerprint" />
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/suggestion_additional_fingerprints" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/suggestion_additional_fingerprints_summary" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity-alias>

        <activity android:name=".biometrics.activeunlock.ActiveUnlockRequireBiometricSetup" android:exported="false"/>

        <!-- Note this must not be exported since it returns the password in the intent -->
        <activity android:name=".password.ConfirmLockPattern$InternalActivity"
            android:exported="false"
            android:theme="@style/GlifTheme.Light"/>

        <!-- Note this must not be exported since it returns the password in the intent -->
        <activity android:name=".password.ConfirmLockPassword$InternalActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustResize"
            android:theme="@style/GlifTheme.Light"/>

        <activity android:name=".password.SetupChooseLockGeneric"
            android:theme="@style/GlifTheme.Light"
            android:exported="true"
            android:label="@string/lock_settings_picker_title">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.SETUP_LOCK_SCREEN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".password.SetupChooseLockGeneric$InternalActivity"
            android:exported="false"
            android:excludeFromRecents="true" />

        <activity android:name=".password.ChooseLockGeneric"
            android:label="@string/lockpassword_choose_lock_generic_header"
            android:excludeFromRecents="true"
            android:exported="false" />

        <activity android:name=".password.SetNewPasswordActivity"
            android:theme="@android:style/Theme.NoDisplay"
            android:exported="true"
            android:excludeFromRecents="true" >
            <intent-filter android:priority="1">
                <action android:name="android.app.action.SET_NEW_PASSWORD" />
                <action android:name="android.app.action.SET_NEW_PARENT_PROFILE_PASSWORD" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".password.ScreenLockSuggestionActivity"
            android:exported="false"
            android:icon="@drawable/ic_suggestion_security">
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.dismiss" android:value="14" />
            <meta-data android:name="com.android.settings.title"
                       android:resource="@string/suggested_lock_settings_title" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/suggested_lock_settings_summary" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity>

        <activity android:name=".biometrics.fingerprint.FingerprintEnrollSuggestionActivity"
            android:exported="true"
            android:icon="@drawable/ic_suggestion_fingerprint">
            <intent-filter android:priority="2">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.dismiss" android:value="14" />
            <meta-data android:name="com.android.settings.require_feature"
                android:value="android.hardware.fingerprint" />
            <meta-data android:name="com.android.settings.title"
                android:resource="@string/suggested_fingerprint_lock_settings_title" />
            <meta-data android:name="com.android.settings.summary"
                android:resource="@string/suggested_fingerprint_lock_settings_summary" />
            <meta-data android:name="com.android.settings.icon_tintable" android:value="true" />
        </activity>

        <activity android:name=".password.ChooseLockGeneric$InternalActivity"
            android:exported="false"
            android:label="@string/lockpassword_choose_lock_generic_header"
            android:excludeFromRecents="true" />

        <activity android:name=".password.SetupChooseLockPattern"
            android:exported="false"
            android:theme="@style/GlifTheme.Light" />

        <activity android:name=".password.ChooseLockPattern"
            android:exported="false"
            android:theme="@style/GlifTheme.Light" />

        <activity android:name=".password.SetupChooseLockPassword"
            android:exported="false"
            android:theme="@style/GlifTheme.Light"
            android:windowSoftInputMode="stateVisible|adjustResize" />

        <activity android:name=".password.ChooseLockPassword"
            android:exported="false"
            android:theme="@style/GlifTheme.Light"
            android:windowSoftInputMode="stateVisible|adjustResize"/>

        <activity
            android:name=".Settings$StorageDashboardActivity"
            android:label="@string/storage_settings"
            android:exported="true"
            android:icon="@drawable/ic_homepage_storage">
            <intent-filter android:priority="1">
                <action android:name="android.settings.INTERNAL_STORAGE_SETTINGS" />
                <action android:name="android.settings.MEMORY_CARD_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="50">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.deviceinfo.StorageDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_storage"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$PublicVolumeSettingsActivity"
            android:exported="true"
            android:label="@string/storage_settings_title">
            <intent-filter>
                <action android:name="android.provider.action.DOCUMENT_ROOT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data
                    android:scheme="content"
                    android:host="com.android.externalstorage.documents"
                    android:mimeType="vnd.android.document/root" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.deviceinfo.PublicVolumeSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_storage"/>
        </activity>

        <activity
            android:name="Settings$PrivateVolumeForgetActivity"
            android:label="@string/storage_settings_title"
            android:exported="true"
            android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.deviceinfo.PrivateVolumeForget" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_storage"/>
        </activity>

        <!-- Exported for SystemUI to launch into -->
        <activity android:name=".deviceinfo.StorageWizardInit"
                android:theme="@style/GlifTheme.Light"
                android:exported="true"
                android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
        <activity android:name=".deviceinfo.StorageWizardFormatProgress"
                android:theme="@style/GlifTheme.Light"
                android:exported="false"/>
        <activity android:name=".deviceinfo.StorageWizardFormatSlow"
                android:theme="@style/GlifTheme.Light"
                android:exported="false"/>
        <activity android:name=".deviceinfo.StorageWizardMigrateConfirm"
                android:theme="@style/GlifTheme.Light"
                android:exported="false"/>
        <activity android:name=".deviceinfo.StorageWizardMigrateProgress"
                android:theme="@style/GlifTheme.Light"
                android:exported="true"
                android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
        <activity android:name=".deviceinfo.StorageWizardReady"
                android:theme="@style/GlifTheme.Light"
                android:exported="true"
                android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />

        <activity android:name=".deviceinfo.StorageWizardMoveConfirm"
                android:theme="@style/GlifTheme.Light"
                android:exported="false"/>
        <activity android:name=".deviceinfo.StorageWizardMoveProgress"
                android:theme="@style/GlifTheme.Light"
                android:exported="true"
                android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />

        <!-- Exported for SystemUI to trigger -->
        <receiver android:name=".deviceinfo.StorageUnmountReceiver"
                android:exported="true"
                android:permission="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />

        <activity android:name="Settings$ApnEditorActivity"
                android:configChanges="orientation|keyboardHidden|screenSize"
                android:exported="true"
                android:label="@string/apn_edit">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <action android:name="android.intent.action.EDIT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.item/telephony-carrier" />
            </intent-filter>

            <intent-filter>
                <action android:name="android.intent.action.INSERT" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="vnd.android.cursor.dir/telephony-carrier" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.network.apn.ApnEditor" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity
            android:name="Settings$DevelopmentSettingsDashboardActivity"
            android:label="@string/development_settings_title"
            android:icon="@drawable/ic_settings_development"
            android:exported="true"
            android:enabled="false">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APPLICATION_DEVELOPMENT_SETTINGS" />
                <action android:name="com.android.settings.APPLICATION_DEVELOPMENT_SETTINGS" />
                <action android:name="android.service.quicksettings.action.QS_TILE_PREFERENCES"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.android.settings.action.SETTINGS" />
                <action android:name="com.android.intent.action.SHOW_CONTRAST_DIALOG" />
            </intent-filter>
            <meta-data android:name="com.android.settings.order" android:value="-40"/>
            <meta-data android:name="com.android.settings.category"
                       android:value="com.android.settings.category.ia.system" />
            <meta-data android:name="com.android.settings.summary"
                       android:resource="@string/summary_empty"/>
            <meta-data android:name="com.android.settings.icon"
                       android:resource="@drawable/ic_settings_development" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.development.DevelopmentSettingsDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <!-- The opposite of DevelopmentSettingsActivity, it's no-op and only enabled when the real
             activity is disabled to be CTS compliant. -->
        <activity
            android:name=".development.DevelopmentSettingsDisabledActivity"
            android:icon="@drawable/ic_settings_development"
            android:label="@string/development_settings_title"
            android:excludeFromRecents="true"
            android:exported="true"
            android:theme="@style/Transparent">
            <intent-filter android:priority="-1">
                <action android:name="android.settings.APPLICATION_DEVELOPMENT_SETTINGS" />
                <action android:name="com.android.settings.APPLICATION_DEVELOPMENT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$PrintSettingsActivity"
            android:label="@string/print_settings"
            android:exported="true"
            android:icon="@drawable/ic_settings_print">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACTION_PRINT_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.print.PrintSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity>

        <activity android:name="Settings$PrintJobSettingsActivity"
                android:exported="true"
                android:label="@string/print_print_job">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACTION_PRINT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="printjob" android:pathPattern="*" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.print.PrintJobSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity>

        <activity android:name=".development.AppPicker"
                  android:label="@string/select_application" />

        <activity android:name=".development.AdbQrCodeActivity" />

        <activity android:name="com.android.settings.development.DSULoader"
                  android:label="Select DSU Package"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog"
                  android:exported="true" >
            <intent-filter>
                <action android:name="android.settings.development.START_DSU_LOADER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>
        <activity android:name=".development.DSUTermsOfServiceActivity"
                  android:label="Terms of Service"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog" />

        <activity android:name=".development.storage.BlobInfoListView"
                  android:label="@string/shared_data_title" />
        <activity android:name=".development.storage.LeaseInfoListView"
                  android:label="@string/accessor_info_title" />

        <activity android:name="Settings$WebViewAppPickerActivity"
                  android:label="@string/select_webview_provider_dialog_title">
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name="Settings$BugReportHandlerPickerActivity"
            android:label="@string/bug_report_handler_title"
            android:exported="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.settings.BUGREPORT_HANDLER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.bugreporthandler.BugReportHandlerPicker" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name=".bluetooth.BluetoothPairingDialog"
                  android:permission="android.permission.BLUETOOTH_PRIVILEGED"
                  android:excludeFromRecents="true"
                  android:windowSoftInputMode="stateVisible|adjustResize"
                  android:theme="@style/Theme.AlertDialog"
                  android:exported="true"
                  android:taskAffinity=".bluetooth.BluetoothPairingDialog">
            <intent-filter android:priority="1">
                <action android:name="android.bluetooth.device.action.PAIRING_REQUEST" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".bluetooth.RequestPermissionActivity"
                  android:excludeFromRecents="true"
                  android:permission="android.permission.BLUETOOTH_CONNECT"
                  android:exported="true"
                  android:theme="@style/Theme.BluetoothPermission">
            <intent-filter android:priority="1">
                <action android:name="android.bluetooth.adapter.action.REQUEST_DISCOVERABLE" />
                <action android:name="android.bluetooth.adapter.action.REQUEST_ENABLE" />
                <action android:name="android.bluetooth.adapter.action.REQUEST_DISABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".wifi.WifiScanModeActivity"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:theme="@style/Transparent">
            <intent-filter android:priority="1">
                <action android:name="android.net.wifi.action.REQUEST_SCAN_ALWAYS_AVAILABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".Settings$UsbDetailsActivity"
                  android:excludeFromRecents="true"
                  android:permission="android.permission.MANAGE_USB"
                  android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.connecteddevice.usb.UsbDetailsFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity>

        <activity android:name=".RemoteBugreportActivity"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:permission="android.permission.DUMP"
                  android:theme="@style/Theme.AlertDialog">
            <intent-filter>
                <action android:name="android.settings.SHOW_REMOTE_BUGREPORT_DIALOG" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service android:name=".bluetooth.BluetoothPairingService" />

        <receiver android:name=".bluetooth.BluetoothPairingRequest"
            android:exported="true">
            <intent-filter>
                <action android:name="android.bluetooth.device.action.PAIRING_REQUEST" />
                <action android:name="android.bluetooth.action.CSIS_SET_MEMBER_AVAILABLE"/>
            </intent-filter>
        </receiver>

        <receiver android:name=".bluetooth.BluetoothPermissionRequest"
                  android:exported="true"
                  android:permission="android.permission.BLUETOOTH_CONNECT">
            <intent-filter>
                <action android:name="android.bluetooth.device.action.CONNECTION_ACCESS_REQUEST" />
                <action android:name="android.bluetooth.device.action.CONNECTION_ACCESS_CANCEL" />
            </intent-filter>
        </receiver>

        <activity android:name=".bluetooth.BluetoothPermissionActivity"
                  android:label="@string/bluetooth_connection_permission_request"
                  android:excludeFromRecents="true"
                  android:permission="android.permission.BLUETOOTH_CONNECT"
                  android:exported="true"
                  android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight">
            <intent-filter android:priority="1">
                <action android:name="android.bluetooth.device.action.CONNECTION_ACCESS_REQUEST" />
                <action android:name="android.bluetooth.device.action.CONNECTION_ACCESS_CANCEL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name="ActivityPicker"
                android:label="@string/activity_picker_label"
                android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight"
                android:exported="true"
                android:finishOnCloseSystemDialogs="true">
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.PICK_ACTIVITY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!-- NFC settings -->
        <activity
            android:name="Settings$AndroidBeamSettingsActivity"
            android:exported="true"
            android:label="@string/android_beam_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NFCSHARING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.nfc.AndroidBeam" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$WifiDisplaySettingsActivity"
            android:label="@string/wifi_display_settings_title"
            android:exported="true"
            android:icon="@drawable/ic_cast_24dp">
            <intent-filter android:priority="1">
                <action android:name="android.settings.CAST_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wfd.WifiDisplaySettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
        </activity>

        <activity android:name="Settings$TestingSettingsActivity" android:label="@string/testing">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.TestingSettings" />
        </activity>

        <receiver android:name=".TestingSettingsBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                 <action android:name="android.telephony.action.SECRET_CODE" />
                 <data android:scheme="android_secret_code" android:host="4636" />
            </intent-filter>
       </receiver>

        <!-- Standard picker for widgets -->
        <activity android:name="AppWidgetPickActivity"
                android:label="@string/widget_picker_title"
                android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight"
                android:exported="true"
                android:finishOnCloseSystemDialogs="true">
            <intent-filter android:priority="1">
                <action android:name="android.appwidget.action.APPWIDGET_PICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name="AllowBindAppWidgetActivity"
                android:theme="@*android:style/Theme.DeviceDefault.Dialog.Alert.DayNight"
                android:finishOnCloseSystemDialogs="true"
                android:exported="true"
                android:excludeFromRecents="true">
            <intent-filter android:priority="1">
                <action android:name="android.appwidget.action.APPWIDGET_BIND" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$PowerUsageSummaryActivity"
            android:label="@string/power_usage_summary_title"
            android:exported="true"
            android:icon="@drawable/ic_homepage_battery"
            android:configChanges="orientation|screenSize|screenLayout|smallestScreenSize">
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.POWER_USAGE_SUMMARY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="51">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.fuelgauge.batteryusage.PowerUsageSummary" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_battery"/>
        </activity>

        <provider
            android:name=".fuelgauge.batteryusage.BatteryUsageContentProvider"
            android:enabled="true"
            android:exported="true"
            android:authorities="${applicationId}.battery.usage.provider"
            android:permission="com.android.settings.BATTERY_DATA"/>

        <provider
            android:name=".fuelgauge.batteryusage.bugreport.BugReportContentProvider"
            android:exported="false"
            android:authorities="${applicationId}.battery.usage.bugreport"/>

        <receiver android:name="com.android.settings.fuelgauge.batteryusage.BatteryUsageBroadcastReceiver"
                  android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BATTERY_LEVEL_CHANGED"/>
                <action android:name="com.android.settings.battery.action.CLEAR_BATTERY_CACHE_DATA"/>
                <action android:name="com.android.settings.battery.action.ACTION_BATTERY_PLUGGING"/>
                <action android:name="com.android.settings.battery.action.ACTION_BATTERY_UNPLUGGING"/>
            </intent-filter>
        </receiver>

        <receiver
            android:name=".fuelgauge.batteryusage.BootBroadcastReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
                <action android:name="com.google.android.setupwizard.SETUP_WIZARD_FINISHED"/>
                <action android:name="com.android.settings.battery.action.PERIODIC_JOB_RECHECK"/>
            </intent-filter>
        </receiver>

        <receiver
            android:name=".fuelgauge.BatterySettingsMigrateChecker"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <receiver
            android:name=".fuelgauge.batteryusage.PeriodicJobReceiver"
            android:exported="false">
            <intent-filter>
                <action android:name="com.android.settings.battery.action.PERIODIC_JOB_UPDATE"/>
            </intent-filter>
        </receiver>

        <activity
            android:name="Settings$BatterySaverSettingsActivity"
            android:label="@string/battery_saver"
            android:exported="true"
            android:icon="@drawable/ic_homepage_battery">
            <intent-filter android:priority="1">
                <action android:name="android.settings.BATTERY_SAVER_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.fuelgauge.batterysaver.BatterySaverSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_battery"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".fuelgauge.BatterySaverModeVoiceActivity"
                android:label="@string/power_usage_summary_title"
                android:icon="@drawable/ic_homepage_battery"
                android:theme="@*android:style/Theme.DeviceDefault.Light.Voice"
                android:exported="true">
            <intent-filter>
                <action android:name="android.settings.VOICE_CONTROL_BATTERY_SAVER_MODE" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$AccountSyncSettingsActivity"
            android:exported="true"
            android:label="@string/account_sync_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACCOUNT_SYNC_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accounts.AccountSyncSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accounts"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>
        <activity
            android:name="Settings$ManagedProfileSettingsActivity"
            android:label="@string/managed_profile_settings_title"
            android:exported="true"
            android:permission="android.permission.MANAGE_USERS">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGED_PROFILE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.accounts.ManagedProfileSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accounts"/>
        </activity>

        <activity
            android:name="com.android.settings.accounts.AddAccountSettings"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:label="@string/header_add_an_account">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ADD_ACCOUNT_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name="Settings$ChooseAccountActivity"
            android:label="@string/header_add_an_account"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.accounts.ChooseAccountFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accounts"/>
        </activity>

        <!-- Triggered when user-selected home app isn't encryption aware -->
        <activity android:name=".FallbackHome"
                  android:excludeFromRecents="true"
                  android:label=""
                  android:taskAffinity="com.android.settings.FallbackHome"
                  android:exported="true"
                  android:theme="@style/FallbackHome"
                  android:permission="android.permission.DEVICE_POWER"
                  android:configChanges="keyboardHidden">
            <intent-filter android:priority="-1000">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$DataUsageSummaryActivity"
            android:label="@string/data_usage_summary_title"
            android:exported="true"
            android:enabled="@bool/config_show_sim_info"
            android:icon="@drawable/ic_homepage_data_usage">
            <intent-filter android:priority="1">
                <action android:name="android.settings.DATA_USAGE_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="3">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.datausage.DataUsageSummary" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>
        <activity
            android:name="Settings$MobileDataUsageListActivity"
            android:exported="true"
            android:label="@string/cellular_data_usage">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MOBILE_DATA_USAGE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.datausage.DataUsageList" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity
            android:name="Settings$DreamSettingsActivity"
            android:label="@string/screensaver_settings_title"
            android:exported="true"
            android:icon="@drawable/ic_settings_display">
            <intent-filter android:priority="1">
                <action android:name="android.settings.DREAM_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.dream.DreamSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_display"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$CommunalSettingsActivity"
            android:label="@string/communal_settings_title"
            android:exported="true"
            android:icon="@drawable/ia_settings_communal">
            <intent-filter android:priority="1">
                <action android:name="android.settings.COMMUNAL_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.communal.CommunalDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_communal"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$UserSettingsActivity"
            android:label="@string/user_settings_title"
            android:exported="true"
            android:icon="@drawable/ic_settings_multiuser">
            <intent-filter android:priority="1">
                <action android:name="android.settings.USER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.android.settings.action.SETTINGS" />
            </intent-filter>
            <meta-data android:name="com.android.settings.order" android:value="-45"/>
            <meta-data android:name="com.android.settings.category"
                       android:value="com.android.settings.category.ia.system" />
            <meta-data android:name="com.android.settings.summary_uri"
                       android:value="content://com.android.settings.dashboard.SummaryProvider/user" />
            <meta-data android:name="com.android.settings.icon"
                       android:resource="@drawable/ic_settings_multiuser" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.users.UserSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$PaymentSettingsActivity"
            android:label="@string/nfc_payment_settings_title"
            android:exported="true"
            android:icon="@drawable/ic_settings_nfc_payment">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NFC_PAYMENT_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.nfc.PaymentSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".nfc.PaymentDefaultDialog"
                  android:label="@string/nfc_payment_set_default_label"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert">
            <intent-filter android:priority="1">
                <action android:name="android.nfc.cardemulation.action.ACTION_CHANGE_DEFAULT" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity android:name=".nfc.HowItWorks"
                  android:label="@string/nfc_payment_settings_title"
                  android:excludeFromRecents="true">
        </activity>

        <activity
            android:name="Settings$NotificationAccessSettingsActivity"
            android:exported="true"
            android:label="@string/manage_notification_access_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ACTION_NOTIFICATION_LISTENER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.NotificationAccessSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name="Settings$NotificationAccessDetailsActivity"
            android:exported="true"
            android:label="@string/manage_notification_access_title" >
            <intent-filter android:priority="1">
                <action android:name="android.settings.NOTIFICATION_LISTENER_DETAIL_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.notificationaccess.NotificationAccessDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name="Settings$NotificationAssistantSettingsActivity"
            android:exported="true"
            android:label="@string/notification_assistant_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NOTIFICATION_ASSISTANT_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.ConfigureNotificationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name="Settings$VrListenersSettingsActivity"
            android:exported="true"
            android:label="@string/vr_listeners_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.VR_LISTENER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.vrlistener.VrListenerSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$PictureInPictureSettingsActivity"
            android:exported="true"
            android:label="@string/picture_in_picture_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.PICTURE_IN_PICTURE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.specialaccess.pictureinpicture.PictureInPictureSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$AppPictureInPictureSettingsActivity"
            android:exported="true"
            android:label="@string/picture_in_picture_title">
            <intent-filter>
                <action android:name="android.settings.PICTURE_IN_PICTURE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.specialaccess.pictureinpicture.PictureInPictureDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$TurnScreenOnSettingsActivity"
            android:exported="true"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:label="@string/turn_screen_on_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.TURN_SCREEN_ON_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$AppTurnScreenOnSettingsActivity"
            android:exported="true"
            android:label="@string/turn_screen_on_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.TURN_SCREEN_ON_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.turnscreenon.TurnScreenOnDetails"/>
        </activity>

        <activity
            android:name="Settings$InteractAcrossProfilesSettingsActivity"
            android:exported="true"
            android:label="@string/interact_across_profiles_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_CROSS_PROFILE_ACCESS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.interactacrossprofiles.InteractAcrossProfilesSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name="Settings$AppInteractAcrossProfilesSettingsActivity"
                  android:exported="true"
                  android:label="@string/interact_across_profiles_title">
            <intent-filter>
                <action android:name="android.settings.MANAGE_CROSS_PROFILE_ACCESS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.interactacrossprofiles.InteractAcrossProfilesDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$ZenAccessDetailSettingsActivity"
            android:label="@string/manage_zen_access_title"
            android:exported="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.settings.NOTIFICATION_POLICY_ACCESS_DETAIL_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.zenaccess.ZenAccessDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name="Settings$ZenAccessSettingsActivity"
            android:exported="true"
            android:label="@string/manage_zen_access_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NOTIFICATION_POLICY_ACCESS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.zen.ZenAccessSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <activity
            android:name="Settings$ConfigureNotificationSettingsActivity"
            android:label="@string/configure_notification_settings"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NOTIFICATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="21">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.ConfigureNotificationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$ConversationListSettingsActivity"
            android:label="@string/zen_mode_conversations_title"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.CONVERSATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.app.ConversationListSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppBubbleNotificationSettingsActivity"
            android:exported="true"
            android:label="@string/bubbles_app_toggle_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APP_NOTIFICATION_BUBBLE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.app.AppBubbleNotificationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$PremiumSmsAccessActivity"
            android:label="@string/premium_sms_access"
            android:exported="true">
            <intent-filter>
                <action android:name="android.settings.PREMIUM_SMS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.specialaccess.premiumsms.PremiumSmsAccess" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$SoundSettingsActivity"
            android:label="@string/sound_settings"
            android:icon="@drawable/ic_homepage_sound"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.SOUND_SETTINGS" />
                <action android:name="android.settings.SOUND_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <action android:name="android.settings.ACTION_OTHER_SOUND_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="40">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.SoundSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_sound"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <!-- Show apps for which application-level notification settings are applicable -->
        <activity android:name="Settings$NotificationAppListActivity"
                  android:label="@string/app_notifications_title"
                  android:icon="@drawable/ic_notifications"
                  android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                  android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ALL_APPS_NOTIFICATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <!-- Displays a list of apps available for cloning on the device -->
        <activity android:name=".Settings$ClonedAppsListActivity"
                  android:label="@string/cloned_apps_dashboard_title"
                  android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
                  android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_CLONED_APPS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
        </activity>

        <!-- Application-level notification settings page, same as above but only accessible
             internally from system server -->
        <activity android:name="Settings$NotificationReviewPermissionsActivity"
                  android:label="@string/app_notifications_title"
                  android:icon="@drawable/ic_notifications"
                  android:exported="false">
            <intent-filter android:priority="1">
                <action android:name="android.settings.ALL_APPS_NOTIFICATION_SETTINGS_FOR_REVIEW" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <!-- Show application-level notification settings (app passed in as extras) -->
        <activity android:name="Settings$AppNotificationSettingsActivity"
                android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.APP_NOTIFICATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.notification.app.AppNotificationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <!-- Show channel-level notification settings (channel passed in as extras) -->
        <activity android:name=".notification.app.ChannelPanelActivity"
                  android:label="@string/notification_channel_title"
                  android:theme="@style/Theme.Panel.Material"
                  android:excludeFromRecents="true"
                  android:configChanges="keyboardHidden|screenSize"
                  android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.CHANNEL_NOTIFICATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.notification.app.ChannelNotificationSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_notifications"/>
        </activity>

        <!-- Show Manual (from settings item) -->
        <activity
            android:name="ManualDisplayActivity"
            android:label="@string/manual"
            android:exported="true"
            android:enabled="@bool/config_show_manual">
            <intent-filter>
                <action android:name="android.settings.SHOW_MANUAL" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <!-- Show regulatory info (from settings item or dialing "*#07#") -->
        <activity
            android:name="RegulatoryInfoDisplayActivity"
            android:theme="@style/Theme.AlertDialog"
            android:label="@string/regulatory_labels"
            android:exported="true"
            android:enabled="@bool/config_show_regulatory_info">
            <intent-filter>
                <action android:name="android.settings.SHOW_REGULATORY_INFO" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <!-- Confirmation dialog for enabling notification access from CompanionDeviceManager -->
        <activity android:name=".notification.NotificationAccessConfirmationActivity"
                  android:taskAffinity=".notification.NotificationAccessConfirmationActivity"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert" />

        <receiver android:name="com.android.settingslib.bluetooth.BluetoothDiscoverableTimeoutReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.bluetooth.intent.DISCOVERABLE_TIMEOUT" />
            </intent-filter>
        </receiver>

        <!-- Watch for ContactsContract.Profile changes and update the user's photo.  -->
        <receiver android:name=".users.ProfileUpdateReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.provider.Contacts.PROFILE_CHANGED" />
            </intent-filter>
        </receiver>

        <receiver android:name=".sim.SimSelectNotification"
            android:exported="true">
            <intent-filter>
                <action android:name="android.telephony.action.PRIMARY_SUBSCRIPTION_LIST_CHANGED"/>
                <action android:name="android.settings.ENABLE_MMS_DATA_REQUEST"/>
            </intent-filter>
        </receiver>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.files"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <provider
            android:name=".deviceinfo.legal.ModuleLicenseProvider"
            android:authorities="${applicationId}.module_licenses"
            android:grantUriPermissions="true"
            android:exported="false"/>

        <provider
            android:name=".emergency.EmergencyActionContentProvider"
            android:authorities="${applicationId}.emergency"
            android:permission="android.permission.CALL_PRIVILEGED"
            android:exported="true"/>

        <activity
            android:name=".wifi.RequestToggleWiFiActivity"
            android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert"
            android:excludeFromRecents="true"
            android:exported="true"
            android:permission="android.permission.CHANGE_WIFI_STATE">
            <intent-filter>
                <action android:name="android.net.wifi.action.REQUEST_ENABLE" />
                <action android:name="android.net.wifi.action.REQUEST_DISABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <!--
            The Wi-Fi result data will only be returned from WifiDialogActivity if the calling
            package has ACCESS_COARSE_LOCATION or ACCESS_FINE_LOCATION permission. (see b/185126813)
        -->
        <activity
            android:name=".wifi.WifiDialogActivity"
            android:label=""
            android:theme="@style/Transparent"
            android:excludeFromRecents="true"
            android:documentLaunchMode="always"
            android:exported="true"
            android:permission="android.permission.CHANGE_WIFI_STATE"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout">
            <intent-filter>
                <action android:name="com.android.settings.WIFI_DIALOG" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name=".wifi.NetworkRequestDialogActivity"
            android:theme="@style/Theme.AlertDialog"
            android:excludeFromRecents="true"
            android:launchMode="singleTop"
            android:taskAffinity=".wifi.NetworkRequestDialogActivity"
            android:exported="true"
            android:permission="android.permission.NETWORK_SETTINGS">
            <intent-filter>
                <action android:name="com.android.settings.wifi.action.NETWORK_REQUEST" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <receiver
            android:name=".wifi.slice.ConnectToWifiHandler"
            android:exported="false" />

        <activity
            android:name=".sim.SimDialogActivity"
            android:theme="@style/Theme.AlertDialog.SimConfirmDialog"
            android:label="@string/sim_settings_title"
            android:launchMode="singleTop"
            android:exported="true"
            android:excludeFromRecents="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
            </intent-filter>
        </activity>

        <activity
            android:name=".flashlight.FlashlightHandleActivity"
            android:theme="@android:style/Theme.NoDisplay"
            android:excludeFromRecents="true"
            android:exported="true"
            android:label="@string/power_flashlight"/>

        <activity
            android:name="Settings$WifiCallingSettingsActivity"
            android:exported="true"
            android:configChanges="orientation|keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout"
            android:label="@string/wifi_calling_settings_title">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <action android:name="android.settings.WIFI_CALLING_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.wifi.calling.WifiCallingSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name=".wifi.calling.WifiCallingSuggestionActivity"
                  android:label="@string/wifi_calling_settings_title"
                  android:exported="true"
                  android:icon="@drawable/ic_suggestion_wireless">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.suggested.category.FIRST_IMPRESSION" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.wifi.calling.WifiCallingSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
            <meta-data android:name="com.android.settings.dismiss"
                android:value="10,14,30" />
            <meta-data android:name="com.android.settings.title"
                android:resource="@string/wifi_calling_suggestion_title" />
            <meta-data android:name="com.android.settings.summary"
                android:resource="@string/wifi_calling_suggestion_summary" />
        </activity>

        <provider
            android:name=".search.SettingsSearchIndexablesProvider"
            android:authorities="${applicationId}"
            android:multiprocess="false"
            android:grantUriPermissions="true"
            android:permission="android.permission.READ_SEARCH_INDEXABLES"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.action.SEARCH_INDEXABLES_PROVIDER" />
            </intent-filter>
        </provider>

        <provider
            android:name=".dashboard.suggestions.SuggestionStateProvider"
            android:authorities="${applicationId}.suggestions.status"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.settings.action.SUGGESTION_STATE_PROVIDER" />
            </intent-filter>
        </provider>

        <activity
            android:name="Settings$OverlaySettingsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/draw_overlay">
            <intent-filter android:priority="1">
                <action android:name="android.settings.action.MANAGE_OVERLAY_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.action.MANAGE_OVERLAY_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppDrawOverlaySettingsActivity"
            android:label="@string/draw_overlay"
            android:exported="true"
            android:permission="android.permission.INTERNAL_SYSTEM_WINDOW">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_APP_OVERLAY_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.DrawOverlayDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$WriteSettingsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/write_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.action.MANAGE_WRITE_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppWriteSettingsActivity"
            android:exported="true"
            android:label="@string/write_settings_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.action.MANAGE_WRITE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.WriteSettingsDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$AlarmsAndRemindersActivity"
            android:exported="true"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:label="@string/alarms_and_reminders_label">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_SCHEDULE_EXACT_ALARM" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AlarmsAndRemindersAppActivity"
            android:exported="true"
            android:label="@string/alarms_and_reminders_label">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_SCHEDULE_EXACT_ALARM" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.AlarmsAndRemindersDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$ManageExternalSourcesActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/install_other_apps">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_UNKNOWN_APP_SOURCES" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$ManageAppExternalSourcesActivity"
                  android:exported="true"
                  android:label="@string/install_other_apps">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_UNKNOWN_APP_SOURCES" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.ExternalSourcesDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity android:name=".enterprise.ActionDisabledByAdminDialog"
                  android:theme="@style/Theme.AlertDialog"
                android:taskAffinity="com.android.settings.enterprise"
                android:excludeFromRecents="true"
                android:exported="true"
                android:launchMode="singleTop">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SHOW_ADMIN_SUPPORT_DETAILS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity android:name=".ActionDisabledByAppOpsDialog"
                  android:theme="@style/Theme.AlertDialog"
                  android:taskAffinity="com.android.settings.appops"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:launchMode="singleTop">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SHOW_RESTRICTED_SETTING_DIALOG" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <activity
            android:name="Settings$ManageExternalStorageActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/manage_external_storage_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_ALL_FILES_ACCESS_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_storage"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppManageExternalStorageActivity"
            android:exported="true"
            android:label="@string/manage_external_storage_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MANAGE_APP_ALL_FILES_ACCESS_PERMISSION" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.ManageExternalStorageDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name="Settings$MediaManagementAppsActivity"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="true"
            android:label="@string/media_management_apps_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_MANAGE_MEDIA" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.manageapplications.ManageApplications" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$AppMediaManagementAppsActivity"
            android:exported="true"
            android:label="@string/media_management_apps_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_MANAGE_MEDIA" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.appinfo.MediaManagementAppsDetails" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <!-- Keep compatibility with old WebView-picker implementation -->
        <activity-alias android:name=".WebViewImplementation"
                  android:targetActivity="Settings$WebViewAppPickerActivity"
                  android:exported="true"
                  android:excludeFromRecents="true"
                  android:theme="@*android:style/Theme.DeviceDefault.Light.Dialog.Alert">
            <intent-filter>
                <action android:name="android.settings.WEBVIEW_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.webview.WebViewAppPicker" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity-alias>

        <provider
            android:name=".dashboard.SummaryProvider"
            android:authorities="${applicationId}.dashboard.SummaryProvider">
        </provider>

        <activity android:name=".backup.UserBackupSettingsActivity"
                  android:label="@string/privacy_settings_title"
                  android:exported="true"
                  android:icon="@drawable/ic_settings_backup">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
            </intent-filter>
            <!-- Mark the activity as a dynamic setting -->
            <intent-filter>
                <action android:name="com.android.settings.action.IA_SETTINGS" />
            </intent-filter>
            <!-- Tell Settings app which category it belongs to -->
            <meta-data android:name="com.android.settings.category"
                       android:value="com.android.settings.category.ia.system" />
            <meta-data android:name="com.android.settings.icon"
                       android:resource="@drawable/ic_settings_backup" />
            <meta-data android:name="com.android.settings.order" android:value="-60"/>
        </activity>

        <activity
            android:name="Settings$AutomaticStorageManagerSettingsActivity"
            android:exported="@bool/config_storage_manager_settings_enabled"
            android:label="@string/automatic_storage_manager_settings">
            <intent-filter android:priority="1">
                <action android:name="android.settings.STORAGE_MANAGER_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.deletionhelper.AutomaticStorageManagerSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_storage"/>
        </activity>

        <!-- Show app-level advanced power usage details (app passed in as extras) -->
        <activity
            android:name=".fuelgauge.AdvancedPowerUsageDetailActivity"
            android:excludeFromRecents="true"
            android:launchMode="singleInstance"
            android:exported="true"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <action android:name="android.settings.VIEW_ADVANCED_POWER_USAGE_DETAIL" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </activity>

        <activity
            android:name=".Settings$AppDashboardActivity"
            android:label="@string/apps_dashboard_title"
            android:icon="@drawable/ic_homepage_apps"
            android:exported="true">
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.applications.AppDashboardFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_apps"/>
        </activity>

        <activity
            android:name=".Settings$AccountDashboardActivity"
            android:label="@string/account_dashboard_title"
            android:exported="true"
            android:icon="@drawable/ic_homepage_accounts">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SYNC_SETTINGS" />
                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.settings.CREDENTIAL_PROVIDER" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
            <intent-filter android:priority="53">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.accounts.AccountDashboardFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_accounts"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name=".Settings$SystemDashboardActivity"
            android:label="@string/header_category_system"
            android:exported="true"
            android:icon="@drawable/ic_homepage_system_dashboard">
            <intent-filter android:priority="70">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.system.SystemDashboardFragment"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name=".support.SupportDashboardActivity"
                  android:label="@string/page_tab_title_support"
                  android:icon="@drawable/ic_homepage_support"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Panel"
                  android:exported="true"
                  android:enabled="@bool/config_support_enabled">
            <intent-filter>
                <action android:name="com.android.settings.action.SUPPORT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".SettingsDumpService"
            android:exported="true"
            android:permission="android.permission.DUMP" />

        <!-- Quick Settings tiles for Developer Options -->
        <service
            android:name=".development.qstile.DevelopmentTiles$ShowLayout"
            android:label="@string/debug_layout"
            android:icon="@drawable/tile_icon_show_layout"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>
        <service
            android:name=".development.qstile.DevelopmentTiles$GPUProfiling"
            android:label="@string/track_frame_time"
            android:icon="@drawable/tile_icon_graphics"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>
        <service
            android:name=".development.qstile.DevelopmentTiles$ForceRTL"
            android:label="@string/force_rtl_layout_all_locales"
            android:icon="@drawable/tile_icon_force_rtl"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>
        <service
            android:name=".development.qstile.DevelopmentTiles$AnimationSpeed"
            android:label="@string/window_animation_scale_title"
            android:icon="@drawable/tile_icon_animation_speed"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>

        <service
            android:name=".development.qstile.DevelopmentTiles$WinscopeTrace"
            android:label="@string/winscope_trace_quick_settings_title"
            android:icon="@drawable/tile_icon_winscope_trace"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>

        <service
            android:name=".development.qstile.DevelopmentTiles$SensorsOff"
            android:label="@string/sensors_off_quick_settings_title"
            android:icon="@drawable/tile_icon_sensors_off"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>

        <service
            android:name=".development.qstile.DevelopmentTiles$WirelessDebugging"
            android:label="@string/enable_adb_wireless"
            android:icon="@drawable/tile_icon_debugging_wireless"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>

        <service
            android:name=".development.qstile.DevelopmentTiles$ShowTaps"
            android:label="@string/show_touches"
            android:icon="@drawable/tile_icon_show_taps"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                       android:value="true"/>
        </service>

        <service
            android:name=".development.qstile.DevelopmentTiles$DesktopMode"
            android:label="@string/desktop_mode"
            android:icon="@drawable/tile_icon_desktop_mode"
            android:permission="android.permission.BIND_QUICK_SETTINGS_TILE"
            android:exported="true"
            android:enabled="false">
            <intent-filter>
                <action android:name="android.service.quicksettings.action.QS_TILE" />
            </intent-filter>
            <meta-data android:name="android.service.quicksettings.TOGGLEABLE_TILE"
                android:value="true"/>
            <meta-data android:name="com.android.settings.development.qstile.REQUIRES_SYSTEM_PROPERTY"
                       android:value="persist.wm.debug.desktop_mode" />
        </service>

        <activity
            android:name=".HelpTrampoline"
            android:exported="true"
            android:theme="@style/Transparent"
            android:permission="android.permission.DUMP"
            android:excludeFromRecents="true"
            android:enabled="@*android:bool/config_settingsHelpLinksEnabled" />

        <activity android:name=".applications.autofill.AutofillPickerActivity"
                android:excludeFromRecents="true"
                android:launchMode="singleInstance"
                android:exported="false">
        </activity>

        <activity android:name=".applications.autofill.AutofillPickerTrampolineActivity"
                android:theme="@android:style/Theme.NoDisplay"
                android:excludeFromRecents="true"
                android:launchMode="singleInstance"
                android:exported="true"
                android:label="@string/autofill_app">
            <intent-filter android:priority="1">
                <action android:name="android.settings.REQUEST_SET_AUTOFILL_SERVICE" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:scheme="package" />
            </intent-filter>
        </activity>

        <activity android:name="Settings$AdvancedConnectedDeviceActivity"
                  android:exported="true"
                  android:label="@string/connected_device_connections_title">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.ADVANCED_CONNECTED_DEVICE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.connecteddevice.AdvancedConnectedDeviceDashboardFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$NfcSettingsActivity"
                  android:exported="true"
                  android:label="@string/nfc_quick_toggle_title">
            <intent-filter android:priority="1">
                <action android:name="android.settings.NFC_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.connecteddevice.NfcAndPaymentFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$BluetoothDeviceDetailActivity"
                  android:label="@string/device_details_title"
                  android:exported="true"
                  android:permission="android.permission.BLUETOOTH_CONNECT">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.BLUETOOTH_DEVICE_DETAIL_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.bluetooth.BluetoothDeviceDetailsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$BluetoothBroadcastActivity"
            android:exported="true"
            android:theme="@style/Theme.AlertDialog.SimConfirmDialog"
            android:permission="android.permission.BLUETOOTH_CONNECT"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.MEDIA_BROADCAST_DIALOG" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.bluetooth.BluetoothBroadcastDialog" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$BluetoothFindBroadcastsActivity"
            android:label="@string/bluetooth_find_broadcast_title"
            android:exported="true"
            android:permission="android.permission.BLUETOOTH_CONNECT"
            android:configChanges="orientation|keyboardHidden|screenSize">
            <intent-filter android:priority="1">
                <action android:name="android.settings.BLUTOOTH_FIND_BROADCASTS_ACTIVITY" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.bluetooth.BluetoothFindBroadcastsFragment" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <activity
            android:name="Settings$StylusUsiDetailsActivity"
            android:label="@string/stylus_device_details_title"
            android:exported="true">
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.STYLUS_USI_DETAILS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.connecteddevice.stylus.StylusUsiDetailsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_connected_devices"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name=".panel.SettingsPanelActivity"
            android:label="@string/settings_panel_title"
            android:theme="@style/Theme.Panel"
            android:launchMode="singleInstance"
            android:excludeFromRecents="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true">
                 <intent-filter>
                     <action android:name="android.settings.panel.action.INTERNET_CONNECTIVITY" />
                     <category android:name="android.intent.category.DEFAULT" />
                 </intent-filter>
                <intent-filter>
                    <action android:name="android.settings.panel.action.NFC" />
                    <category android:name="android.intent.category.DEFAULT" />
                </intent-filter>
                <intent-filter>
                    <action android:name="android.settings.panel.action.WIFI" />
                    <category android:name="android.intent.category.DEFAULT" />
                </intent-filter>
                <intent-filter>
                    <action android:name="android.settings.panel.action.VOLUME" />
                    <category android:name="android.intent.category.DEFAULT" />
                </intent-filter>
        </activity>

        <activity android:name=".wifi.addappnetworks.AddAppNetworksActivity"
                  android:label="@string/settings_panel_title"
                  android:theme="@style/Theme.Panel"
                  android:launchMode="singleInstance"
                  android:excludeFromRecents="true"
                  android:exported="true"
                  android:configChanges="orientation|keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout">
            <intent-filter>
                <action android:name="android.settings.WIFI_ADD_NETWORKS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <provider android:name=".slices.SettingsSliceProvider"
                  android:authorities="${applicationId}.slices;android.settings.slices"
                  android:exported="true"
                  android:grantUriPermissions="true" />

        <receiver
            android:name=".slices.SliceBroadcastReceiver"
            android:exported="false" />

        <receiver
            android:name=".slices.SliceRelayReceiver"
            android:permission="android.permission.MANAGE_SLICE_PERMISSIONS"
            android:exported="true" />

        <receiver
            android:name=".slices.VolumeSliceRelayReceiver"
            android:permission="android.permission.MANAGE_SLICE_PERMISSIONS"
            android:exported="true" />

        <!-- Couldn't be triggered from outside of settings. Statsd can trigger it because we send
             PendingIntent to it-->
        <receiver android:name=".fuelgauge.batterytip.AnomalyDetectionReceiver"
                  android:exported="false" />

        <receiver android:name=".fuelgauge.batterytip.AnomalyConfigReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.app.action.STATSD_STARTED"/>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <service android:name=".fuelgauge.batterytip.AnomalyCleanupJobService"
                 android:permission="android.permission.BIND_JOB_SERVICE" />

        <service android:name=".fuelgauge.batterytip.AnomalyConfigJobService"
                 android:permission="android.permission.BIND_JOB_SERVICE" />

        <service android:name=".fuelgauge.batterytip.AnomalyDetectionJobService"
                 android:permission="android.permission.BIND_JOB_SERVICE" />

        <provider
            android:name=".homepage.contextualcards.CardContentProvider"
            android:authorities="${applicationId}.homepage.CardContentProvider"
            android:exported="true"
            android:permission="android.permission.WRITE_SETTINGS_HOMEPAGE_DATA" />

        <provider
            android:name=".homepage.contextualcards.SettingsContextualCardProvider"
            android:authorities="${applicationId}.homepage.contextualcards"
            android:permission="android.permission.WRITE_SETTINGS_HOMEPAGE_DATA"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.action.SETTINGS_HOMEPAGE_DATA"/>
            </intent-filter>
        </provider>

        <activity
            android:name=".wifi.dpp.WifiDppConfiguratorActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.settings.WIFI_DPP_CONFIGURATOR_QR_CODE_SCANNER"/>
                <action android:name="android.settings.WIFI_DPP_CONFIGURATOR_QR_CODE_GENERATOR"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.settings.PROCESS_WIFI_EASY_CONNECT_URI"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <data android:scheme="DPP"/>
            </intent-filter>
        </activity>

        <activity
            android:name=".wifi.dpp.WifiDppEnrolleeActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.settings.WIFI_DPP_ENROLLEE_QR_CODE_SCANNER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity android:name=".homepage.contextualcards.ContextualCardFeedbackDialog"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert" />

        <activity android:name=".homepage.contextualcards.FaceReEnrollDialog"
                  android:theme="@android:style/Theme.DeviceDefault.Light.Dialog.Alert" />

        <activity
            android:name="Settings$WifiCallingDisclaimerActivity"
            android:label="@string/wifi_calling_settings_title"
            android:exported="true"
            android:taskAffinity="com.android.settings">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.wifi.calling.WifiCallingDisclaimerFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_network"/>
        </activity>

        <activity android:name="Settings$BatterySaverScheduleSettingsActivity"
            android:exported="true"
            android:label="@string/battery_saver_schedule_settings_title">
            <intent-filter>
                <action android:name="com.android.settings.BATTERY_SAVER_SCHEDULE_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.fuelgauge.batterysaver.BatterySaverScheduleSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_battery"/>
        </activity>

        <activity android:name="Settings$PowerMenuSettingsActivity"
                  android:exported="true"
                  android:label="@string/power_menu_setting_name">
            <intent-filter>
                <action android:name="android.settings.ACTION_POWER_MENU_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.gestures.PowerMenuSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity
            android:name="Settings$GestureNavigationSettingsActivity"
            android:label="@string/gesture_settings_activity_title"
            android:exported="true"
            android:enabled="true">
            <intent-filter android:priority="32">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.GESTURE_NAVIGATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.gestures.GestureNavigationSettingsFragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity
            android:name="Settings$ButtonNavigationSettingsActivity"
            android:label="@string/button_navigation_settings_activity_title"
            android:exported="true"
            android:enabled="true">
            <intent-filter android:priority="32">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="com.android.settings.BUTTON_NAVIGATION_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.gestures.ButtonNavigationSettingsFragment" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <activity android:name="Settings$MediaControlsSettingsActivity"
                  android:exported="true"
                  android:label="@string/media_controls_title">
            <intent-filter>
                <action android:name="android.settings.ACTION_MEDIA_CONTROLS_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.sound.MediaControlsSettings" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_sound"/>
        </activity>

        <!-- SCREENSHOT -->
        <activity
            android:name="Settings$ScreenshotSettingsActivity"
            android:label="@string/screenshot_settings_activity_title"
            android:exported="true"
            android:theme="@style/Theme.SubSettings"
            android:enabled="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.SCREENSHOT_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="60">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.screenshot.ScreenshotSettings" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>

        <receiver
            android:name=".media.BluetoothPairingReceiver"
            android:exported="true"
            android:permission="android.permission.BLUETOOTH_CONNECT">
            <intent-filter>
                <action android:name="com.android.settings.action.LAUNCH_BLUETOOTH_PAIRING"/>
            </intent-filter>
        </receiver>

        <receiver
            android:name=".sim.receivers.SimSlotChangeReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.telephony.action.SIM_SLOT_STATUS_CHANGED" />
            </intent-filter>
        </receiver>

        <service android:name=".sim.receivers.SimSlotChangeService"
                 android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:name=".sim.receivers.SimCompleteBootReceiver"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <activity
            android:name=".sim.ChooseSimActivity"
            android:theme="@style/GlifV3Theme.DayNight.NoActionBar"
            android:launchMode="singleInstance"
            android:excludeFromRecents="true"
            android:exported="false"/>

        <activity
            android:name=".sim.SwitchToEsimConfirmDialogActivity"
            android:exported="false"
            android:permission="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"
            android:launchMode="singleInstance"
            android:theme="@style/Theme.AlertDialog.SimConfirmDialog"/>

        <activity
            android:name=".sim.DsdsDialogActivity"
            android:exported="false"
            android:permission="android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS"
            android:launchMode="singleInstance"
            android:theme="@style/Theme.AlertDialog.SimConfirmDialog"/>

        <service android:name=".sim.SimNotificationService"
                 android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity android:name=".sim.smartForwarding.SmartForwardingActivity"
            android:exported="true"
            android:launchMode="singleTask">
        </activity>

        <activity android:name="Settings$FactoryResetActivity"
                  android:permission="android.permission.MASTER_CLEAR"
                  android:label="@string/main_clear_title"
                  android:exported="true"
                  android:theme="@style/SudThemeGlif.Light">
            <intent-filter>
                <action android:name="com.android.settings.action.FACTORY_RESET"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                       android:value="com.android.settings.MainClear"/>
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                       android:value="@string/menu_key_system"/>
        </activity>

        <activity android:name="Settings$FactoryResetConfirmActivity"
                  android:label="@string/main_clear_confirm_title"
                  android:exported="false"
                  android:theme="@style/SudThemeGlif.Light">
        </activity>

        <activity
            android:name="Settings$OneHandedSettingsActivity"
            android:label="@string/one_handed_title"
            android:exported="true"
            android:enabled="true">
            <intent-filter android:priority="1">
                <action android:name="android.settings.action.ONE_HANDED_SETTINGS" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter android:priority="1">
                <action android:name="android.intent.action.MAIN" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                   android:value="com.android.settings.gestures.OneHandedSettings"/>
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                       android:value="true" />
        </activity>

        <receiver android:name=".safetycenter.SafetySourceBroadcastReceiver"
                  android:exported="true">
            <intent-filter>
                <action android:name="android.safetycenter.action.REFRESH_SAFETY_SOURCES"/>
                <action android:name="android.intent.action.BOOT_COMPLETED"/>
            </intent-filter>
        </receiver>

        <activity
            android:name="com.android.settings.bluetooth.QrCodeScanModeActivity"
            android:permission="android.permission.BLUETOOTH_CONNECT"
            android:exported="true">
            <intent-filter>
                <action android:name="android.settings.BLUETOOTH_LE_AUDIO_QR_CODE_SCANNER"/>
                <category android:name="android.intent.category.DEFAULT"/>
            </intent-filter>
        </activity>

        <activity
            android:name=".spa.SpaActivity"
            android:configChanges="orientation|screenLayout|screenSize|smallestScreenSize"
            android:knownActivityEmbeddingCerts="@array/config_known_host_certs"
            android:exported="false" />
        <activity android:name=".spa.SpaBridgeActivity" android:exported="false"/>
        <activity android:name=".spa.SpaAppBridgeActivity" android:exported="false"/>

        <activity android:name=".Settings$FingerprintSettingsActivityV2"
            android:label="@string/security_settings_fingerprint_preference_title"
            android:exported="false"
            android:icon="@drawable/ic_fingerprint_header">
            <intent-filter>
                <action android:name="android.settings.FINGERPRINT_SETTINGS_V2" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.biometrics.fingerprint2.ui.fragment.FingerprintSettingsV2Fragment" />
            <meta-data android:name="com.android.settings.HIGHLIGHT_MENU_KEY"
                android:value="@string/menu_key_security"/>
        </activity>

        <activity-alias android:name="UsageStatsActivity"
                        android:exported="true"
                        android:label="@string/testing_usage_stats"
                        android:targetActivity=".spa.SpaBridgeActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEVELOPMENT_PREFERENCE" />
            </intent-filter>
            <meta-data android:name="com.android.settings.spa.DESTINATION"
                       android:value="UsageStats"/>
        </activity-alias>

        <!-- [b/197780098] Disable eager initialization of Jetpack libraries. -->
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="${applicationId}.androidx-startup"
            tools:node="remove" />

        <!-- =======================rk code========== -->
        <!-- HDMI -->
        <activity
            android:name="Settings$HdmiSettingsActivity"
            android:label="@string/hdmi_settings"
            android:taskAffinity=""
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.VOICE_LAUNCH" />
                <category android:name="com.android.settings.SHORTCUT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.display.HdmiSettings" />
            <meta-data android:name="com.android.settings.PRIMARY_PROFILE_CONTROLLED"
                android:value="true" />
        </activity>
        <activity android:name="com.android.settings.display.ScreenScaleActivity"/>

        <activity
            android:name=".Settings$RamExtensionActivity"
            android:label="@string/ram_extension"
            android:icon="@drawable/ic_storage_white"
            android:exported="true">
            <intent-filter>
                <action android:name="com.android.settings.action.RAM_ESETTINGS"/>
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="com.android.settings.FRAGMENT_CLASS"
                android:value="com.android.settings.ramextension.RamExtensionFragment"/>
        </activity>
        <!-- ======================================== -->

        <!-- This is the longest AndroidManifest.xml ever. -->
    </application>
</manifest>
