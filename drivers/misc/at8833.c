#include <linux/gpio/consumer.h>
#include <linux/gpio.h>
#include <linux/of_gpio.h>
#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>

#include <linux/platform_device.h>
#include <linux/of_platform.h>
#include <linux/fb.h>
#include <linux/backlight.h>
#include <linux/err.h>
#include <linux/pwm.h>
#include <linux/pwm_backlight.h>
#include <linux/regulator/consumer.h>
#include <linux/slab.h>

#include <linux/iio/iio.h>
#include <linux/iio/machine.h>
#include <linux/iio/driver.h>
#include <linux/iio/consumer.h>

#include <linux/sched.h>

#include <linux/interrupt.h>
#include <linux/irq.h>
#include "../input/touchscreen/tp_suspend.h"

#include <linux/timer.h>
#include <linux/jiffies.h>
#include <linux/input.h>
#include <linux/gpio_keys.h>
#include <linux/miscdevice.h>
#include <linux/delay.h>
#include <linux/device.h>

struct at8833_drvdata {
	struct miscdevice misc;
	int ain1;
	int ain2;
	int bin1;
	int bin2;
	int sleep_en;
	int motor_en;
	int pi_det_1;
	int pi_det_2;
};

int corote_count = 1;
int reversal_count = 1;
int step_number = 0;	//0-1839

#define STEP_VALUE_ID 10
#define STEP_HIGH_VALUE_ID 11
#define STEP_VALUE_LEN 1
#define STEP_VALUE_LEN_2 2
#define STEP_COUNT_MAX 1840

extern int emmc_vendor_read(u32 id, void *pbuf, u32 size);
extern int emmc_vendor_write(u32 id, void *pbuf, u32 size);

struct at8833_drvdata* g_at8833=NULL;

#define AT8833_IOCTL_COROTATION 		_IO('a',0x1)
#define AT8833_IOCTL_REVERSAL   		_IO('a',0x2)

void at8833_corotation(void)
{
	switch(corote_count)
	{
		case 1:
		gpio_set_value(g_at8833->ain1, 1);
		gpio_set_value(g_at8833->ain2, 0);
		gpio_set_value(g_at8833->bin1, 0);
		gpio_set_value(g_at8833->bin2, 1);
		break;
		case 2:
		gpio_set_value(g_at8833->ain1, 1);
		gpio_set_value(g_at8833->ain2, 0);
		gpio_set_value(g_at8833->bin1, 1);
		gpio_set_value(g_at8833->bin2, 0);
		break;
		case 3:
		gpio_set_value(g_at8833->ain1, 0);
		gpio_set_value(g_at8833->ain2, 1);
		gpio_set_value(g_at8833->bin1, 1);
		gpio_set_value(g_at8833->bin2, 0);
		break;
		case 4:
		gpio_set_value(g_at8833->ain1, 0);
		gpio_set_value(g_at8833->ain2, 1);
		gpio_set_value(g_at8833->bin1, 0);
		gpio_set_value(g_at8833->bin2, 1);
		break;
		default:
		break;					
	}

	usleep_range(2500,2500);
	corote_count++;
	if(corote_count > 4)
	{
			corote_count = 1;
	}

	if((step_number > 0)&&(step_number < STEP_COUNT_MAX))
	{
		step_number --;
	}
}

void at8833_reversal(void)
{
	switch(reversal_count)
	{
		case 1:
		gpio_set_value(g_at8833->ain1, 0);
		gpio_set_value(g_at8833->ain2, 1);
		gpio_set_value(g_at8833->bin1, 0);
		gpio_set_value(g_at8833->bin2, 1);
		break;
		case 2:
		gpio_set_value(g_at8833->ain1, 0);
		gpio_set_value(g_at8833->ain2, 1);
		gpio_set_value(g_at8833->bin1, 1);
		gpio_set_value(g_at8833->bin2, 0);
		break;
		case 3:
		gpio_set_value(g_at8833->ain1, 1);
		gpio_set_value(g_at8833->ain2, 0);
		gpio_set_value(g_at8833->bin1, 1);
		gpio_set_value(g_at8833->bin2, 0);
		break;
		case 4:
		gpio_set_value(g_at8833->ain1, 1);
		gpio_set_value(g_at8833->ain2, 0);
		gpio_set_value(g_at8833->bin1, 0);
		gpio_set_value(g_at8833->bin2, 1);
		break;
		default:
		break;					
	}

	usleep_range(2500,2500);
	reversal_count++;
	if(reversal_count > 4)
	{
			reversal_count = 1;
	}	

	if((step_number >= 0)&&(step_number < STEP_COUNT_MAX))
	{
		step_number ++;
	}
}

void at8833_stop(void)
{
	gpio_set_value(g_at8833->ain1, 0);
	gpio_set_value(g_at8833->ain2, 0);
	gpio_set_value(g_at8833->bin1, 0);
	gpio_set_value(g_at8833->bin2, 0);
}

static long at8833_ioctl(struct file *file, unsigned int cmd, unsigned long arg)
{
	char corote_to_return[] = "corote over";
	char reversal_to_return[] = "reversal over";
	int ret;
		
	pr_debug("at8833_ioctl : cmd = %u\n", cmd);
	switch (cmd) {
		case AT8833_IOCTL_COROTATION:
				if(gpio_get_value(g_at8833->pi_det_2))
				{	
					at8833_corotation();
					at8833_stop();
				}
				else {
					ret = copy_to_user((void __user *)arg, corote_to_return, sizeof(corote_to_return));	
          if (ret) {
              return -EFAULT;
          }
				}			
			break;
		case AT8833_IOCTL_REVERSAL:
				if(gpio_get_value(g_at8833->pi_det_1))
				{	
					at8833_reversal();
					at8833_stop();
				}
				else {
					ret = copy_to_user((void __user *)arg, reversal_to_return, sizeof(reversal_to_return));	
          if (ret) {
              return -EFAULT;
          }
				}
			break;

		default:
			return -EINVAL;

	}
	return 0;
}

static ssize_t at8833_read(struct file *fp, char __user *user_buf, size_t count, loff_t *ppos)
{
	char kbuf[10];
	int ret,wrinten;
	int value;

  if (*ppos)
	{  	
  	return 0;	
	}
	
	if(gpio_get_value(g_at8833->pi_det_1) && gpio_get_value(g_at8833->pi_det_2))
	{
		value = 0;
	}	
	else
	{
		value = 1;
	}	
	
	wrinten = sprintf(kbuf, "%d\n", value);

  if (clear_user(user_buf, count)) {
      printk(KERN_ERR "clear error\n");
      return -EIO;
  }
	
	ret = copy_to_user(user_buf, kbuf, wrinten);
	if (ret != 0) {
	    printk(KERN_ERR "read error");
	    return -EIO;
	}
	*ppos += wrinten;
	
	return wrinten;	
}

static ssize_t at8833_write(struct file *file,
			const char __user *buf, size_t n, loff_t *offset)
{
	int argc = 0, i;	
	char tmp[64];
	char *argv[16];
	char *cmd, *data;
	unsigned int cycle_cnt = 0;
		
	memset(tmp, 0, sizeof(tmp));
	if (copy_from_user(tmp, buf, n))
		return -EFAULT;
	cmd = tmp;
	data = tmp;

	while (data < (tmp + n)) {
		data = strstr(data, " ");
		if (!data)
			break;
		*data = 0;
		argv[argc] = ++data;
		argc++;
		if (argc >= 16)
			break;
	}

	tmp[n - 1] = 0;

	if (!strcmp(cmd, "left")) {
			sscanf(argv[0], "%d", &cycle_cnt);
			
			if(gpio_get_value(g_at8833->pi_det_2))
			{
				for(i=0;i<cycle_cnt;i++)
				{	
					at8833_corotation();
					at8833_stop();	
				}
			}
	} else if (!strcmp(cmd, "right")){
			sscanf(argv[0], "%d", &cycle_cnt);
			if(gpio_get_value(g_at8833->pi_det_1))
			{	
				for(i=0;i<cycle_cnt;i++)
				{	
					at8833_reversal();
					at8833_stop();
				}
			}
	} else if (!strcmp(cmd, "stop")){
		  at8833_stop();
	}	else {
		at8833_stop();		
		printk("at8833 write not ok\n");
	}
	return n;	
}

#ifdef CONFIG_COMPAT
static long at8833_compat_ioctl(struct file *file, unsigned int cmd, unsigned long arg)
{
	return at8833_ioctl(file, cmd, (unsigned long)compat_ptr(arg));
}
#endif

static const struct file_operations at8833_fops = {
	.owner 			= THIS_MODULE,
	.read 			= at8833_read,	
	.write 			= at8833_write,
	.unlocked_ioctl		= at8833_ioctl,
#ifdef CONFIG_COMPAT
	.compat_ioctl		= at8833_compat_ioctl,
#endif
};

static struct miscdevice at8833_misc = {
	.minor = MISC_DYNAMIC_MINOR,
	.name = "at8833",
	.fops = &at8833_fops,
};

static ssize_t show_at8833_step(struct device *dev,
    struct device_attribute *attr, char *buf)
{
    int value;
    int ret;

    value = step_number;
    ret = sprintf(buf, "%d\n", value);
    return ret;
}

static ssize_t store_at8833_step(struct device *dev, struct device_attribute *attr, const char *buf, size_t count)
{
    int err, w, i;
		int cycle_count;
		u8 look[2] = {0x00,0x00};
		u8 look_1[2] = {0x00,0x00};
		u8 high_step,low_step;

    err = kstrtoint(buf, 10, &w);
    if(err)
        return err;
    if(w <= STEP_COUNT_MAX)
    {
	    if(w > step_number)
	    {
					cycle_count = w - step_number;
					for(i=0;i<cycle_count;i++)
					{
						at8833_reversal();
						//at8833_corotation();
						at8833_stop();
					}
	    }
	    else
	    {
					cycle_count = step_number - w;
					for(i=0;i<cycle_count;i++)
					{
						//at8833_reversal();
						at8833_corotation();
						at8833_stop();
					}
	    }
			step_number = w;
			high_step = (step_number >> 8) & 0xff;
			low_step = step_number & 0xff;
			//look[0] = low_step;
			look[1] = low_step;
			look_1[1] = high_step;
			printk("dsw test high_step = %d low_step = %d w = %d \n",high_step,low_step,w);
			emmc_vendor_write((u32)STEP_VALUE_ID, (void *)&look[1], (u32)STEP_VALUE_LEN);
			emmc_vendor_write((u32)STEP_HIGH_VALUE_ID, (void *)&look_1[1], (u32)STEP_VALUE_LEN);
		}
    return count;
}
static DEVICE_ATTR(at8833_step, S_IRUGO | S_IWUSR, show_at8833_step, store_at8833_step);

static ssize_t store_at8833_calibration(struct device *dev, struct device_attribute *attr, const char *buf, size_t count)
{
    int err, w;

    err = kstrtoint(buf, 10, &w);
    if(err)
        return err;

    if(w > 0)
    {
			while(gpio_get_value(g_at8833->pi_det_2))
			{
					at8833_corotation();
					//at8833_reversal();
					at8833_stop();
			}

			if(0 == gpio_get_value(g_at8833->pi_det_2))
			{
				step_number = 0;
			}
		}
    return count;
}

static DEVICE_ATTR(at8833_calibration, S_IRUGO | S_IWUSR, NULL, store_at8833_calibration);

static struct attribute *at8833_attributes[] = {
		&dev_attr_at8833_step.attr,
		&dev_attr_at8833_calibration.attr,
		NULL
};

static struct attribute_group at8833_attribute_group = {
		.attrs = at8833_attributes,
};

static int at8833_parse_dt(struct device *dev, struct at8833_drvdata *drvdata)
{
		int ret;
		int flags;

    drvdata->ain1 = of_get_named_gpio_flags(dev->of_node, "ain1", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->ain1)) {
        dev_err(dev, "error requesting ain1 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->ain1, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:ain1 request err\n");
            return ret;
        }
    }

    drvdata->ain2 = of_get_named_gpio_flags(dev->of_node, "ain2", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->ain2)) {
        dev_err(dev, "error requesting ain2 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev,drvdata-> ain2, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:ain2 request err\n");
            return ret;
        }
    }

    drvdata->bin1 = of_get_named_gpio_flags(dev->of_node, "bin1", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->bin1)) {
        dev_err(dev, "error requesting bin1 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->bin1, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:bin1 request err\n");
            return ret;
        }
    }

    drvdata->bin2 = of_get_named_gpio_flags(dev->of_node, "bin2", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->bin2)) {
        dev_err(dev, "error requesting bin2 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->bin2, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:bin2 request err\n");
            return ret;
        }
    }

    drvdata->sleep_en = of_get_named_gpio_flags(dev->of_node, "sleep_en", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->sleep_en)) {
        dev_err(dev, "error requesting sleep_en !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->sleep_en, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:sleep_en request err\n");
            return ret;
        }
    }

    drvdata->motor_en = of_get_named_gpio_flags(dev->of_node, "motor_en", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->motor_en)) {
        dev_err(dev, "error requesting motor_en !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->motor_en, GPIOF_DIR_OUT|GPIOF_INIT_LOW, NULL);
        if(ret){
            printk("[at8833]:motor_en request err\n");
            return ret;
        }
    }
    
    drvdata->pi_det_1 = of_get_named_gpio_flags(dev->of_node, "pi_det_1", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->pi_det_1)) {
        dev_err(dev, "error requesting pi_det_1 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->pi_det_1, GPIOF_DIR_IN, NULL);
        if(ret){
            printk("[at8833]:pi_det_1 request err\n");
            return ret;
        }
    }

    drvdata->pi_det_2 = of_get_named_gpio_flags(dev->of_node, "pi_det_2", 0, (enum of_gpio_flags *)&flags);
    if (!gpio_is_valid(drvdata->pi_det_2)) {
        dev_err(dev, "error requesting pi_det_2 !!!\n");
        return -EINVAL;
    } else {
        ret = devm_gpio_request_one(dev, drvdata->pi_det_2, GPIOF_DIR_IN, NULL);
        if(ret){
            printk("[at8833]:pi_det_2 request err\n");
            return ret;
        }
    }
    
    //printk("dsw test at8833_parse_dt is ok \n");
    return 0;
}

static int at8833_probe(struct platform_device *pdev)
{
	struct device *dev = &pdev->dev;
	struct at8833_drvdata *drvdata;
  u8 read_look[STEP_VALUE_LEN_2 + 1] = {0x00, 0x00, 0x00};
  u8 read_look_1[STEP_VALUE_LEN_2 + 1] = {0x00, 0x00, 0x00};
  int read_len = 0;
  int ret;

	if (!pdev)
		return -ENOMEM;
	
	drvdata = devm_kzalloc(dev, sizeof(*drvdata), GFP_KERNEL);
	if (!drvdata)
		return -ENOMEM;
	
	at8833_parse_dt(dev, drvdata);
	
	gpio_set_value(drvdata->motor_en, 1);
	gpio_set_value(drvdata->sleep_en, 1);
	msleep(10);

	misc_register(&at8833_misc);
	
//	drvdata->misc.minor = MISC_DYNAMIC_MINOR;
//	drvdata->misc.name = "at8833";
//	drvdata->misc.fops = &at8833_fops;

	ret = sysfs_create_group(&pdev->dev.kobj, &at8833_attribute_group);
	g_at8833=drvdata;

	read_len = emmc_vendor_read((u32)STEP_VALUE_ID, (void *)read_look, (u32)STEP_VALUE_LEN_2);
	//printk("dsw test read_look[0]=%d read_look{1} = %d read_look{2} = %d\n",read_look[0],read_look[1],read_look[2]);

	read_len = emmc_vendor_read((u32)STEP_HIGH_VALUE_ID, (void *)read_look_1, (u32)STEP_VALUE_LEN_2);
	//printk("dsw test read_look_1[0]=%d read_look_1{1} = %d read_look_1{2} = %d\n",read_look_1[0],read_look_1[1],read_look_1[2]);

	step_number = read_look[0] | ((read_look_1[0])<<8);
	printk("dsw test step_number = %d \n",step_number);
	return 0;
}

static int at8833_remove(struct platform_device *pdev)
{
	return 0;
}

#ifdef CONFIG_PM_SLEEP
static int at8833_suspend(struct device *dev)
{
	return 0;
}

static int at8833_resume(struct device *dev)
{
	return 0;
}
#endif

static const struct dev_pm_ops at8833_pm_ops = {
#ifdef CONFIG_PM_SLEEP
    .suspend = at8833_suspend,
    .resume = at8833_resume,
#endif
};

static struct of_device_id at8833_of_match[] = {
    { .compatible = "at8833" },
    { }
};

MODULE_DEVICE_TABLE(of, at8833_of_match);

static struct platform_driver at8833_driver = {
    .driver	    = {
        .name	    = "at8833",
        .pm	    = &at8833_pm_ops,
        .of_match_table	= of_match_ptr(at8833_of_match),
    },
    .probe	    = at8833_probe,
    .remove	    = at8833_remove,
};

static int __init at8833_driver_init(void)
{
	return platform_driver_register(&at8833_driver);
}

static void __exit at8833_driver_exit(void)
{
	platform_driver_unregister(&at8833_driver);
}

late_initcall_sync(at8833_driver_init);
module_exit(at8833_driver_exit);

MODULE_AUTHOR("dushiwang<<EMAIL>>");
MODULE_DESCRIPTION("at8833 contral Driver");
MODULE_LICENSE("GPL");
MODULE_ALIAS("platform:at8833 contral");